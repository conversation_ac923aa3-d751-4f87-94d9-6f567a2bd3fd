'use client'

import { useState, useEffect } from 'react'
import { X, Download, Smartphone } from 'lucide-react'

export default function PWAInstallBanner() {
  const [showBanner, setShowBanner] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setShowBanner(true)
    }

    const handleAppInstalled = () => {
      setShowBanner(false)
      setDeferredPrompt(null)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    // Check if app is already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setShowBanner(false)
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt')
      } else {
        console.log('User dismissed the install prompt')
      }
      
      setDeferredPrompt(null)
      setShowBanner(false)
    }
  }

  const handleDismiss = () => {
    setShowBanner(false)
    // Remember user dismissed the banner
    localStorage.setItem('pwa-install-dismissed', 'true')
  }

  // Don't show if user previously dismissed
  useEffect(() => {
    const dismissed = localStorage.getItem('pwa-install-dismissed')
    if (dismissed === 'true') {
      setShowBanner(false)
    }
  }, [])

  if (!showBanner) return null

  return (
    <div 
      id="pwa-install-banner"
      className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card"
    >
      <div className="flex items-start gap-3">
        <div className="bg-white bg-opacity-20 p-2 rounded-lg">
          <Smartphone className="h-5 w-5" />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-sm md:text-base mb-1">
            تثبيت التطبيق
          </h3>
          <p className="text-blue-100 text-xs md:text-sm mb-3">
            ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت
          </p>
          <div className="flex gap-2">
            <button
              onClick={handleInstallClick}
              className="bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1"
            >
              <Download className="h-3 w-3 md:h-4 md:w-4" />
              تثبيت
            </button>
            <button
              onClick={handleDismiss}
              className="text-blue-100 hover:text-white transition-colors"
            >
              <X className="h-4 w-4 md:h-5 md:w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
