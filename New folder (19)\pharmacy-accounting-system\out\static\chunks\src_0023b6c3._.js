(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/Sidebar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Sidebar,
    "useSidebar": ()=>useSidebar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-client] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-cart.js [app-client] (ecmascript) <export default as ShoppingCart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-check.js [app-client] (ecmascript) <export default as UserCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript) <export default as RotateCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pill$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pill$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pill.js [app-client] (ecmascript) <export default as Pill>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wallet.js [app-client] (ecmascript) <export default as Wallet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-client] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wrench.js [app-client] (ecmascript) <export default as Wrench>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/printer.js [app-client] (ecmascript) <export default as Printer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bug.js [app-client] (ecmascript) <export default as Bug>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const getMenuItems = (permissions)=>[
        {
            title: 'الرئيسية',
            href: '/',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"]
        },
        {
            title: 'إدارة المخزون',
            href: '/inventory',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
            permission: 'inventory_view'
        },
        {
            title: 'المبيعات',
            href: '/sales',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__["ShoppingCart"],
            permission: 'sales_view'
        },
        {
            title: 'المشتريات',
            href: '/purchases',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pill$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pill$3e$__["Pill"],
            permission: 'purchases_view'
        },
        {
            title: 'العملاء',
            href: '/customers',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            permission: 'customers_view'
        },
        {
            title: 'الموردين',
            href: '/suppliers',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__UserCheck$3e$__["UserCheck"],
            permission: 'suppliers_view'
        },
        {
            title: 'المرتجعات',
            href: '/returns',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"],
            permission: 'returns_view'
        },
        {
            title: 'الصندوق',
            href: '/cashbox',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wallet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wallet$3e$__["Wallet"],
            permission: 'cashbox_view'
        },
        {
            title: 'التقارير',
            href: '/reports',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            permission: 'reports_view'
        },
        {
            title: 'إدارة المستخدمين',
            href: '/users',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"],
            permission: 'users_view'
        },
        {
            title: 'سجل النشاطات',
            href: '/activity-log',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"],
            permission: 'users_view'
        },
        {
            title: 'التنبيهات',
            href: '/notifications',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"]
        },
        {
            title: 'الإعدادات',
            href: '/settings',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"],
            permission: 'settings_view'
        },
        {
            title: 'إصلاح البيانات',
            href: '/fix-data',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"],
            permission: 'settings_view'
        },
        {
            title: 'اختبار الطباعة',
            href: '/test-print',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"],
            permission: 'sales_view'
        },
        {
            title: 'تشخيص البيانات',
            href: '/debug-data',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bug$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bug$3e$__["Bug"],
            permission: 'settings_view'
        },
        {
            title: 'اختبار المبيعات',
            href: '/debug-sales',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__["ShoppingCart"],
            permission: 'sales_view'
        }
    ];
function Sidebar() {
    let { isOpen = false, onClose } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { hasPermission, permissions } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePermissions"])();
    const menuItems = getMenuItems(permissions);
    // إغلاق القائمة عند تغيير الصفحة
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Sidebar.useEffect": ()=>{
            if (onClose) {
                onClose();
            }
        }
    }["Sidebar.useEffect"], [
        pathname,
        onClose
    ]);
    // إغلاق القائمة عند النقر خارجها
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Sidebar.useEffect": ()=>{
            const handleClickOutside = {
                "Sidebar.useEffect.handleClickOutside": (event)=>{
                    const sidebar = document.getElementById('mobile-sidebar');
                    const menuButton = document.getElementById('mobile-menu-button');
                    if (sidebar && !sidebar.contains(event.target) && menuButton && !menuButton.contains(event.target)) {
                        if (onClose) {
                            onClose();
                        }
                    }
                }
            }["Sidebar.useEffect.handleClickOutside"];
            if (isOpen) {
                document.addEventListener('mousedown', handleClickOutside);
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = 'unset';
            }
            return ({
                "Sidebar.useEffect": ()=>{
                    document.removeEventListener('mousedown', handleClickOutside);
                    document.body.style.overflow = 'unset';
                }
            })["Sidebar.useEffect"];
        }
    }["Sidebar.useEffect"], [
        isOpen,
        onClose
    ]);
    // تصفية العناصر بناءً على الصلاحيات
    const visibleMenuItems = menuItems.filter((item)=>{
        if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)
        ;
        return hasPermission(item.permission);
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden",
                onClick: onClose
            }, void 0, false, {
                fileName: "[project]/src/components/Sidebar.tsx",
                lineNumber: 198,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:right-0 md:z-30",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col flex-grow bg-gradient-to-b from-white to-gray-50 shadow-xl border-l border-gray-200",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3 p-6 bg-gradient-to-r from-blue-600 to-indigo-700 text-white",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white bg-opacity-20 p-2 rounded-lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pill$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pill$3e$__["Pill"], {
                                        className: "h-6 w-6 text-white"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Sidebar.tsx",
                                        lineNumber: 209,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 208,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "text-lg font-bold",
                                            children: "نظام الصيدلية"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 212,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-blue-100",
                                            children: "مكتب لارين العلمي"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 213,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 211,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 207,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "flex-1 px-4 py-6 space-y-2 overflow-y-auto",
                            children: visibleMenuItems.map((item)=>{
                                const Icon = item.icon;
                                const isActive = pathname === item.href;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: item.href,
                                    className: "flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group ".concat(isActive ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800'),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                            className: "h-5 w-5 ".concat(isActive ? 'text-white' : '')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 232,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-medium",
                                            children: item.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 233,
                                            columnNumber: 19
                                        }, this),
                                        isActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mr-auto w-2 h-2 bg-white rounded-full animate-pulse"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 235,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, item.href, true, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 223,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 217,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center gap-2 mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Sidebar.tsx",
                                                lineNumber: 245,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-600 font-medium",
                                                children: "متصل"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Sidebar.tsx",
                                                lineNumber: 246,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Sidebar.tsx",
                                        lineNumber: 244,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-500",
                                        children: "© 2024 مكتب لارين العلمي"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Sidebar.tsx",
                                        lineNumber: 248,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Sidebar.tsx",
                                lineNumber: 243,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 242,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Sidebar.tsx",
                    lineNumber: 206,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Sidebar.tsx",
                lineNumber: 205,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                id: "mobile-sidebar",
                className: "md:hidden",
                style: {
                    position: 'fixed',
                    top: 0,
                    right: isOpen ? '0px' : '-300px',
                    height: '100vh',
                    width: '280px',
                    backgroundColor: 'white',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    borderLeft: '1px solid #e5e7eb',
                    transition: 'right 0.3s ease-in-out',
                    zIndex: 9999,
                    overflow: 'auto'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        padding: '24px',
                        height: '100%',
                        overflowY: 'auto'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                padding: '16px',
                                background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
                                borderRadius: '12px',
                                color: 'white',
                                marginBottom: '24px',
                                textAlign: 'center'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    style: {
                                        fontSize: '18px',
                                        fontWeight: 'bold',
                                        margin: 0
                                    },
                                    children: "نظام الصيدلية"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 282,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    style: {
                                        fontSize: '14px',
                                        margin: '8px 0 0 0',
                                        opacity: 0.9
                                    },
                                    children: "مكتب لارين العلمي"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 283,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 274,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                marginBottom: '24px'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    style: {
                                        margin: '0 0 16px 0',
                                        fontSize: '16px',
                                        fontWeight: 'bold',
                                        color: '#374151',
                                        borderBottom: '2px solid #e5e7eb',
                                        paddingBottom: '8px'
                                    },
                                    children: "القائمة الرئيسية"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 288,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px'
                                    },
                                    children: visibleMenuItems.map((item)=>{
                                        const Icon = item.icon;
                                        const isActive = pathname === item.href;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: item.href,
                                            onClick: onClose,
                                            style: {
                                                padding: '12px 16px',
                                                backgroundColor: isActive ? '#3b82f6' : '#f8fafc',
                                                color: isActive ? 'white' : '#374151',
                                                textDecoration: 'none',
                                                borderRadius: '8px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '12px',
                                                border: isActive ? 'none' : '1px solid #e5e7eb',
                                                transition: 'all 0.2s ease',
                                                fontSize: '14px',
                                                fontWeight: isActive ? 'bold' : 'normal'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                    style: {
                                                        width: '20px',
                                                        height: '20px'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/Sidebar.tsx",
                                                    lineNumber: 322,
                                                    columnNumber: 21
                                                }, this),
                                                item.title
                                            ]
                                        }, item.href, true, {
                                            fileName: "[project]/src/components/Sidebar.tsx",
                                            lineNumber: 303,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Sidebar.tsx",
                                    lineNumber: 297,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 287,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                marginTop: 'auto',
                                paddingTop: '16px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: onClose,
                                style: {
                                    width: '100%',
                                    padding: '12px',
                                    backgroundColor: '#ef4444',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    cursor: 'pointer',
                                    transition: 'background-color 0.2s ease'
                                },
                                onMouseOver: (e)=>e.target.style.backgroundColor = '#dc2626',
                                onMouseOut: (e)=>e.target.style.backgroundColor = '#ef4444',
                                children: "إغلاق القائمة"
                            }, void 0, false, {
                                fileName: "[project]/src/components/Sidebar.tsx",
                                lineNumber: 332,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/Sidebar.tsx",
                            lineNumber: 331,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Sidebar.tsx",
                    lineNumber: 272,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Sidebar.tsx",
                lineNumber: 255,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    zIndex: 9998
                },
                onClick: onClose
            }, void 0, false, {
                fileName: "[project]/src/components/Sidebar.tsx",
                lineNumber: 358,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
}
_s(Sidebar, "WdRph+NsG1Vo9f7Rm0XgbYUtBBM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePermissions"]
    ];
});
_c = Sidebar;
const useSidebar = ()=>{
    _s1();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    return {
        isOpen,
        setIsOpen,
        toggleSidebar: ()=>setIsOpen(!isOpen)
    };
};
_s1(useSidebar, "+sus0Lb0ewKHdwiUhiTAJFoFyQ0=");
var _c;
__turbopack_context__.k.register(_c, "Sidebar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/NotificationDropdown.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>NotificationDropdown
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check-check.js [app-client] (ecmascript) <export default as CheckCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-client] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-client] (ecmascript) <export default as XCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-cart.js [app-client] (ecmascript) <export default as ShoppingCart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-client] (ecmascript) <export default as DollarSign>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function NotificationDropdown() {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('all');
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { notifications, unreadCount, markAsRead, markAllAsRead, removeNotification, clearAll } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    // إغلاق القائمة عند النقر خارجها
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotificationDropdown.useEffect": ()=>{
            const handleClickOutside = {
                "NotificationDropdown.useEffect.handleClickOutside": (event)=>{
                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                        setIsOpen(false);
                    }
                }
            }["NotificationDropdown.useEffect.handleClickOutside"];
            document.addEventListener('mousedown', handleClickOutside);
            return ({
                "NotificationDropdown.useEffect": ()=>document.removeEventListener('mousedown', handleClickOutside)
            })["NotificationDropdown.useEffect"];
        }
    }["NotificationDropdown.useEffect"], []);
    const filteredNotifications = activeTab === 'unread' ? notifications.filter((n)=>!n.isRead) : notifications;
    const getNotificationIcon = (type, category)=>{
        if (type === 'error') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
            className: "h-4 w-4 text-red-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 57,
            columnNumber: 34
        }, this);
        if (type === 'warning') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
            className: "h-4 w-4 text-yellow-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 58,
            columnNumber: 36
        }, this);
        if (type === 'success') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
            className: "h-4 w-4 text-green-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 59,
            columnNumber: 36
        }, this);
        if (type === 'info') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
            className: "h-4 w-4 text-blue-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 60,
            columnNumber: 33
        }, this);
        // أيقونات حسب الفئة
        if (category === 'inventory') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
            className: "h-4 w-4 text-purple-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 63,
            columnNumber: 42
        }, this);
        if (category === 'sales') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__["ShoppingCart"], {
            className: "h-4 w-4 text-green-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 64,
            columnNumber: 38
        }, this);
        if (category === 'financial') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
            className: "h-4 w-4 text-yellow-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 65,
            columnNumber: 42
        }, this);
        if (category === 'user') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
            className: "h-4 w-4 text-blue-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 66,
            columnNumber: 37
        }, this);
        if (category === 'system') return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
            className: "h-4 w-4 text-gray-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 67,
            columnNumber: 39
        }, this);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
            className: "h-4 w-4 text-gray-500"
        }, void 0, false, {
            fileName: "[project]/src/components/NotificationDropdown.tsx",
            lineNumber: 69,
            columnNumber: 12
        }, this);
    };
    const getPriorityColor = (priority)=>{
        switch(priority){
            case 'critical':
                return 'border-r-4 border-red-500 bg-red-50';
            case 'high':
                return 'border-r-4 border-orange-500 bg-orange-50';
            case 'medium':
                return 'border-r-4 border-yellow-500 bg-yellow-50';
            case 'low':
                return 'border-r-4 border-blue-500 bg-blue-50';
            default:
                return 'border-r-4 border-gray-500 bg-gray-50';
        }
    };
    const formatTimeAgo = (dateString)=>{
        const now = new Date();
        const date = new Date(dateString);
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return "منذ ".concat(diffInMinutes, " دقيقة");
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return "منذ ".concat(diffInHours, " ساعة");
        const diffInDays = Math.floor(diffInHours / 24);
        return "منذ ".concat(diffInDays, " يوم");
    };
    const handleNotificationClick = (notification)=>{
        if (!notification.isRead) {
            markAsRead(notification.id);
        }
        if (notification.actionUrl) {
            router.push(notification.actionUrl);
            setIsOpen(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        ref: dropdownRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsOpen(!isOpen),
                className: "relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                        className: "h-5 w-5"
                    }, void 0, false, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 115,
                        columnNumber: 9
                    }, this),
                    unreadCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse",
                        children: unreadCount > 99 ? '99+' : unreadCount
                    }, void 0, false, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 117,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/NotificationDropdown.tsx",
                lineNumber: 111,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 border-b border-gray-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold text-gray-900",
                                        children: "التنبيهات"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 129,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setIsOpen(false),
                                        className: "text-gray-400 hover:text-gray-600",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-5 w-5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                                            lineNumber: 134,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 130,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                lineNumber: 128,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex space-x-2 space-x-reverse",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setActiveTab('all'),
                                        className: "px-3 py-1 rounded-full text-sm font-medium transition-colors ".concat(activeTab === 'all' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'),
                                        children: [
                                            "الكل (",
                                            notifications.length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 140,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setActiveTab('unread'),
                                        className: "px-3 py-1 rounded-full text-sm font-medium transition-colors ".concat(activeTab === 'unread' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'),
                                        children: [
                                            "غير مقروءة (",
                                            unreadCount,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 150,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                lineNumber: 139,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, this),
                    notifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 border-b border-gray-100 bg-gray-50",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex space-x-2 space-x-reverse",
                                    children: unreadCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: markAllAsRead,
                                        className: "flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"], {
                                                className: "h-3 w-3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                lineNumber: 173,
                                                columnNumber: 23
                                            }, this),
                                            "تحديد الكل كمقروء"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 169,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationDropdown.tsx",
                                    lineNumber: 167,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: clearAll,
                                    className: "flex items-center gap-1 text-xs text-red-600 hover:text-red-800",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                            className: "h-3 w-3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                                            lineNumber: 182,
                                            columnNumber: 19
                                        }, this),
                                        "مسح الكل"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/NotificationDropdown.tsx",
                                    lineNumber: 178,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                            lineNumber: 166,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 165,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-h-96 overflow-y-auto",
                        children: filteredNotifications.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-8 text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                    className: "h-12 w-12 text-gray-300 mx-auto mb-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationDropdown.tsx",
                                    lineNumber: 193,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-500",
                                    children: activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationDropdown.tsx",
                                    lineNumber: 194,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                            lineNumber: 192,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "divide-y divide-gray-100",
                            children: filteredNotifications.map((notification)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 hover:bg-gray-50 transition-colors cursor-pointer ".concat(!notification.isRead ? getPriorityColor(notification.priority) : ''),
                                    onClick: ()=>handleNotificationClick(notification),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-start gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-shrink-0 mt-1",
                                                children: getNotificationIcon(notification.type, notification.category)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                lineNumber: 210,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-1 min-w-0",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-start justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex-1",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                        className: "text-sm font-medium ".concat(!notification.isRead ? 'text-gray-900' : 'text-gray-700'),
                                                                        children: notification.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                        lineNumber: 218,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-sm text-gray-600 mt-1 line-clamp-2",
                                                                        children: notification.message
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                        lineNumber: 223,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    notification.actionUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        className: "inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                                                                className: "h-3 w-3"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                                lineNumber: 230,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            notification.actionLabel || 'عرض التفاصيل'
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                        lineNumber: 229,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                lineNumber: 217,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center gap-1 mr-2",
                                                                children: [
                                                                    !notification.isRead && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: (e)=>{
                                                                            e.stopPropagation();
                                                                            markAsRead(notification.id);
                                                                        },
                                                                        className: "p-1 text-gray-400 hover:text-blue-600",
                                                                        title: "تحديد كمقروء",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                                                            className: "h-3 w-3"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                            lineNumber: 247,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                        lineNumber: 239,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: (e)=>{
                                                                            e.stopPropagation();
                                                                            removeNotification(notification.id);
                                                                        },
                                                                        className: "p-1 text-gray-400 hover:text-red-600",
                                                                        title: "حذف",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                                            className: "h-3 w-3"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                            lineNumber: 258,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                        lineNumber: 250,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                lineNumber: 237,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                        lineNumber: 216,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-1 mt-2 text-xs text-gray-500",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                                className: "h-3 w-3"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                                lineNumber: 265,
                                                                columnNumber: 27
                                                            }, this),
                                                            formatTimeAgo(notification.createdAt)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                        lineNumber: 264,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/NotificationDropdown.tsx",
                                                lineNumber: 215,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                                        lineNumber: 208,
                                        columnNumber: 21
                                    }, this)
                                }, notification.id, false, {
                                    fileName: "[project]/src/components/NotificationDropdown.tsx",
                                    lineNumber: 201,
                                    columnNumber: 19
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                            lineNumber: 199,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this),
                    notifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 border-t border-gray-200 bg-gray-50",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>{
                                router.push('/notifications');
                                setIsOpen(false);
                            },
                            className: "w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium",
                            children: "عرض جميع التنبيهات"
                        }, void 0, false, {
                            fileName: "[project]/src/components/NotificationDropdown.tsx",
                            lineNumber: 279,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/NotificationDropdown.tsx",
                        lineNumber: 278,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/NotificationDropdown.tsx",
                lineNumber: 125,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/NotificationDropdown.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
}
_s(NotificationDropdown, "DZ0V/XZpQX4tPhOhVp2yAPrIP2I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"]
    ];
});
_c = NotificationDropdown;
var _c;
__turbopack_context__.k.register(_c, "NotificationDropdown");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/MobileMenuButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>MobileMenuButton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/menu.js [app-client] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
'use client';
;
;
function MobileMenuButton(param) {
    let { isOpen, onClick } = param;
    const handleClick = ()=>{
        onClick();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        id: "mobile-menu-button",
        onClick: handleClick,
        className: "p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center",
        "aria-label": isOpen ? 'إغلاق القائمة' : 'فتح القائمة',
        children: isOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
            className: "h-6 w-6"
        }, void 0, false, {
            fileName: "[project]/src/components/MobileMenuButton.tsx",
            lineNumber: 23,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
            className: "h-6 w-6"
        }, void 0, false, {
            fileName: "[project]/src/components/MobileMenuButton.tsx",
            lineNumber: 25,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/MobileMenuButton.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
_c = MobileMenuButton;
var _c;
__turbopack_context__.k.register(_c, "MobileMenuButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Header
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$NotificationDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/NotificationDropdown.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MobileMenuButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/MobileMenuButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-client] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function Header() {
    let { onMobileMenuToggle, isMobileMenuOpen = false } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const [showUserMenu, setShowUserMenu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleLogout = async ()=>{
        await logout();
        router.push('/login');
    };
    const getRoleDisplayName = (role)=>{
        const roleNames = {
            admin: 'مدير النظام',
            manager: 'مدير',
            pharmacist: 'صيدلي',
            cashier: 'كاشير',
            viewer: 'مشاهد'
        };
        return roleNames[role] || role;
    };
    const getRoleColor = (role)=>{
        const colors = {
            admin: 'bg-red-600',
            manager: 'bg-purple-600',
            pharmacist: 'bg-blue-600',
            cashier: 'bg-green-600',
            viewer: 'bg-gray-600'
        };
        return colors[role] || 'bg-gray-600';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 md:right-64 z-40 header-mobile",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between h-full px-3 md:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2 md:gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "md:hidden",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MobileMenuButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    isOpen: isMobileMenuOpen,
                                    onClick: onMobileMenuToggle || (()=>console.log('⚠️ No onMobileMenuToggle function provided!'))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Header.tsx",
                                    lineNumber: 61,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/Header.tsx",
                                lineNumber: 60,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative hidden sm:hidden md:block",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                        className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Header.tsx",
                                        lineNumber: 69,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "text",
                                        placeholder: "البحث...",
                                        className: "pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/Header.tsx",
                                        lineNumber: 70,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Header.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "md:hidden p-2 hover:bg-gray-100 rounded-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                    className: "h-5 w-5 text-gray-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/Header.tsx",
                                    lineNumber: 79,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/Header.tsx",
                                lineNumber: 78,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Header.tsx",
                        lineNumber: 58,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2 md:gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$NotificationDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/Header.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setShowUserMenu(!showUserMenu),
                                        className: "flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-right hidden md:block",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm font-medium text-gray-800",
                                                        children: (user === null || user === void 0 ? void 0 : user.full_name) || 'مستخدم'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 95,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs text-gray-500",
                                                        children: user ? getRoleDisplayName(user.role) : ''
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 96,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 94,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "".concat(user ? getRoleColor(user.role) : 'bg-gray-600', " p-2 rounded-full"),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                    className: "h-4 w-4 text-white"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/Header.tsx",
                                                    lineNumber: 99,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 98,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                className: "h-4 w-4 text-gray-400 hidden md:block"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 101,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Header.tsx",
                                        lineNumber: 89,
                                        columnNumber: 13
                                    }, this),
                                    showUserMenu && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "px-4 py-3 border-b border-gray-100",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "".concat(user ? getRoleColor(user.role) : 'bg-gray-600', " p-2 rounded-full"),
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                                    className: "h-5 w-5 text-white"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/Header.tsx",
                                                                    lineNumber: 111,
                                                                    columnNumber: 23
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/Header.tsx",
                                                                lineNumber: 110,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-sm font-medium text-gray-900",
                                                                        children: user === null || user === void 0 ? void 0 : user.full_name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/Header.tsx",
                                                                        lineNumber: 114,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-xs text-gray-500",
                                                                        children: [
                                                                            "@",
                                                                            user === null || user === void 0 ? void 0 : user.username
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/Header.tsx",
                                                                        lineNumber: 115,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-xs text-gray-500",
                                                                        children: user === null || user === void 0 ? void 0 : user.email
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/Header.tsx",
                                                                        lineNumber: 116,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/Header.tsx",
                                                                lineNumber: 113,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 109,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat((user === null || user === void 0 ? void 0 : user.role) === 'admin' ? 'bg-red-100 text-red-800' : (user === null || user === void 0 ? void 0 : user.role) === 'manager' ? 'bg-purple-100 text-purple-800' : (user === null || user === void 0 ? void 0 : user.role) === 'pharmacist' ? 'bg-blue-100 text-blue-800' : (user === null || user === void 0 ? void 0 : user.role) === 'cashier' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                                                                    className: "h-3 w-3 ml-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/Header.tsx",
                                                                    lineNumber: 127,
                                                                    columnNumber: 23
                                                                }, this),
                                                                user ? getRoleDisplayName(user.role) : ''
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/Header.tsx",
                                                            lineNumber: 120,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 119,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 108,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "py-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>{
                                                            setShowUserMenu(false);
                                                            router.push('/profile');
                                                        },
                                                        className: "flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/Header.tsx",
                                                                lineNumber: 142,
                                                                columnNumber: 21
                                                            }, this),
                                                            "الملف الشخصي"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>{
                                                            setShowUserMenu(false);
                                                            router.push('/activity-log');
                                                        },
                                                        className: "flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/Header.tsx",
                                                                lineNumber: 153,
                                                                columnNumber: 21
                                                            }, this),
                                                            "سجل النشاطات"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 146,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>{
                                                            setShowUserMenu(false);
                                                            router.push('/settings');
                                                        },
                                                        className: "flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/Header.tsx",
                                                                lineNumber: 164,
                                                                columnNumber: 21
                                                            }, this),
                                                            "الإعدادات"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/Header.tsx",
                                                        lineNumber: 157,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 134,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "border-t border-gray-100 pt-1",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>{
                                                        setShowUserMenu(false);
                                                        handleLogout();
                                                    },
                                                    className: "flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/Header.tsx",
                                                            lineNumber: 178,
                                                            columnNumber: 21
                                                        }, this),
                                                        "تسجيل الخروج"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/Header.tsx",
                                                    lineNumber: 171,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/Header.tsx",
                                                lineNumber: 170,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/Header.tsx",
                                        lineNumber: 106,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/Header.tsx",
                                lineNumber: 88,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Header.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Header.tsx",
                lineNumber: 57,
                columnNumber: 7
            }, this),
            showUserMenu && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-40",
                onClick: ()=>setShowUserMenu(false)
            }, void 0, false, {
                fileName: "[project]/src/components/Header.tsx",
                lineNumber: 190,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Header.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
_s(Header, "ue4JXPytN0MeY8hkX0GmibQyev4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ErrorBoundary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "useErrorHandler": ()=>useErrorHandler,
    "withErrorBoundary": ()=>withErrorBoundary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
;
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
class ErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        this.setState({
            error,
            errorInfo
        });
    }
    render() {
        if (this.state.hasError) {
            var _this_state_error, _this_state_errorInfo;
            if (this.props.fallback) {
                const FallbackComponent = this.props.fallback;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FallbackComponent, {
                    error: this.state.error,
                    retry: this.retry
                }, void 0, false, {
                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                    lineNumber: 46,
                    columnNumber: 16
                }, this);
            }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                className: "h-12 w-12 text-red-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ErrorBoundary.tsx",
                                lineNumber: 53,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 52,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold text-gray-900 mb-2",
                            children: "حدث خطأ غير متوقع"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 56,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 mb-4",
                            children: "عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى."
                        }, void 0, false, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 58,
                            columnNumber: 13
                        }, this),
                        this.state.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-red-800 font-mono",
                                children: this.state.error.message
                            }, void 0, false, {
                                fileName: "[project]/src/components/ErrorBoundary.tsx",
                                lineNumber: 64,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 63,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-3 justify-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: this.retry,
                                    className: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                                            lineNumber: 75,
                                            columnNumber: 17
                                        }, this),
                                        "إعادة المحاولة"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                                    lineNumber: 71,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.reload(),
                                    className: "bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",
                                    children: "إعادة تحميل الصفحة"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                                    lineNumber: 79,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 70,
                            columnNumber: 13
                        }, this),
                        ("TURBOPACK compile-time value", "development") === 'development' && this.state.errorInfo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                            className: "mt-4 text-left",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                    className: "cursor-pointer text-sm text-gray-500 hover:text-gray-700",
                                    children: "تفاصيل الخطأ (للمطورين)"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                                    lineNumber: 89,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                    className: "mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40",
                                    children: [
                                        (_this_state_error = this.state.error) === null || _this_state_error === void 0 ? void 0 : _this_state_error.stack,
                                        '\n\n',
                                        (_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                                    lineNumber: 92,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ErrorBoundary.tsx",
                            lineNumber: 88,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ErrorBoundary.tsx",
                    lineNumber: 51,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ErrorBoundary.tsx",
                lineNumber: 50,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
    constructor(props){
        super(props), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "retry", ()=>{
            this.setState({
                hasError: false,
                error: undefined,
                errorInfo: undefined
            });
        });
        this.state = {
            hasError: false
        };
    }
}
function useErrorHandler() {
    _s();
    const [error, setError] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const resetError = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "useErrorHandler.useCallback[resetError]": ()=>{
            setError(null);
        }
    }["useErrorHandler.useCallback[resetError]"], []);
    const handleError = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useCallback({
        "useErrorHandler.useCallback[handleError]": (error)=>{
            console.error('Error caught by useErrorHandler:', error);
            setError(error);
        }
    }["useErrorHandler.useCallback[handleError]"], []);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "useErrorHandler.useEffect": ()=>{
            if (error) {
                throw error;
            }
        }
    }["useErrorHandler.useEffect"], [
        error
    ]);
    return {
        handleError,
        resetError
    };
}
_s(useErrorHandler, "JFBWx6A3DUv0A0pLKDz1G96WXws=");
function withErrorBoundary(Component, fallback) {
    const WrappedComponent = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ErrorBoundary, {
            fallback: fallback,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/ErrorBoundary.tsx",
                lineNumber: 137,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ErrorBoundary.tsx",
            lineNumber: 136,
            columnNumber: 5
        }, this);
    WrappedComponent.displayName = "withErrorBoundary(".concat(Component.displayName || Component.name, ")");
    return WrappedComponent;
}
const __TURBOPACK__default__export__ = ErrorBoundary;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ToastNotifications.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>ToastNotifications
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-client] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-client] (ecmascript) <export default as XCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ToastNotifications() {
    _s();
    const [toasts, setToasts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const { notifications } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    // مراقبة التنبيهات الجديدة وعرضها كـ Toast
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToastNotifications.useEffect": ()=>{
            const latestNotification = notifications[0];
            if (latestNotification && !latestNotification.isRead) {
                // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)
                const notificationTime = new Date(latestNotification.createdAt).getTime();
                const now = new Date().getTime();
                const diffInMinutes = (now - notificationTime) / (1000 * 60);
                if (diffInMinutes < 1) {
                    showToast({
                        id: latestNotification.id,
                        type: latestNotification.type,
                        title: latestNotification.title,
                        message: latestNotification.message,
                        actionUrl: latestNotification.actionUrl,
                        actionLabel: latestNotification.actionLabel,
                        duration: getDurationByPriority(latestNotification.priority)
                    });
                }
            }
        }
    }["ToastNotifications.useEffect"], [
        notifications
    ]);
    const getDurationByPriority = (priority)=>{
        switch(priority){
            case 'critical':
                return 10000 // 10 ثواني
                ;
            case 'high':
                return 7000 // 7 ثواني
                ;
            case 'medium':
                return 5000 // 5 ثواني
                ;
            case 'low':
                return 3000 // 3 ثواني
                ;
            default:
                return 5000;
        }
    };
    const showToast = (toast)=>{
        // تجنب التكرار
        if (toasts.find((t)=>t.id === toast.id)) return;
        setToasts((prev)=>[
                ...prev,
                toast
            ]);
        // إزالة التنبيه تلقائياً بعد المدة المحددة
        setTimeout(()=>{
            removeToast(toast.id);
        }, toast.duration || 5000);
    };
    const removeToast = (id)=>{
        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));
    };
    const getToastIcon = (type)=>{
        switch(type){
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                    className: "h-5 w-5 text-green-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 80,
                    columnNumber: 16
                }, this);
            case 'warning':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                    className: "h-5 w-5 text-yellow-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 82,
                    columnNumber: 16
                }, this);
            case 'error':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
                    className: "h-5 w-5 text-red-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 84,
                    columnNumber: 16
                }, this);
            case 'info':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
                    className: "h-5 w-5 text-blue-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 86,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
                    className: "h-5 w-5 text-gray-500"
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 88,
                    columnNumber: 16
                }, this);
        }
    };
    const getToastStyles = (type)=>{
        switch(type){
            case 'success':
                return 'bg-green-50 border-green-200 text-green-800';
            case 'warning':
                return 'bg-yellow-50 border-yellow-200 text-yellow-800';
            case 'error':
                return 'bg-red-50 border-red-200 text-red-800';
            case 'info':
                return 'bg-blue-50 border-blue-200 text-blue-800';
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };
    if (toasts.length === 0) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed top-20 left-4 md:left-72 z-[60] space-y-3 max-w-sm",
        children: toasts.map((toast)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full shadow-lg rounded-lg border p-3 md:p-4 transition-all duration-300 transform animate-slide-in-left ".concat(getToastStyles(toast.type)),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start gap-2 md:gap-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0 mt-0.5",
                            children: getToastIcon(toast.type)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ToastNotifications.tsx",
                            lineNumber: 118,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 min-w-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-xs md:text-sm font-medium mb-1",
                                    children: toast.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ToastNotifications.tsx",
                                    lineNumber: 124,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs md:text-sm opacity-90 line-clamp-2",
                                    children: toast.message
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ToastNotifications.tsx",
                                    lineNumber: 127,
                                    columnNumber: 15
                                }, this),
                                toast.actionUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: toast.actionUrl,
                                    className: "inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline min-h-[32px]",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                            className: "h-3 w-3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ToastNotifications.tsx",
                                            lineNumber: 137,
                                            columnNumber: 19
                                        }, this),
                                        toast.actionLabel || 'عرض التفاصيل'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ToastNotifications.tsx",
                                    lineNumber: 133,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ToastNotifications.tsx",
                            lineNumber: 123,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>removeToast(toast.id),
                            className: "flex-shrink-0 p-2 hover:bg-black hover:bg-opacity-10 rounded min-h-[44px] min-w-[44px] flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ToastNotifications.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ToastNotifications.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ToastNotifications.tsx",
                    lineNumber: 116,
                    columnNumber: 11
                }, this)
            }, toast.id, false, {
                fileName: "[project]/src/components/ToastNotifications.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ToastNotifications.tsx",
        lineNumber: 110,
        columnNumber: 5
    }, this);
}
_s(ToastNotifications, "yBiPY6BATSJIu5onCvb4F560wAk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"]
    ];
});
_c = ToastNotifications;
// إضافة الأنيميشن إلى CSS
const toastStyles = "\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n";
// إضافة الأنيميشن إلى الصفحة
if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = toastStyles;
    document.head.appendChild(styleElement);
}
var _c;
__turbopack_context__.k.register(_c, "ToastNotifications");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/MobileOptimizer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>MobileOptimizer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function MobileOptimizer() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MobileOptimizer.useEffect": ()=>{
            // Detect mobile device
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            if (isMobile || isTouch) {
                // Add mobile-specific classes to body
                document.body.classList.add('mobile-device', 'touch-device');
                // Prevent zoom on input focus (iOS)
                const viewport = document.querySelector('meta[name=viewport]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover');
                }
                // Add touch-friendly classes to interactive elements
                const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
                interactiveElements.forEach({
                    "MobileOptimizer.useEffect": (element)=>{
                        element.classList.add('touch-friendly');
                    }
                }["MobileOptimizer.useEffect"]);
                // Optimize scrolling
                document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch');
                document.body.style.setProperty('-webkit-overflow-scrolling', 'touch');
                // Prevent bounce scrolling on iOS
                document.addEventListener('touchmove', {
                    "MobileOptimizer.useEffect": (e)=>{
                        if (e.target === document.body) {
                            e.preventDefault();
                        }
                    }
                }["MobileOptimizer.useEffect"], {
                    passive: false
                });
                // Add safe area support
                if (CSS.supports('padding: max(0px)')) {
                    document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
                    document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
                    document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)');
                    document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)');
                }
                // Optimize performance for mobile
                const optimizeElement = {
                    "MobileOptimizer.useEffect.optimizeElement": (element)=>{
                        if (element instanceof HTMLElement) {
                            element.style.setProperty('transform', 'translateZ(0)');
                            element.style.setProperty('will-change', 'transform');
                        }
                    }
                }["MobileOptimizer.useEffect.optimizeElement"];
                // Apply optimizations to animated elements
                const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift');
                animatedElements.forEach(optimizeElement);
                // Handle orientation change
                const handleOrientationChange = {
                    "MobileOptimizer.useEffect.handleOrientationChange": ()=>{
                        // Force repaint after orientation change
                        setTimeout({
                            "MobileOptimizer.useEffect.handleOrientationChange": ()=>{
                                window.scrollTo(0, window.scrollY);
                            }
                        }["MobileOptimizer.useEffect.handleOrientationChange"], 100);
                    }
                }["MobileOptimizer.useEffect.handleOrientationChange"];
                window.addEventListener('orientationchange', handleOrientationChange);
                // Cleanup
                return ({
                    "MobileOptimizer.useEffect": ()=>{
                        window.removeEventListener('orientationchange', handleOrientationChange);
                    }
                })["MobileOptimizer.useEffect"];
            }
        }
    }["MobileOptimizer.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MobileOptimizer.useEffect": ()=>{
            // Add PWA install prompt handling
            let deferredPrompt;
            const handleBeforeInstallPrompt = {
                "MobileOptimizer.useEffect.handleBeforeInstallPrompt": (e)=>{
                    e.preventDefault();
                    deferredPrompt = e;
                    // Show custom install button or banner
                    const installBanner = document.getElementById('pwa-install-banner');
                    if (installBanner) {
                        installBanner.style.display = 'block';
                    }
                }
            }["MobileOptimizer.useEffect.handleBeforeInstallPrompt"];
            const handleAppInstalled = {
                "MobileOptimizer.useEffect.handleAppInstalled": ()=>{
                    console.log('PWA was installed');
                    deferredPrompt = null;
                    // Hide install banner
                    const installBanner = document.getElementById('pwa-install-banner');
                    if (installBanner) {
                        installBanner.style.display = 'none';
                    }
                }
            }["MobileOptimizer.useEffect.handleAppInstalled"];
            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
            window.addEventListener('appinstalled', handleAppInstalled);
            return ({
                "MobileOptimizer.useEffect": ()=>{
                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
                    window.removeEventListener('appinstalled', handleAppInstalled);
                }
            })["MobileOptimizer.useEffect"];
        }
    }["MobileOptimizer.useEffect"], []);
    return null // This component doesn't render anything
    ;
}
_s(MobileOptimizer, "3ubReDTFssvu4DHeldAg55cW/CI=");
_c = MobileOptimizer;
var _c;
__turbopack_context__.k.register(_c, "MobileOptimizer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/PWAInstallBanner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>PWAInstallBanner
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/smartphone.js [app-client] (ecmascript) <export default as Smartphone>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function PWAInstallBanner() {
    _s();
    const [showBanner, setShowBanner] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [deferredPrompt, setDeferredPrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PWAInstallBanner.useEffect": ()=>{
            const handleBeforeInstallPrompt = {
                "PWAInstallBanner.useEffect.handleBeforeInstallPrompt": (e)=>{
                    e.preventDefault();
                    setDeferredPrompt(e);
                    setShowBanner(true);
                }
            }["PWAInstallBanner.useEffect.handleBeforeInstallPrompt"];
            const handleAppInstalled = {
                "PWAInstallBanner.useEffect.handleAppInstalled": ()=>{
                    setShowBanner(false);
                    setDeferredPrompt(null);
                }
            }["PWAInstallBanner.useEffect.handleAppInstalled"];
            window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
            window.addEventListener('appinstalled', handleAppInstalled);
            // Check if app is already installed
            if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                setShowBanner(false);
            }
            return ({
                "PWAInstallBanner.useEffect": ()=>{
                    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
                    window.removeEventListener('appinstalled', handleAppInstalled);
                }
            })["PWAInstallBanner.useEffect"];
        }
    }["PWAInstallBanner.useEffect"], []);
    const handleInstallClick = async ()=>{
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
            setDeferredPrompt(null);
            setShowBanner(false);
        }
    };
    const handleDismiss = ()=>{
        setShowBanner(false);
        // Remember user dismissed the banner
        localStorage.setItem('pwa-install-dismissed', 'true');
    };
    // Don't show if user previously dismissed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PWAInstallBanner.useEffect": ()=>{
            const dismissed = localStorage.getItem('pwa-install-dismissed');
            if (dismissed === 'true') {
                setShowBanner(false);
            }
        }
    }["PWAInstallBanner.useEffect"], []);
    if (!showBanner) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        id: "pwa-install-banner",
        className: "fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start gap-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white bg-opacity-20 p-2 rounded-lg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"], {
                        className: "h-5 w-5"
                    }, void 0, false, {
                        fileName: "[project]/src/components/PWAInstallBanner.tsx",
                        lineNumber: 75,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/PWAInstallBanner.tsx",
                    lineNumber: 74,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "font-semibold text-sm md:text-base mb-1",
                            children: "تثبيت التطبيق"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PWAInstallBanner.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-blue-100 text-xs md:text-sm mb-3",
                            children: "ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت"
                        }, void 0, false, {
                            fileName: "[project]/src/components/PWAInstallBanner.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleInstallClick,
                                    className: "bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                            className: "h-3 w-3 md:h-4 md:w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/PWAInstallBanner.tsx",
                                            lineNumber: 89,
                                            columnNumber: 15
                                        }, this),
                                        "تثبيت"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/PWAInstallBanner.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleDismiss,
                                    className: "text-blue-100 hover:text-white transition-colors",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                        className: "h-4 w-4 md:h-5 md:w-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/PWAInstallBanner.tsx",
                                        lineNumber: 96,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/PWAInstallBanner.tsx",
                                    lineNumber: 92,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/PWAInstallBanner.tsx",
                            lineNumber: 84,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/PWAInstallBanner.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/PWAInstallBanner.tsx",
            lineNumber: 73,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/PWAInstallBanner.tsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_s(PWAInstallBanner, "r4bXssVLLng/NBlgd/cDXYMEMiI=");
_c = PWAInstallBanner;
var _c;
__turbopack_context__.k.register(_c, "PWAInstallBanner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ScreenSizeIndicator.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>ScreenSizeIndicator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Monitor$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript) <export default as Monitor>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tablet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tablet$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tablet.js [app-client] (ecmascript) <export default as Tablet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/smartphone.js [app-client] (ecmascript) <export default as Smartphone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/laptop.js [app-client] (ecmascript) <export default as Laptop>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function ScreenSizeIndicator() {
    _s();
    const [screenInfo, setScreenInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        width: 0,
        height: 0,
        breakpoint: '',
        device: '',
        orientation: ''
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ScreenSizeIndicator.useEffect": ()=>{
            const updateScreenInfo = {
                "ScreenSizeIndicator.useEffect.updateScreenInfo": ()=>{
                    const width = window.innerWidth;
                    const height = window.innerHeight;
                    let breakpoint = '';
                    let device = '';
                    if (width < 640) {
                        breakpoint = 'sm (< 640px)';
                        device = 'هاتف صغير';
                    } else if (width < 768) {
                        breakpoint = 'md (640px - 768px)';
                        device = 'هاتف كبير';
                    } else if (width < 1024) {
                        breakpoint = 'lg (768px - 1024px)';
                        device = 'تابلت';
                    } else if (width < 1280) {
                        breakpoint = 'xl (1024px - 1280px)';
                        device = 'لابتوب';
                    } else {
                        breakpoint = '2xl (> 1280px)';
                        device = 'كمبيوتر مكتبي';
                    }
                    const orientation = width > height ? 'أفقي' : 'عمودي';
                    setScreenInfo({
                        width,
                        height,
                        breakpoint,
                        device,
                        orientation
                    });
                }
            }["ScreenSizeIndicator.useEffect.updateScreenInfo"];
            updateScreenInfo();
            window.addEventListener('resize', updateScreenInfo);
            window.addEventListener('orientationchange', updateScreenInfo);
            return ({
                "ScreenSizeIndicator.useEffect": ()=>{
                    window.removeEventListener('resize', updateScreenInfo);
                    window.removeEventListener('orientationchange', updateScreenInfo);
                }
            })["ScreenSizeIndicator.useEffect"];
        }
    }["ScreenSizeIndicator.useEffect"], []);
    const getIcon = ()=>{
        if (screenInfo.width < 768) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$smartphone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Smartphone$3e$__["Smartphone"];
        if (screenInfo.width < 1024) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tablet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tablet$3e$__["Tablet"];
        if (screenInfo.width < 1280) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$laptop$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Laptop$3e$__["Laptop"];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Monitor$3e$__["Monitor"];
    };
    const Icon = getIcon();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2 mb-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: screenInfo.device
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            screenInfo.width,
                            " × ",
                            screenInfo.height
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                        lineNumber: 77,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: screenInfo.breakpoint
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: screenInfo.orientation
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
                lineNumber: 76,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ScreenSizeIndicator.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
_s(ScreenSizeIndicator, "HRPmtLI5JndYa41o/mdSYWtg5xg=");
_c = ScreenSizeIndicator;
var _c;
__turbopack_context__.k.register(_c, "ScreenSizeIndicator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AppLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>AppLayout
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ErrorBoundary.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ToastNotifications$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ToastNotifications.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MobileOptimizer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/MobileOptimizer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PWAInstallBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/PWAInstallBanner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ScreenSizeIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ScreenSizeIndicator.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function AppLayout(param) {
    let { children } = param;
    _s();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const toggleMobileMenu = ()=>{
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MobileOptimizer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isMobileMenuOpen,
                onClose: ()=>setIsMobileMenuOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onMobileMenuToggle: toggleMobileMenu,
                isMobileMenuOpen: isMobileMenuOpen
            }, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "\n        mr-0 md:mr-64\n        mt-16\n        p-3 md:p-6\n        main-content-mobile\n        min-h-screen\n        w-full\n        max-w-full\n        overflow-x-hidden\n        safe-area-inset-bottom\n      ",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-full overflow-x-auto container",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/AppLayout.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/AppLayout.tsx",
                    lineNumber: 42,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ToastNotifications$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$PWAInstallBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 49,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ScreenSizeIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            isMobileMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight",
                onClick: ()=>setIsMobileMenuOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/AppLayout.tsx",
                lineNumber: 54,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/AppLayout.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_s(AppLayout, "QerECOS75+B7gv+k3q7FrDf39mc=");
_c = AppLayout;
var _c;
__turbopack_context__.k.register(_c, "AppLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": ()=>supabase
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://tazhdabhidycvsvjxqcb.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRhemhkYWJoaWR5Y3Zzdmp4cWNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMzU0NzYsImV4cCI6MjA2ODgxMTQ3Nn0.0nxvya6ZMm-22SgiqCrxxjfJyNK-Zs4vkAPAaPlAQEE");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/database.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCashTransaction": ()=>addCashTransaction,
    "addCustomer": ()=>addCustomer,
    "addInventoryMovement": ()=>addInventoryMovement,
    "addMedicine": ()=>addMedicine,
    "addMedicineBatch": ()=>addMedicineBatch,
    "addPurchaseInvoiceItems": ()=>addPurchaseInvoiceItems,
    "addReturnItems": ()=>addReturnItems,
    "addSalesInvoiceItems": ()=>addSalesInvoiceItems,
    "addSupplier": ()=>addSupplier,
    "cleanAndFixAllLocalStorageData": ()=>cleanAndFixAllLocalStorageData,
    "completePurchaseTransaction": ()=>completePurchaseTransaction,
    "completeSalesTransaction": ()=>completeSalesTransaction,
    "createPurchaseInvoice": ()=>createPurchaseInvoice,
    "createPurchaseReturn": ()=>createPurchaseReturn,
    "createSalesInvoice": ()=>createSalesInvoice,
    "createSalesReturn": ()=>createSalesReturn,
    "createSampleMedicinesIfNeeded": ()=>createSampleMedicinesIfNeeded,
    "fixLocalStorageInvoiceItems": ()=>fixLocalStorageInvoiceItems,
    "getCashBalance": ()=>getCashBalance,
    "getCashTransactions": ()=>getCashTransactions,
    "getCustomerDebts": ()=>getCustomerDebts,
    "getCustomerStatement": ()=>getCustomerStatement,
    "getCustomers": ()=>getCustomers,
    "getInventoryReport": ()=>getInventoryReport,
    "getMedicineMovementReport": ()=>getMedicineMovementReport,
    "getMedicineNameFromBatch": ()=>getMedicineNameFromBatch,
    "getMedicines": ()=>getMedicines,
    "getPurchaseInvoiceForPrint": ()=>getPurchaseInvoiceForPrint,
    "getPurchaseInvoices": ()=>getPurchaseInvoices,
    "getPurchasesReport": ()=>getPurchasesReport,
    "getReturnById": ()=>getReturnById,
    "getReturnForPrint": ()=>getReturnForPrint,
    "getReturns": ()=>getReturns,
    "getSalesInvoiceForPrint": ()=>getSalesInvoiceForPrint,
    "getSalesInvoices": ()=>getSalesInvoices,
    "getSalesReport": ()=>getSalesReport,
    "getSupplierDebts": ()=>getSupplierDebts,
    "getSupplierStatement": ()=>getSupplierStatement,
    "getSuppliers": ()=>getSuppliers,
    "initializeSystemData": ()=>initializeSystemData,
    "processReturn": ()=>processReturn,
    "updateBatchQuantity": ()=>updateBatchQuantity,
    "updatePaymentStatus": ()=>updatePaymentStatus,
    "updateReturn": ()=>updateReturn,
    "updateReturnStatus": ()=>updateReturnStatus
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
const addMedicine = async (medicineData)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicines').insert([
            {
                name: medicineData.name,
                category: medicineData.category,
                manufacturer: medicineData.manufacturer || '',
                active_ingredient: medicineData.active_ingredient || '',
                strength: medicineData.strength || '',
                form: medicineData.form,
                unit_price: medicineData.unit_price,
                selling_price: medicineData.selling_price
            }
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding medicine:', error);
        return {
            success: false,
            error
        };
    }
};
const getMedicines = async ()=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicines').select("\n        *,\n        medicine_batches (\n          id,\n          batch_code,\n          expiry_date,\n          quantity,\n          cost_price,\n          selling_price,\n          supplier_id,\n          received_date\n        )\n      ").order('name');
        if (error) {
            console.warn('Supabase error fetching medicines, using localStorage:', error);
            // Fallback to localStorage
            return getMedicinesFromLocalStorage();
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching medicines:', error);
        // Final fallback to localStorage
        return getMedicinesFromLocalStorage();
    }
};
// Helper function to get medicines from localStorage
const getMedicinesFromLocalStorage = ()=>{
    try {
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        // If no medicines in localStorage, create sample data
        if (medicines.length === 0) {
            console.log('🔄 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية...');
            return createSampleMedicinesData();
        }
        // Combine medicines with their batches
        const medicinesWithBatches = medicines.map((medicine)=>({
                ...medicine,
                medicine_batches: batches.filter((batch)=>batch.medicine_id === medicine.id),
                batches: batches.filter((batch)=>batch.medicine_id === medicine.id)
            }));
        console.log("✅ تم تحميل ".concat(medicinesWithBatches.length, " دواء من localStorage"));
        return {
            success: true,
            data: medicinesWithBatches
        };
    } catch (error) {
        console.error('Error loading medicines from localStorage:', error);
        return {
            success: false,
            error
        };
    }
};
// Helper function to create sample medicines data
const createSampleMedicinesData = ()=>{
    try {
        const sampleMedicines = [
            {
                id: 'med_1',
                name: 'باراسيتامول 500 مجم',
                category: 'مسكنات',
                manufacturer: 'شركة الأدوية العراقية',
                strength: '500mg',
                form: 'أقراص',
                created_at: new Date().toISOString()
            },
            {
                id: 'med_2',
                name: 'أموكسيسيلين 250 مجم',
                category: 'مضادات حيوية',
                manufacturer: 'شركة بغداد للأدوية',
                strength: '250mg',
                form: 'كبسولات',
                created_at: new Date().toISOString()
            },
            {
                id: 'med_3',
                name: 'أسبرين 100 مجم',
                category: 'مسكنات',
                manufacturer: 'شركة النهرين',
                strength: '100mg',
                form: 'أقراص',
                created_at: new Date().toISOString()
            },
            {
                id: 'med_4',
                name: 'إيبوبروفين 400 مجم',
                category: 'مسكنات',
                manufacturer: 'شركة الرافدين',
                strength: '400mg',
                form: 'أقراص',
                created_at: new Date().toISOString()
            },
            {
                id: 'med_5',
                name: 'أوميبرازول 20 مجم',
                category: 'أدوية المعدة',
                manufacturer: 'شركة دجلة',
                strength: '20mg',
                form: 'كبسولات',
                created_at: new Date().toISOString()
            }
        ];
        const sampleBatches = [
            {
                id: 'batch_1',
                medicine_id: 'med_1',
                batch_code: 'PAR001',
                expiry_date: '2025-12-31',
                quantity: 100,
                cost_price: 500,
                selling_price: 750,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            },
            {
                id: 'batch_2',
                medicine_id: 'med_2',
                batch_code: 'AMX001',
                expiry_date: '2025-06-30',
                quantity: 50,
                cost_price: 1000,
                selling_price: 1500,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            },
            {
                id: 'batch_3',
                medicine_id: 'med_3',
                batch_code: 'ASP001',
                expiry_date: '2026-03-31',
                quantity: 200,
                cost_price: 300,
                selling_price: 500,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            },
            {
                id: 'batch_4',
                medicine_id: 'med_4',
                batch_code: 'IBU001',
                expiry_date: '2025-09-30',
                quantity: 75,
                cost_price: 800,
                selling_price: 1200,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            },
            {
                id: 'batch_5',
                medicine_id: 'med_5',
                batch_code: 'OME001',
                expiry_date: '2025-11-30',
                quantity: 30,
                cost_price: 1500,
                selling_price: 2000,
                received_date: '2024-01-01',
                created_at: new Date().toISOString()
            }
        ];
        // Create sample customers
        const sampleCustomers = [
            {
                id: 'cust_1',
                name: 'أحمد محمد علي',
                phone: '07701234567',
                address: 'بغداد - الكرادة',
                created_at: new Date().toISOString()
            },
            {
                id: 'cust_2',
                name: 'فاطمة حسن',
                phone: '07809876543',
                address: 'بغداد - الجادرية',
                created_at: new Date().toISOString()
            }
        ];
        // Save to localStorage
        localStorage.setItem('medicines', JSON.stringify(sampleMedicines));
        localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches));
        localStorage.setItem('customers', JSON.stringify(sampleCustomers));
        // Initialize empty arrays for invoices
        localStorage.setItem('sales_invoices', JSON.stringify([]));
        localStorage.setItem('sales_invoice_items', JSON.stringify([]));
        // Combine medicines with their batches
        const medicinesWithBatches = sampleMedicines.map((medicine)=>({
                ...medicine,
                medicine_batches: sampleBatches.filter((batch)=>batch.medicine_id === medicine.id),
                batches: sampleBatches.filter((batch)=>batch.medicine_id === medicine.id)
            }));
        console.log("✅ تم إنشاء ".concat(medicinesWithBatches.length, " دواء تجريبي"));
        console.log("✅ تم إنشاء ".concat(sampleBatches.length, " دفعة تجريبية"));
        console.log("✅ تم إنشاء ".concat(sampleCustomers.length, " عميل تجريبي"));
        return {
            success: true,
            data: medicinesWithBatches
        };
    } catch (error) {
        console.error('Error creating sample medicines:', error);
        return {
            success: false,
            error
        };
    }
};
const initializeSystemData = async ()=>{
    try {
        console.log('🔄 تهيئة بيانات النظام...');
        // Check if we have basic data
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        if (medicines.length === 0 || batches.length === 0) {
            console.log('📦 إنشاء البيانات الأساسية...');
            return createSampleMedicinesData();
        }
        console.log("✅ البيانات الأساسية موجودة: ".concat(medicines.length, " دواء، ").concat(batches.length, " دفعة"));
        return {
            success: true,
            data: medicines
        };
    } catch (error) {
        console.error('Error initializing system data:', error);
        return {
            success: false,
            error
        };
    }
};
const addMedicineBatch = async (batchData)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').insert([
            batchData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding medicine batch:', error);
        return {
            success: false,
            error
        };
    }
};
const updateBatchQuantity = async (batchId, newQuantity)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').update({
            quantity: newQuantity
        }).eq('id', batchId).select().single();
        if (error) {
            console.warn('Supabase error updating batch quantity, using localStorage:', error);
            // Fallback to localStorage
            const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            const batchIndex = existingBatches.findIndex((batch)=>batch.id === batchId);
            if (batchIndex !== -1) {
                existingBatches[batchIndex].quantity = newQuantity;
                localStorage.setItem('medicine_batches', JSON.stringify(existingBatches));
                return {
                    success: true,
                    data: existingBatches[batchIndex]
                };
            }
            return {
                success: false,
                error: 'Batch not found in localStorage'
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error updating batch quantity:', error);
        // Final fallback to localStorage
        try {
            const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            const batchIndex = existingBatches.findIndex((batch)=>batch.id === batchId);
            if (batchIndex !== -1) {
                existingBatches[batchIndex].quantity = newQuantity;
                localStorage.setItem('medicine_batches', JSON.stringify(existingBatches));
                return {
                    success: true,
                    data: existingBatches[batchIndex]
                };
            }
            return {
                success: false,
                error: 'Batch not found'
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed for batch update:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
const createSalesInvoice = async (invoiceData)=>{
    try {
        // Try Supabase first
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').insert([
            invoiceData
        ]).select().single();
        if (error) {
            console.warn('Supabase error, using localStorage:', error);
            // Fallback to localStorage
            const invoiceId = "invoice_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
            const invoice = {
                id: invoiceId,
                ...invoiceData,
                created_at: new Date().toISOString()
            };
            // Save to localStorage
            const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            existingInvoices.push(invoice);
            localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices));
            return {
                success: true,
                data: invoice
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error creating sales invoice:', error);
        // Final fallback to localStorage
        try {
            const invoiceId = "invoice_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
            const invoice = {
                id: invoiceId,
                ...invoiceData,
                created_at: new Date().toISOString()
            };
            const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            existingInvoices.push(invoice);
            localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices));
            return {
                success: true,
                data: invoice
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
const addSalesInvoiceItems = async (items)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoice_items').insert(items).select();
        if (error) {
            console.warn('Supabase error for invoice items, using localStorage:', error);
            // Fallback to localStorage - preserve existing medicine names
            console.log('📦 العناصر الواردة للحفظ:', items);
            const enhancedItems = items.map((item)=>{
                // Use existing medicine name if available, otherwise enhance
                const medicineName = item.medicine_name || item.medicineName;
                if (medicineName && medicineName !== 'غير محدد') {
                    console.log("✅ استخدام اسم الدواء الموجود: ".concat(medicineName));
                    return {
                        id: "item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                        ...item,
                        medicine_name: medicineName,
                        medicineName: medicineName,
                        medicine_batches: {
                            batch_code: '',
                            expiry_date: '',
                            medicines: {
                                name: medicineName,
                                category: '',
                                manufacturer: '',
                                strength: '',
                                form: ''
                            }
                        },
                        created_at: new Date().toISOString()
                    };
                } else {
                    console.log("⚠️ لا يوجد اسم دواء، سيتم البحث عنه...");
                    // Only enhance if no medicine name is available
                    return item;
                }
            });
            // Enhance items that still need medicine names
            const itemsNeedingEnhancement = enhancedItems.filter((item)=>!item.medicine_name || item.medicine_name === 'غير محدد');
            let finalItems = enhancedItems;
            if (itemsNeedingEnhancement.length > 0) {
                console.log("🔍 تحسين ".concat(itemsNeedingEnhancement.length, " عنصر يحتاج أسماء أدوية"));
                const enhancedNeeded = await enhanceItemsWithMedicineNames(itemsNeedingEnhancement);
                // Replace items that needed enhancement
                finalItems = enhancedItems.map((item)=>{
                    if (!item.medicine_name || item.medicine_name === 'غير محدد') {
                        const enhanced = enhancedNeeded.find((e)=>e.medicine_batch_id === item.medicine_batch_id);
                        return enhanced || item;
                    }
                    return item;
                });
            }
            const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            existingItems.push(...finalItems);
            localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems));
            console.log('✅ تم حفظ العناصر في localStorage:', finalItems);
            return {
                success: true,
                data: finalItems
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding sales invoice items:', error);
        // Final fallback to localStorage
        try {
            console.log('🔄 Final fallback - حفظ العناصر مع الأسماء الموجودة');
            const enhancedItems = items.map((item)=>{
                const medicineName = item.medicine_name || item.medicineName || 'غير محدد';
                return {
                    id: "item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                    ...item,
                    medicine_name: medicineName,
                    medicineName: medicineName,
                    medicine_batches: {
                        batch_code: '',
                        expiry_date: '',
                        medicines: {
                            name: medicineName,
                            category: '',
                            manufacturer: '',
                            strength: '',
                            form: ''
                        }
                    },
                    created_at: new Date().toISOString()
                };
            });
            const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            existingItems.push(...enhancedItems);
            localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems));
            console.log('✅ Final fallback - تم حفظ العناصر:', enhancedItems);
            return {
                success: true,
                data: enhancedItems
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed for items:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
// Helper function to enhance items with medicine names
const enhanceItemsWithMedicineNames = async (items)=>{
    try {
        // Get medicine data for names
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        return items.map((item)=>{
            // Use existing medicine name if available, otherwise find from batch
            let medicineName = item.medicine_name || item.medicineName;
            if (!medicineName || medicineName === 'غير محدد') {
                // Find medicine name from batch
                const batch = batches.find((b)=>b.id === item.medicine_batch_id);
                const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
                medicineName = (medicine === null || medicine === void 0 ? void 0 : medicine.name) || 'غير محدد';
            }
            const batch = batches.find((b)=>b.id === item.medicine_batch_id);
            const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
            return {
                id: "item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                ...item,
                medicine_name: medicineName,
                medicineName: medicineName,
                medicine_batches: {
                    batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || '',
                    expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || '',
                    medicines: {
                        name: medicineName,
                        category: (medicine === null || medicine === void 0 ? void 0 : medicine.category) || '',
                        manufacturer: (medicine === null || medicine === void 0 ? void 0 : medicine.manufacturer) || '',
                        strength: (medicine === null || medicine === void 0 ? void 0 : medicine.strength) || '',
                        form: (medicine === null || medicine === void 0 ? void 0 : medicine.form) || ''
                    }
                },
                created_at: new Date().toISOString()
            };
        });
    } catch (error) {
        console.error('خطأ في تحسين العناصر بأسماء الأدوية:', error);
        // Return items with basic structure if enhancement fails
        return items.map((item)=>({
                id: "item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                ...item,
                medicine_name: 'غير محدد',
                created_at: new Date().toISOString()
            }));
    }
};
const fixLocalStorageInvoiceItems = ()=>{
    try {
        console.log('🔧 بدء إصلاح بيانات الفواتير في localStorage...');
        const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        console.log("📦 عدد عناصر الفواتير: ".concat(salesItems.length));
        console.log("💊 عدد الأدوية: ".concat(medicines.length));
        console.log("📋 عدد الدفعات: ".concat(batches.length));
        let fixedCount = 0;
        let notFoundCount = 0;
        let createdBatchesCount = 0;
        const fixedItems = salesItems.map((item)=>{
            var _item_medicine_batches_medicines, _item_medicine_batches;
            // Skip if already has proper medicine name structure
            if (((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) && item.medicine_batches.medicines.name !== 'غير محدد') {
                return item;
            }
            // Find medicine name from batch
            let batch = batches.find((b)=>b.id === item.medicine_batch_id);
            let medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
            // If batch not found, try to create a missing batch
            if (!batch && item.medicine_batch_id) {
                console.log("🔍 محاولة إنشاء دفعة مفقودة: ".concat(item.medicine_batch_id));
                // Try to find medicine by name if available in item
                if (item.medicine_name && item.medicine_name !== 'غير محدد') {
                    medicine = medicines.find((m)=>m.name === item.medicine_name);
                }
                // If still no medicine found, use first available medicine as fallback
                if (!medicine && medicines.length > 0) {
                    medicine = medicines[0];
                    console.log("🔄 استخدام دواء افتراضي: ".concat(medicine.name));
                }
                // Create missing batch if we have a medicine
                if (medicine) {
                    batch = {
                        id: item.medicine_batch_id,
                        medicine_id: medicine.id,
                        batch_code: item.batch_code || "BATCH_".concat(Date.now()),
                        expiry_date: item.expiry_date || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        quantity: item.quantity || 0,
                        cost_price: item.unit_price || medicine.unit_price || 0,
                        selling_price: item.unit_price || medicine.selling_price || 0,
                        supplier_id: null,
                        received_date: new Date().toISOString().split('T')[0],
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    batches.push(batch);
                    createdBatchesCount++;
                    console.log("✅ تم إنشاء دفعة جديدة: ".concat(batch.batch_code, " للدواء: ").concat(medicine.name));
                }
            }
            if (medicine === null || medicine === void 0 ? void 0 : medicine.name) {
                fixedCount++;
                console.log("✅ إصلاح العنصر: ".concat(medicine.name, " (Batch: ").concat(batch === null || batch === void 0 ? void 0 : batch.batch_code, ")"));
                return {
                    ...item,
                    medicine_name: medicine.name,
                    medicineName: medicine.name,
                    medicine_batches: {
                        batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || item.batch_code || '',
                        expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || item.expiry_date || '',
                        medicines: {
                            name: medicine.name,
                            category: medicine.category || '',
                            manufacturer: medicine.manufacturer || '',
                            strength: medicine.strength || '',
                            form: medicine.form || ''
                        }
                    }
                };
            } else {
                notFoundCount++;
                console.log("⚠️ لم يتم العثور على الدواء للعنصر: ".concat(item.medicine_batch_id));
                // Try to preserve any existing name
                const existingName = item.medicine_name || item.medicineName || 'غير محدد';
                return {
                    ...item,
                    medicine_name: existingName,
                    medicineName: existingName,
                    medicine_batches: {
                        batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || item.batch_code || '',
                        expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || item.expiry_date || '',
                        medicines: {
                            name: existingName,
                            category: '',
                            manufacturer: '',
                            strength: '',
                            form: ''
                        }
                    }
                };
            }
        });
        // Save fixed data back to localStorage
        localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems));
        // Save updated batches if any were created
        if (createdBatchesCount > 0) {
            localStorage.setItem('medicine_batches', JSON.stringify(batches));
            console.log("📋 تم حفظ ".concat(createdBatchesCount, " دفعة جديدة"));
        }
        console.log("✅ تم إصلاح ".concat(fixedCount, " عنصر من أصل ").concat(salesItems.length));
        console.log("⚠️ لم يتم العثور على ".concat(notFoundCount, " عنصر"));
        return {
            success: true,
            fixedCount,
            notFoundCount,
            createdBatchesCount,
            totalCount: salesItems.length
        };
    } catch (error) {
        console.error('❌ خطأ في إصلاح بيانات localStorage:', error);
        return {
            success: false,
            error
        };
    }
};
const cleanAndFixAllLocalStorageData = ()=>{
    try {
        console.log('🧹 بدء تنظيف وإصلاح جميع بيانات localStorage...');
        // Step 1: Remove invalid or orphaned items
        const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
        console.log("📊 البيانات الحالية:");
        console.log("📦 عناصر الفواتير: ".concat(salesItems.length));
        console.log("💊 الأدوية: ".concat(medicines.length));
        console.log("📋 الدفعات: ".concat(batches.length));
        console.log("🧾 الفواتير: ".concat(invoices.length));
        // Step 2: Remove duplicate items
        const uniqueItems = salesItems.filter((item, index, self)=>index === self.findIndex((i)=>i.id === item.id));
        // Step 3: Remove items with invalid invoice_id
        const validInvoiceIds = new Set(invoices.map((inv)=>inv.id));
        const itemsWithValidInvoices = uniqueItems.filter((item)=>validInvoiceIds.has(item.invoice_id));
        // Step 4: Fix medicine batch references
        const result = fixLocalStorageInvoiceItems();
        // Step 5: Clean up orphaned batches (batches without medicines)
        const validMedicineIds = new Set(medicines.map((med)=>med.id));
        const cleanBatches = batches.filter((batch)=>validMedicineIds.has(batch.medicine_id));
        // Save cleaned data
        localStorage.setItem('sales_invoice_items', JSON.stringify(itemsWithValidInvoices));
        localStorage.setItem('medicine_batches', JSON.stringify(cleanBatches));
        const removedItems = salesItems.length - itemsWithValidInvoices.length;
        const removedBatches = batches.length - cleanBatches.length;
        console.log("🧹 تم تنظيف البيانات:");
        console.log("❌ تم حذف ".concat(removedItems, " عنصر مكرر أو غير صالح"));
        console.log("❌ تم حذف ".concat(removedBatches, " دفعة يتيمة"));
        console.log("✅ تم الاحتفاظ بـ ".concat(itemsWithValidInvoices.length, " عنصر صالح"));
        return {
            success: true,
            removedItems,
            removedBatches,
            remainingItems: itemsWithValidInvoices.length,
            fixResult: result
        };
    } catch (error) {
        console.error('❌ خطأ في تنظيف البيانات:', error);
        return {
            success: false,
            error
        };
    }
};
const createSampleMedicinesIfNeeded = ()=>{
    try {
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        if (medicines.length === 0) {
            console.log('🏥 إنشاء أدوية تجريبية...');
            const sampleMedicines = [
                {
                    id: "medicine_".concat(Date.now(), "_1"),
                    name: 'باراسيتامول 500 مجم',
                    category: 'مسكنات',
                    manufacturer: 'شركة الأدوية المصرية',
                    active_ingredient: 'باراسيتامول',
                    strength: '500 مجم',
                    form: 'أقراص',
                    unit_price: 5.00,
                    selling_price: 8.00,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: "medicine_".concat(Date.now(), "_2"),
                    name: 'أموكسيسيلين 250 مجم',
                    category: 'مضادات حيوية',
                    manufacturer: 'شركة الأدوية العربية',
                    active_ingredient: 'أموكسيسيلين',
                    strength: '250 مجم',
                    form: 'كبسولات',
                    unit_price: 12.00,
                    selling_price: 18.00,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: "medicine_".concat(Date.now(), "_3"),
                    name: 'فيتامين سي 1000 مجم',
                    category: 'فيتامينات',
                    manufacturer: 'شركة الفيتامينات الطبيعية',
                    active_ingredient: 'حمض الأسكوربيك',
                    strength: '1000 مجم',
                    form: 'أقراص فوارة',
                    unit_price: 8.00,
                    selling_price: 12.00,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];
            localStorage.setItem('medicines', JSON.stringify(sampleMedicines));
            console.log("✅ تم إنشاء ".concat(sampleMedicines.length, " دواء تجريبي"));
            // Create sample batches for these medicines
            const sampleBatches = sampleMedicines.map((medicine, index)=>({
                    id: "batch_".concat(Date.now(), "_").concat(index + 1),
                    medicine_id: medicine.id,
                    batch_code: "BATCH".concat(String(index + 1).padStart(3, '0')),
                    expiry_date: new Date(Date.now() + (365 + index * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    quantity: 100 + index * 50,
                    cost_price: medicine.unit_price,
                    selling_price: medicine.selling_price,
                    supplier_id: null,
                    received_date: new Date().toISOString().split('T')[0],
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }));
            localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches));
            console.log("✅ تم إنشاء ".concat(sampleBatches.length, " دفعة تجريبية"));
            return {
                success: true,
                medicinesCreated: sampleMedicines.length,
                batchesCreated: sampleBatches.length
            };
        }
        return {
            success: true,
            medicinesCreated: 0,
            batchesCreated: 0
        };
    } catch (error) {
        console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
        return {
            success: false,
            error
        };
    }
};
const getMedicineNameFromBatch = (batchId)=>{
    try {
        const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
        const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
        const batch = batches.find((b)=>b.id === batchId);
        const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
        return (medicine === null || medicine === void 0 ? void 0 : medicine.name) || 'غير محدد';
    } catch (error) {
        console.error('خطأ في الحصول على اسم الدواء:', error);
        return 'غير محدد';
    }
};
const createPurchaseInvoice = async (invoiceData)=>{
    try {
        // Try Supabase first
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').insert([
            invoiceData
        ]).select().single();
        if (error) {
            console.warn('Supabase error for purchase invoice, using localStorage:', error);
            // Fallback to localStorage
            const invoiceId = "purchase_invoice_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
            const invoice = {
                id: invoiceId,
                ...invoiceData,
                created_at: new Date().toISOString()
            };
            // Save to localStorage
            const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
            existingInvoices.push(invoice);
            localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices));
            return {
                success: true,
                data: invoice
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error creating purchase invoice:', error);
        // Final fallback to localStorage
        try {
            const invoiceId = "purchase_invoice_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
            const invoice = {
                id: invoiceId,
                ...invoiceData,
                created_at: new Date().toISOString()
            };
            const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
            existingInvoices.push(invoice);
            localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices));
            return {
                success: true,
                data: invoice
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed for purchase invoice:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
const addPurchaseInvoiceItems = async (items)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoice_items').insert(items).select();
        if (error) {
            console.warn('Supabase error for purchase invoice items, using localStorage:', error);
            // Fallback to localStorage
            const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]');
            const newItems = items.map((item)=>({
                    id: "purchase_item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                    ...item,
                    medicine_name: item.medicine_name || 'غير محدد',
                    medicineName: item.medicine_name || 'غير محدد',
                    created_at: new Date().toISOString()
                }));
            existingItems.push(...newItems);
            localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems));
            return {
                success: true,
                data: newItems
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding purchase invoice items:', error);
        // Final fallback to localStorage
        try {
            const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]');
            const newItems = items.map((item)=>({
                    id: "purchase_item_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                    ...item,
                    medicine_name: item.medicine_name || 'غير محدد',
                    medicineName: item.medicine_name || 'غير محدد',
                    created_at: new Date().toISOString()
                }));
            existingItems.push(...newItems);
            localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems));
            return {
                success: true,
                data: newItems
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed for purchase items:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
const addInventoryMovement = async (movementData)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('inventory_movements').insert([
            movementData
        ]).select().single();
        if (error) {
            console.warn('Supabase error for inventory movement, using localStorage:', error);
            // Fallback to localStorage
            const movement = {
                id: "movement_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                ...movementData,
                created_at: new Date().toISOString()
            };
            const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]');
            existingMovements.push(movement);
            localStorage.setItem('inventory_movements', JSON.stringify(existingMovements));
            return {
                success: true,
                data: movement
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding inventory movement:', error);
        // Final fallback to localStorage
        try {
            const movement = {
                id: "movement_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                ...movementData,
                created_at: new Date().toISOString()
            };
            const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]');
            existingMovements.push(movement);
            localStorage.setItem('inventory_movements', JSON.stringify(existingMovements));
            return {
                success: true,
                data: movement
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed for inventory movement:', localError);
            return {
                success: false,
                error: localError
            };
        }
    }
};
const getCustomers = async ()=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('customers').select('*').order('name');
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching customers:', error);
        return {
            success: false,
            error
        };
    }
};
const addCustomer = async (customerData)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('customers').insert([
            customerData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding customer:', error);
        return {
            success: false,
            error
        };
    }
};
const getSuppliers = async ()=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('suppliers').select('*').order('name');
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching suppliers:', error);
        return {
            success: false,
            error
        };
    }
};
const addSupplier = async (supplierData)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('suppliers').insert([
            supplierData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error adding supplier:', error);
        return {
            success: false,
            error
        };
    }
};
const completeSalesTransaction = async (invoiceData, items)=>{
    try {
        console.log('🔄 بدء معاملة المبيعات الكاملة...');
        console.log('📄 بيانات الفاتورة:', invoiceData);
        console.log('📦 العناصر:', items);
        // Start transaction by creating invoice
        console.log('📝 إنشاء الفاتورة...');
        const invoiceResult = await createSalesInvoice(invoiceData);
        console.log('📝 نتيجة إنشاء الفاتورة:', invoiceResult);
        if (!invoiceResult.success) {
            var _invoiceResult_error;
            console.error('❌ فشل في إنشاء الفاتورة:', invoiceResult.error);
            throw new Error("فشل في إنشاء الفاتورة: ".concat(((_invoiceResult_error = invoiceResult.error) === null || _invoiceResult_error === void 0 ? void 0 : _invoiceResult_error.message) || 'خطأ غير معروف'));
        }
        const invoiceId = invoiceResult.data.id;
        console.log('✅ تم إنشاء الفاتورة بنجاح، ID:', invoiceId);
        // Process each item
        console.log('📦 معالجة عناصر الفاتورة...');
        const itemsToAdd = [];
        for (const item of items){
            console.log('📦 معالجة العنصر:', item);
            const batchId = item.medicine_batch_id || item.batchId;
            // Prepare item for batch insert with medicine name
            itemsToAdd.push({
                invoice_id: invoiceId,
                medicine_batch_id: batchId,
                quantity: item.quantity,
                unit_price: item.unit_price || item.unitPrice,
                total_price: item.total_price || item.totalPrice,
                is_gift: item.is_gift || item.isGift || false,
                medicine_name: item.medicine_name || item.medicineName || 'غير محدد'
            });
            // Update batch quantity (only for non-gift items)
            if (!(item.is_gift || item.isGift)) {
                try {
                    const currentBatch = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').select('quantity').eq('id', batchId).single();
                    if (currentBatch.data) {
                        const newQuantity = Math.max(0, currentBatch.data.quantity - item.quantity);
                        await updateBatchQuantity(batchId, newQuantity);
                        console.log("✅ تم تحديث كمية الدفعة ".concat(batchId, " إلى ").concat(newQuantity));
                    }
                } catch (batchError) {
                    console.warn('تحذير: فشل في تحديث كمية الدفعة:', batchError);
                }
            }
            // Add inventory movement
            try {
                await addInventoryMovement({
                    medicine_batch_id: batchId,
                    movement_type: 'out',
                    quantity: item.quantity,
                    reference_type: 'sale',
                    reference_id: invoiceId,
                    notes: item.is_gift || item.isGift ? 'هدية' : undefined
                });
                console.log("✅ تم إضافة حركة المخزون للدفعة ".concat(batchId));
            } catch (movementError) {
                console.warn('تحذير: فشل في إضافة حركة المخزون:', movementError);
            }
        }
        // Add all invoice items in batch
        console.log('📝 إضافة عناصر الفاتورة...');
        const itemsResult = await addSalesInvoiceItems(itemsToAdd);
        if (!itemsResult.success) {
            console.warn('تحذير: فشل في إضافة عناصر الفاتورة:', itemsResult.error);
        } else {
            console.log('✅ تم إضافة جميع عناصر الفاتورة بنجاح');
        }
        // Add cash transaction if payment is cash
        if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {
            try {
                await addCashTransaction({
                    transaction_type: 'income',
                    category: 'مبيعات',
                    amount: invoiceData.final_amount,
                    description: "فاتورة مبيعات رقم ".concat(invoiceData.invoice_number),
                    reference_type: 'sale',
                    reference_id: invoiceId,
                    payment_method: 'cash',
                    notes: invoiceData.notes
                });
                console.log('✅ تم إضافة معاملة الصندوق');
            } catch (cashError) {
                console.warn('تحذير: فشل في إضافة معاملة الصندوق:', cashError);
            }
        }
        console.log('🎉 تمت معاملة المبيعات بنجاح!');
        return {
            success: true,
            data: {
                invoiceId
            }
        };
    } catch (error) {
        console.error('❌ خطأ في إتمام معاملة المبيعات:', error);
        return {
            success: false,
            error
        };
    }
};
const getSalesInvoices = async ()=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').select("\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").order('created_at', {
            ascending: false
        });
        if (error) {
            console.warn('Supabase error for sales invoices, using localStorage fallback:', error);
            // Fallback to localStorage
            const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            // Combine invoices with their items and ensure medicine names are available
            const invoicesWithItems = localInvoices.map((invoice)=>{
                const items = localItems.filter((item)=>item.invoice_id === invoice.id);
                // Enhance items with medicine names if not already present
                const enhancedItems = items.map((item)=>{
                    var _item_medicine_batches_medicines, _item_medicine_batches;
                    if ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) {
                        return item // Already has medicine name
                        ;
                    }
                    // Find medicine name from batch
                    const batch = batches.find((b)=>b.id === item.medicine_batch_id);
                    const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
                    return {
                        ...item,
                        medicine_name: (medicine === null || medicine === void 0 ? void 0 : medicine.name) || item.medicine_name || 'غير محدد',
                        medicine_batches: {
                            batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || '',
                            expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || '',
                            medicines: {
                                name: (medicine === null || medicine === void 0 ? void 0 : medicine.name) || item.medicine_name || 'غير محدد',
                                category: (medicine === null || medicine === void 0 ? void 0 : medicine.category) || '',
                                manufacturer: (medicine === null || medicine === void 0 ? void 0 : medicine.manufacturer) || '',
                                strength: (medicine === null || medicine === void 0 ? void 0 : medicine.strength) || '',
                                form: (medicine === null || medicine === void 0 ? void 0 : medicine.form) || ''
                            }
                        }
                    };
                });
                return {
                    ...invoice,
                    sales_invoice_items: enhancedItems
                };
            });
            return {
                success: true,
                data: invoicesWithItems
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching sales invoices:', error);
        // Final fallback to localStorage
        try {
            const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            // Combine invoices with their items and ensure medicine names
            const invoicesWithItems = localInvoices.map((invoice)=>{
                const items = localItems.filter((item)=>item.invoice_id === invoice.id);
                // Enhance items with medicine names if not already present
                const enhancedItems = items.map((item)=>{
                    var _item_medicine_batches_medicines, _item_medicine_batches;
                    if ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) {
                        return item // Already has medicine name
                        ;
                    }
                    // Find medicine name from batch
                    const batch = batches.find((b)=>b.id === item.medicine_batch_id);
                    const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
                    return {
                        ...item,
                        medicine_name: (medicine === null || medicine === void 0 ? void 0 : medicine.name) || item.medicine_name || 'غير محدد',
                        medicine_batches: {
                            batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || '',
                            expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || '',
                            medicines: {
                                name: (medicine === null || medicine === void 0 ? void 0 : medicine.name) || item.medicine_name || 'غير محدد',
                                category: (medicine === null || medicine === void 0 ? void 0 : medicine.category) || '',
                                manufacturer: (medicine === null || medicine === void 0 ? void 0 : medicine.manufacturer) || '',
                                strength: (medicine === null || medicine === void 0 ? void 0 : medicine.strength) || '',
                                form: (medicine === null || medicine === void 0 ? void 0 : medicine.form) || ''
                            }
                        }
                    };
                });
                return {
                    ...invoice,
                    sales_invoice_items: enhancedItems
                };
            });
            return {
                success: true,
                data: invoicesWithItems
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed:', localError);
            return {
                success: false,
                error
            };
        }
    }
};
const getSalesInvoiceForPrint = async (invoiceId)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').select("\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").eq('id', invoiceId).single();
        if (error) {
            console.warn('Supabase error for single invoice, using localStorage fallback:', error);
            // Fallback to localStorage
            const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            const invoice = localInvoices.find((inv)=>inv.id === invoiceId);
            if (invoice) {
                const items = localItems.filter((item)=>item.invoice_id === invoiceId);
                // Enhance items with medicine names - FORCE REFRESH
                console.log('🔧 بدء تحسين عناصر الفاتورة للطباعة...');
                console.log('📦 عدد العناصر:', items.length);
                console.log('💊 عدد الأدوية المتاحة:', medicines.length);
                console.log('📋 عدد الدفعات المتاحة:', batches.length);
                const itemsWithNames = items.map((item, index)=>{
                    console.log("\n--- العنصر ".concat(index + 1, " ---"));
                    console.log('البيانات الأصلية:', item);
                    // Find medicine name from batch - ALWAYS recalculate
                    const batch = batches.find((b)=>b.id === item.medicine_batch_id);
                    console.log('الدفعة الموجودة:', batch);
                    const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
                    console.log('الدواء الموجود:', medicine);
                    // Get the best available medicine name
                    const medicineName = (medicine === null || medicine === void 0 ? void 0 : medicine.name) || 'غير محدد';
                    console.log('اسم الدواء المحسوب:', medicineName);
                    const enhancedItem = {
                        ...item,
                        medicine_name: medicineName,
                        medicineName: medicineName,
                        medicine_batches: {
                            id: batch === null || batch === void 0 ? void 0 : batch.id,
                            batch_code: (batch === null || batch === void 0 ? void 0 : batch.batch_code) || item.batch_code || '',
                            expiry_date: (batch === null || batch === void 0 ? void 0 : batch.expiry_date) || item.expiry_date || '',
                            medicine_id: batch === null || batch === void 0 ? void 0 : batch.medicine_id,
                            medicines: {
                                id: medicine === null || medicine === void 0 ? void 0 : medicine.id,
                                name: medicineName,
                                category: (medicine === null || medicine === void 0 ? void 0 : medicine.category) || '',
                                manufacturer: (medicine === null || medicine === void 0 ? void 0 : medicine.manufacturer) || '',
                                strength: (medicine === null || medicine === void 0 ? void 0 : medicine.strength) || '',
                                form: (medicine === null || medicine === void 0 ? void 0 : medicine.form) || ''
                            }
                        }
                    };
                    console.log('العنصر المحسن:', enhancedItem);
                    return enhancedItem;
                });
                console.log('✅ تم تحسين جميع العناصر');
                console.log('النتيجة النهائية:', itemsWithNames);
                return {
                    success: true,
                    data: {
                        ...invoice,
                        sales_invoice_items: itemsWithNames
                    }
                };
            }
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching sales invoice for print:', error);
        return {
            success: false,
            error
        };
    }
};
const getPurchaseInvoiceForPrint = async (invoiceId)=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").eq('id', invoiceId).single();
        if (error) {
            console.warn('Supabase error for single purchase invoice, using localStorage fallback:', error);
            // Fallback to localStorage
            const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]');
            const invoice = localInvoices.find((inv)=>inv.id === invoiceId);
            if (invoice) {
                const items = localItems.filter((item)=>item.invoice_id === invoiceId);
                // Enhance items with medicine names
                console.log('🔧 بدء تحسين عناصر فاتورة المشتريات للطباعة...');
                console.log('📦 عدد العناصر:', items.length);
                const itemsWithNames = items.map((item, index)=>{
                    console.log("\n--- العنصر ".concat(index + 1, " ---"));
                    console.log('البيانات الأصلية:', item);
                    // Use existing medicine name if available
                    const medicineName = item.medicine_name || item.medicineName || 'غير محدد';
                    console.log('اسم الدواء:', medicineName);
                    const enhancedItem = {
                        ...item,
                        medicine_name: medicineName,
                        medicineName: medicineName,
                        medicines: {
                            name: medicineName,
                            category: item.category || '',
                            manufacturer: item.manufacturer || '',
                            strength: item.strength || '',
                            form: item.form || ''
                        }
                    };
                    console.log('العنصر المحسن:', enhancedItem);
                    return enhancedItem;
                });
                console.log('✅ تم تحسين جميع عناصر المشتريات');
                return {
                    success: true,
                    data: {
                        ...invoice,
                        purchase_invoice_items: itemsWithNames
                    }
                };
            }
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching purchase invoice for print:', error);
        return {
            success: false,
            error
        };
    }
};
const getPurchaseInvoices = async ()=>{
    try {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").order('created_at', {
            ascending: false
        });
        if (error) {
            console.warn('Supabase error for purchase invoices, using localStorage fallback:', error);
            // Fallback to localStorage
            const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]');
            // Combine invoices with their items
            const invoicesWithItems = localInvoices.map((invoice)=>({
                    ...invoice,
                    purchase_invoice_items: localItems.filter((item)=>item.invoice_id === invoice.id)
                }));
            return {
                success: true,
                data: invoicesWithItems
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error('Error fetching purchase invoices:', error);
        // Final fallback to localStorage
        try {
            const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
            const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]');
            // Combine invoices with their items
            const invoicesWithItems = localInvoices.map((invoice)=>({
                    ...invoice,
                    purchase_invoice_items: localItems.filter((item)=>item.invoice_id === invoice.id)
                }));
            return {
                success: true,
                data: invoicesWithItems
            };
        } catch (localError) {
            console.error('LocalStorage fallback failed:', localError);
            return {
                success: false,
                error
            };
        }
    }
};
const createSalesReturn = async (returnData)=>{
    try {
        // Try Supabase first
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').insert([
            returnData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase sales return failed, using localStorage fallback:', error);
        // Fallback to localStorage
        try {
            const returnWithId = {
                ...returnData,
                id: "sr_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                created_at: new Date().toISOString()
            };
            const existingReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]');
            existingReturns.push(returnWithId);
            localStorage.setItem('sales_returns', JSON.stringify(existingReturns));
            console.log('Sales return saved to localStorage:', returnWithId);
            console.log('Total sales returns in localStorage:', existingReturns.length);
            return {
                success: true,
                data: returnWithId
            };
        } catch (fallbackError) {
            console.error('Error creating sales return (fallback):', fallbackError);
            return {
                success: false,
                error: fallbackError
            };
        }
    }
};
const createPurchaseReturn = async (returnData)=>{
    try {
        // Try Supabase first
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').insert([
            returnData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase purchase return failed, using localStorage fallback:', error);
        // Fallback to localStorage
        try {
            const returnWithId = {
                ...returnData,
                id: "pr_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                created_at: new Date().toISOString()
            };
            const existingReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]');
            existingReturns.push(returnWithId);
            localStorage.setItem('purchase_returns', JSON.stringify(existingReturns));
            console.log('Purchase return saved to localStorage:', returnWithId);
            console.log('Total purchase returns in localStorage:', existingReturns.length);
            return {
                success: true,
                data: returnWithId
            };
        } catch (fallbackError) {
            console.error('Error creating purchase return (fallback):', fallbackError);
            return {
                success: false,
                error: fallbackError
            };
        }
    }
};
const addReturnItems = async (items)=>{
    try {
        var _items_;
        // Try Supabase first
        const tableName = ((_items_ = items[0]) === null || _items_ === void 0 ? void 0 : _items_.return_type) === 'sales' ? 'sales_return_items' : 'purchase_return_items';
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(tableName).insert(items.map((item)=>({
                return_id: item.return_id,
                medicine_batch_id: item.medicine_batch_id,
                medicine_id: item.medicine_id,
                quantity: item.quantity,
                unit_price: item.unit_price,
                total_price: item.total_price
            }))).select();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase return items failed, using localStorage fallback:', error);
        // Fallback to localStorage
        try {
            var _items_1;
            const storageKey = ((_items_1 = items[0]) === null || _items_1 === void 0 ? void 0 : _items_1.return_type) === 'sales' ? 'sales_return_items' : 'purchase_return_items';
            const itemsWithIds = items.map((item)=>({
                    ...item,
                    id: "ri_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                    created_at: new Date().toISOString()
                }));
            const existingItems = JSON.parse(localStorage.getItem(storageKey) || '[]');
            existingItems.push(...itemsWithIds);
            localStorage.setItem(storageKey, JSON.stringify(existingItems));
            return {
                success: true,
                data: itemsWithIds
            };
        } catch (fallbackError) {
            console.error('Error adding return items (fallback):', fallbackError);
            return {
                success: false,
                error: fallbackError
            };
        }
    }
};
const processReturn = async (returnType, returnData, items)=>{
    try {
        // Create return record
        const returnResult = returnType === 'sales' ? await createSalesReturn(returnData) : await createPurchaseReturn(returnData);
        if (!returnResult.success) throw new Error('Failed to create return');
        const returnId = returnResult.data.id;
        // Add return items
        const returnItems = items.map((item)=>({
                return_id: returnId,
                return_type: returnType,
                medicine_batch_id: item.batchId,
                medicine_id: item.medicineId,
                quantity: item.quantity,
                unit_price: item.unitPrice,
                total_price: item.totalPrice
            }));
        await addReturnItems(returnItems);
        // Try to update inventory (skip if Supabase is not available)
        try {
            // Update inventory for sales returns (add back to stock)
            if (returnType === 'sales') {
                for (const item of items){
                    if (item.batchId) {
                        try {
                            // Get current batch quantity
                            const { data: batch } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').select('quantity').eq('id', item.batchId).single();
                            if (batch) {
                                // Add returned quantity back to stock
                                await updateBatchQuantity(item.batchId, batch.quantity + item.quantity);
                            }
                            // Add inventory movement
                            await addInventoryMovement({
                                medicine_batch_id: item.batchId,
                                movement_type: 'in',
                                quantity: item.quantity,
                                reference_type: 'return',
                                reference_id: returnId,
                                notes: "مرتجع مبيعات - ".concat(returnData.reason)
                            });
                        } catch (inventoryError) {
                            console.warn('Failed to update inventory for item:', item.batchId, inventoryError);
                        }
                    }
                }
            }
            // Update inventory for purchase returns (remove from stock)
            if (returnType === 'purchase') {
                for (const item of items){
                    if (item.batchId) {
                        try {
                            // Get current batch quantity
                            const { data: batch } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').select('quantity').eq('id', item.batchId).single();
                            if (batch) {
                                // Remove returned quantity from stock
                                const newQuantity = Math.max(0, batch.quantity - item.quantity);
                                await updateBatchQuantity(item.batchId, newQuantity);
                            }
                            // Add inventory movement
                            await addInventoryMovement({
                                medicine_batch_id: item.batchId,
                                movement_type: 'out',
                                quantity: item.quantity,
                                reference_type: 'return',
                                reference_id: returnId,
                                notes: "مرتجع مشتريات - ".concat(returnData.reason)
                            });
                        } catch (inventoryError) {
                            console.warn('Failed to update inventory for item:', item.batchId, inventoryError);
                        }
                    }
                }
            }
        } catch (inventoryError) {
            console.warn('Inventory update failed, but return was created successfully:', inventoryError);
        }
        return {
            success: true,
            data: {
                returnId
            }
        };
    } catch (error) {
        console.error('Error processing return:', error);
        return {
            success: false,
            error
        };
    }
};
const getReturns = async ()=>{
    // Always try localStorage first for faster response
    try {
        const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]');
        const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]');
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
        console.log('Loading returns from localStorage:', {
            salesReturns: salesReturns.length,
            purchaseReturns: purchaseReturns.length,
            customers: customers.length,
            suppliers: suppliers.length
        });
        // Enrich sales returns with customer data
        const enrichedSalesReturns = salesReturns.map((returnItem)=>{
            var _returnItem_return_items;
            const customer = customers.find((c)=>c.id === returnItem.customer_id);
            console.log("Enriching sales return ".concat(returnItem.id, ":"), {
                original_items: returnItem.return_items,
                items_count: ((_returnItem_return_items = returnItem.return_items) === null || _returnItem_return_items === void 0 ? void 0 : _returnItem_return_items.length) || 0
            });
            return {
                ...returnItem,
                return_type: 'sales',
                customers: customer ? {
                    name: customer.name,
                    phone: customer.phone,
                    address: customer.address
                } : null,
                customer_name: (customer === null || customer === void 0 ? void 0 : customer.name) || returnItem.customer_name || 'عميل غير محدد',
                // تأكد من وجود المواد
                return_items: returnItem.return_items || []
            };
        });
        // Enrich purchase returns with supplier data
        const enrichedPurchaseReturns = purchaseReturns.map((returnItem)=>{
            var _returnItem_return_items;
            const supplier = suppliers.find((s)=>s.id === returnItem.supplier_id);
            console.log("Enriching purchase return ".concat(returnItem.id, ":"), {
                original_items: returnItem.return_items,
                items_count: ((_returnItem_return_items = returnItem.return_items) === null || _returnItem_return_items === void 0 ? void 0 : _returnItem_return_items.length) || 0
            });
            return {
                ...returnItem,
                return_type: 'purchase',
                suppliers: supplier ? {
                    name: supplier.name,
                    phone: supplier.phone,
                    address: supplier.address
                } : null,
                supplier_name: (supplier === null || supplier === void 0 ? void 0 : supplier.name) || returnItem.supplier_name || 'مورد غير محدد',
                // تأكد من وجود المواد
                return_items: returnItem.return_items || []
            };
        });
        // If we have local data, return it immediately
        if (enrichedSalesReturns.length > 0 || enrichedPurchaseReturns.length > 0) {
            const allReturns = [
                ...enrichedSalesReturns,
                ...enrichedPurchaseReturns
            ].sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            console.log('Returning enriched returns from localStorage:', allReturns.slice(0, 2));
            return {
                success: true,
                data: allReturns
            };
        }
    } catch (localError) {
        console.warn('Error reading from localStorage:', localError);
    }
    // If no local data, try Supabase
    try {
        console.log('Trying Supabase for returns...');
        const [salesReturns, purchaseReturns] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').select("\n          *,\n          customers (name, phone),\n          sales_return_items (\n            *,\n            medicine_batches (\n              batch_code,\n              medicines (name)\n            )\n          )\n        ").order('created_at', {
                ascending: false
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').select("\n          *,\n          suppliers (name, contact_person),\n          purchase_return_items (\n            *,\n            medicines (name)\n          )\n        ").order('created_at', {
                ascending: false
            })
        ]);
        const allReturns = [
            ...(salesReturns.data || []).map((item)=>{
                var _item_customers;
                return {
                    ...item,
                    return_type: 'sales',
                    customer_name: ((_item_customers = item.customers) === null || _item_customers === void 0 ? void 0 : _item_customers.name) || 'عميل غير محدد'
                };
            }),
            ...(purchaseReturns.data || []).map((item)=>{
                var _item_suppliers;
                return {
                    ...item,
                    return_type: 'purchase',
                    supplier_name: ((_item_suppliers = item.suppliers) === null || _item_suppliers === void 0 ? void 0 : _item_suppliers.name) || 'مورد غير محدد'
                };
            })
        ].sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        console.log('Returning returns from Supabase:', allReturns.slice(0, 2));
        return {
            success: true,
            data: allReturns
        };
    } catch (error) {
        console.warn('Supabase returns failed, returning empty array:', error);
        // Return empty array if both localStorage and Supabase fail
        return {
            success: true,
            data: []
        };
    }
};
const getReturnById = async (returnId)=>{
    try {
        // Try localStorage first
        const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]');
        const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]');
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
        let foundReturn = salesReturns.find((r)=>r.id === returnId);
        let returnType = 'sales';
        if (!foundReturn) {
            foundReturn = purchaseReturns.find((r)=>r.id === returnId);
            returnType = 'purchase';
        }
        if (foundReturn) {
            // Enrich with customer/supplier data
            if (returnType === 'sales') {
                const customer = customers.find((c)=>c.id === foundReturn.customer_id);
                foundReturn = {
                    ...foundReturn,
                    return_type: 'sales',
                    customers: customer ? {
                        name: customer.name,
                        phone: customer.phone,
                        address: customer.address
                    } : null,
                    customer_name: (customer === null || customer === void 0 ? void 0 : customer.name) || foundReturn.customer_name || 'عميل غير محدد'
                };
            } else {
                const supplier = suppliers.find((s)=>s.id === foundReturn.supplier_id);
                foundReturn = {
                    ...foundReturn,
                    return_type: 'purchase',
                    suppliers: supplier ? {
                        name: supplier.name,
                        phone: supplier.phone,
                        address: supplier.address
                    } : null,
                    supplier_name: (supplier === null || supplier === void 0 ? void 0 : supplier.name) || foundReturn.supplier_name || 'مورد غير محدد'
                };
            }
            console.log('Found enriched return in localStorage:', foundReturn);
            return {
                success: true,
                data: foundReturn
            };
        }
        // If not in localStorage, try Supabase
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"]) {
            console.warn('Supabase not available, return not found');
            return {
                success: false,
                error: 'Return not found'
            };
        }
        // Try sales returns first
        const { data: salesReturn, error: salesError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').select("\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            *,\n            medicines (name)\n          )\n        )\n      ").eq('id', returnId).single();
        if (salesReturn && !salesError) {
            const returnData = {
                ...salesReturn,
                return_type: 'sales',
                return_items: salesReturn.sales_return_items || []
            };
            console.log('Found sales return in Supabase:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        // Try purchase returns
        const { data: purchaseReturn, error: purchaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').select("\n        *,\n        suppliers (name, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name)\n        )\n      ").eq('id', returnId).single();
        if (purchaseReturn && !purchaseError) {
            const returnData = {
                ...purchaseReturn,
                return_type: 'purchase',
                return_items: purchaseReturn.purchase_return_items || []
            };
            console.log('Found purchase return in Supabase:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        console.warn('Return not found:', returnId);
        return {
            success: false,
            error: 'Return not found'
        };
    } catch (error) {
        console.error('Error getting return by ID:', error);
        return {
            success: false,
            error
        };
    }
};
const completePurchaseTransaction = async (invoiceData, items)=>{
    try {
        // Create purchase invoice
        const invoiceResult = await createPurchaseInvoice(invoiceData);
        if (!invoiceResult.success) throw new Error('Failed to create purchase invoice');
        const invoiceId = invoiceResult.data.id;
        // Process each item
        for (const item of items){
            let medicineId = item.medicineId;
            // If medicine doesn't exist, create it
            if (!medicineId) {
                const newMedicineResult = await addMedicine({
                    name: item.medicineName,
                    category: item.category || 'أخرى',
                    manufacturer: item.manufacturer || '',
                    active_ingredient: item.activeIngredient || '',
                    strength: item.strength || '',
                    form: item.form || 'tablet',
                    unit_price: item.unitCost,
                    selling_price: item.sellingPrice || item.unitCost * 1.5
                });
                if (newMedicineResult.success) {
                    medicineId = newMedicineResult.data.id;
                } else {
                    console.error('Failed to create medicine:', newMedicineResult.error);
                    continue;
                }
            }
            // Add purchase invoice item
            await addPurchaseInvoiceItems([
                {
                    invoice_id: invoiceId,
                    medicine_id: medicineId,
                    batch_code: item.batchCode,
                    quantity: item.quantity,
                    unit_cost: item.unitCost,
                    total_cost: item.totalCost,
                    expiry_date: item.expiryDate,
                    medicine_name: item.medicineName || 'غير محدد'
                }
            ]);
            // Create or update medicine batch
            const batchResult = await addMedicineBatch({
                medicine_id: medicineId,
                batch_code: item.batchCode,
                expiry_date: item.expiryDate,
                quantity: item.quantity,
                cost_price: item.unitCost,
                selling_price: item.sellingPrice || item.unitCost * 1.5,
                supplier_id: invoiceData.supplier_id
            });
            if (batchResult.success) {
                // Add inventory movement
                await addInventoryMovement({
                    medicine_batch_id: batchResult.data.id,
                    movement_type: 'in',
                    quantity: item.quantity,
                    reference_type: 'purchase',
                    reference_id: invoiceId
                });
            }
        }
        // Add cash transaction if payment is cash
        if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {
            await addCashTransaction({
                transaction_type: 'expense',
                category: 'مشتريات',
                amount: invoiceData.final_amount,
                description: "فاتورة مشتريات رقم ".concat(invoiceData.invoice_number),
                reference_type: 'purchase',
                reference_id: invoiceId,
                payment_method: 'cash',
                notes: invoiceData.notes
            });
        }
        return {
            success: true,
            data: {
                invoiceId
            }
        };
    } catch (error) {
        console.error('Error completing purchase transaction:', error);
        return {
            success: false,
            error
        };
    }
};
const addCashTransaction = async (transactionData)=>{
    try {
        // Try Supabase first
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('cash_transactions').insert([
            transactionData
        ]).select().single();
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase cash transaction failed, using localStorage fallback:', error);
        // Fallback to localStorage
        try {
            const transactionWithId = {
                ...transactionData,
                id: "ct_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
                created_at: new Date().toISOString()
            };
            const existingTransactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]');
            existingTransactions.push(transactionWithId);
            localStorage.setItem('cash_transactions', JSON.stringify(existingTransactions));
            console.log('Cash transaction saved to localStorage:', transactionWithId);
            console.log('Total cash transactions in localStorage:', existingTransactions.length);
            return {
                success: true,
                data: transactionWithId
            };
        } catch (fallbackError) {
            console.error('Error adding cash transaction (fallback):', fallbackError);
            return {
                success: false,
                error: fallbackError
            };
        }
    }
};
const getCashTransactions = async (filters)=>{
    // Always try localStorage first for faster response
    try {
        const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]');
        // If we have local data, filter and return it
        if (transactions.length > 0) {
            console.log('Loading cash transactions from localStorage:', transactions.length);
            let filteredTransactions = transactions;
            if (filters === null || filters === void 0 ? void 0 : filters.start_date) {
                filteredTransactions = filteredTransactions.filter((t)=>t.created_at >= filters.start_date);
            }
            if (filters === null || filters === void 0 ? void 0 : filters.end_date) {
                filteredTransactions = filteredTransactions.filter((t)=>t.created_at <= filters.end_date);
            }
            if (filters === null || filters === void 0 ? void 0 : filters.transaction_type) {
                filteredTransactions = filteredTransactions.filter((t)=>t.transaction_type === filters.transaction_type);
            }
            if (filters === null || filters === void 0 ? void 0 : filters.category) {
                filteredTransactions = filteredTransactions.filter((t)=>t.category === filters.category);
            }
            // Sort by created_at descending
            filteredTransactions.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            return {
                success: true,
                data: filteredTransactions
            };
        }
    } catch (localError) {
        console.warn('Error reading cash transactions from localStorage:', localError);
    }
    // If no local data, try Supabase
    try {
        console.log('Trying Supabase for cash transactions...');
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('cash_transactions').select('*').order('created_at', {
            ascending: false
        });
        if (filters === null || filters === void 0 ? void 0 : filters.start_date) {
            query = query.gte('created_at', filters.start_date);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.end_date) {
            query = query.lte('created_at', filters.end_date);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.transaction_type) {
            query = query.eq('transaction_type', filters.transaction_type);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.category) {
            query = query.eq('category', filters.category);
        }
        const { data, error } = await query;
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase cash transactions failed, returning empty array:', error);
        return {
            success: true,
            data: []
        };
    }
};
const getCashBalance = async ()=>{
    // Try localStorage first
    try {
        const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]');
        if (transactions.length > 0) {
            console.log('Calculating cash balance from localStorage:', transactions.length, 'transactions');
            const balance = transactions.reduce((total, transaction)=>{
                return transaction.transaction_type === 'income' ? total + transaction.amount : total - transaction.amount;
            }, 0);
            return {
                success: true,
                data: balance
            };
        }
    } catch (localError) {
        console.warn('Error calculating balance from localStorage:', localError);
    }
    // If no local data, try Supabase
    try {
        console.log('Trying Supabase for cash balance...');
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('cash_transactions').select('transaction_type, amount');
        if (error) throw error;
        const balance = data.reduce((total, transaction)=>{
            return transaction.transaction_type === 'income' ? total + transaction.amount : total - transaction.amount;
        }, 0);
        return {
            success: true,
            data: balance
        };
    } catch (error) {
        console.warn('Supabase cash balance failed, returning 0:', error);
        return {
            success: true,
            data: 0
        };
    }
};
const getCustomerDebts = async ()=>{
    // Try localStorage first
    try {
        const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
        if (salesInvoices.length > 0) {
            console.log('Loading customer debts from localStorage:', salesInvoices.length, 'invoices');
            // Filter for pending payments only
            const pendingInvoices = salesInvoices.filter((invoice)=>invoice.payment_status === 'pending' || invoice.payment_status === 'partial');
            console.log('Found customer debts:', pendingInvoices.length);
            return {
                success: true,
                data: pendingInvoices
            };
        } else {
            // Create sample debt data if no invoices exist
            console.log('No sales invoices found, creating sample customer debts');
            const sampleDebts = [
                {
                    id: 'debt_1',
                    invoice_number: 'INV-001',
                    customer_id: 'cust_1',
                    customer_name: 'أحمد محمد علي',
                    final_amount: 150000,
                    payment_status: 'pending',
                    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    customers: {
                        name: 'أحمد محمد علي',
                        phone: '07901111111'
                    }
                },
                {
                    id: 'debt_2',
                    invoice_number: 'INV-003',
                    customer_id: 'cust_2',
                    customer_name: 'فاطمة حسن محمد',
                    final_amount: 85000,
                    payment_status: 'partial',
                    created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                    customers: {
                        name: 'فاطمة حسن محمد',
                        phone: '07802222222'
                    }
                }
            ];
            return {
                success: true,
                data: sampleDebts
            };
        }
    } catch (localError) {
        console.warn('Error reading customer debts from localStorage:', localError);
    }
    // If no local data, try Supabase
    try {
        console.log('Trying Supabase for customer debts...');
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').select("\n        id,\n        invoice_number,\n        customer_id,\n        customer_name,\n        final_amount,\n        payment_status,\n        created_at,\n        customers (name, phone)\n      ").eq('payment_status', 'pending').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase customer debts failed, returning empty array:', error);
        return {
            success: true,
            data: []
        };
    }
};
const getSupplierDebts = async ()=>{
    // Try localStorage first
    try {
        const purchaseInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]');
        if (purchaseInvoices.length > 0) {
            console.log('Loading supplier debts from localStorage:', purchaseInvoices.length, 'invoices');
            // Filter for pending payments only
            const pendingInvoices = purchaseInvoices.filter((invoice)=>invoice.payment_status === 'pending' || invoice.payment_status === 'partial');
            console.log('Found supplier debts:', pendingInvoices.length);
            return {
                success: true,
                data: pendingInvoices
            };
        } else {
            // Create sample debt data if no invoices exist
            console.log('No purchase invoices found, creating sample supplier debts');
            const sampleDebts = [
                {
                    id: 'debt_3',
                    invoice_number: 'PUR-001',
                    supplier_id: 'sup_1',
                    final_amount: 2500000,
                    payment_status: 'pending',
                    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                    suppliers: {
                        name: 'شركة الأدوية العراقية',
                        contact_person: 'أحمد محمد',
                        phone: '07901234567'
                    }
                },
                {
                    id: 'debt_4',
                    invoice_number: 'PUR-004',
                    supplier_id: 'sup_2',
                    final_amount: 1800000,
                    payment_status: 'partial',
                    created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    suppliers: {
                        name: 'شركة بغداد للأدوية',
                        contact_person: 'فاطمة علي',
                        phone: '07801234567'
                    }
                }
            ];
            return {
                success: true,
                data: sampleDebts
            };
        }
    } catch (localError) {
        console.warn('Error reading supplier debts from localStorage:', localError);
    }
    // If no local data, try Supabase
    try {
        console.log('Trying Supabase for supplier debts...');
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').select("\n        id,\n        invoice_number,\n        supplier_id,\n        final_amount,\n        payment_status,\n        created_at,\n        suppliers (name, contact_person, phone)\n      ").eq('payment_status', 'pending').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase supplier debts failed, returning empty array:', error);
        return {
            success: true,
            data: []
        };
    }
};
const updatePaymentStatus = async (invoiceType, invoiceId, paymentStatus, paidAmount)=>{
    try {
        // Try Supabase first
        const tableName = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices';
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(tableName).update({
            payment_status: paymentStatus,
            ...paidAmount && {
                paid_amount: paidAmount
            }
        }).eq('id', invoiceId).select().single();
        if (error) throw error;
        // Add cash transaction if payment is completed
        if (paymentStatus === 'paid' && paidAmount) {
            const transactionType = invoiceType === 'sales' ? 'income' : 'expense';
            const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات';
            await addCashTransaction({
                transaction_type: transactionType,
                category,
                amount: paidAmount,
                description: "دفع فاتورة ".concat(invoiceType === 'sales' ? 'مبيعات' : 'مشتريات', " رقم ").concat(data.invoice_number),
                reference_type: invoiceType,
                reference_id: invoiceId,
                payment_method: 'cash'
            });
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.warn('Supabase payment update failed, using localStorage fallback:', error);
        // Fallback to localStorage
        try {
            const storageKey = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices';
            const invoices = JSON.parse(localStorage.getItem(storageKey) || '[]');
            const invoiceIndex = invoices.findIndex((inv)=>inv.id === invoiceId);
            if (invoiceIndex !== -1) {
                invoices[invoiceIndex].payment_status = paymentStatus;
                if (paidAmount) {
                    invoices[invoiceIndex].paid_amount = paidAmount;
                }
                localStorage.setItem(storageKey, JSON.stringify(invoices));
                // Add cash transaction if payment is completed
                if (paymentStatus === 'paid' && paidAmount) {
                    const transactionType = invoiceType === 'sales' ? 'income' : 'expense';
                    const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات';
                    await addCashTransaction({
                        transaction_type: transactionType,
                        category,
                        amount: paidAmount,
                        description: "دفع فاتورة ".concat(invoiceType === 'sales' ? 'مبيعات' : 'مشتريات', " رقم ").concat(invoices[invoiceIndex].invoice_number),
                        reference_type: invoiceType,
                        reference_id: invoiceId,
                        payment_method: 'cash'
                    });
                }
                console.log('Payment status updated in localStorage:', invoices[invoiceIndex]);
                return {
                    success: true,
                    data: invoices[invoiceIndex]
                };
            } else {
                throw new Error('Invoice not found in localStorage');
            }
        } catch (fallbackError) {
            console.error('Error updating payment status (fallback):', fallbackError);
            return {
                success: false,
                error: fallbackError
            };
        }
    }
};
const getSalesReport = async (filters)=>{
    try {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').select("\n        *,\n        customers (name, phone),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            medicines (name, category)\n          )\n        )\n      ").order('created_at', {
            ascending: false
        });
        if (filters.start_date) {
            query = query.gte('created_at', filters.start_date);
        }
        if (filters.end_date) {
            query = query.lte('created_at', filters.end_date);
        }
        if (filters.customer_id) {
            query = query.eq('customer_id', filters.customer_id);
        }
        const { data, error } = await query;
        if (error) throw error;
        // Filter by medicine if specified
        let filteredData = data;
        if (filters.medicine_id) {
            filteredData = data.filter((invoice)=>invoice.sales_invoice_items.some((item)=>{
                    var _item_medicine_batches_medicines, _item_medicine_batches;
                    return ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.id) === filters.medicine_id;
                }));
        }
        return {
            success: true,
            data: filteredData
        };
    } catch (error) {
        console.error('Error fetching sales report:', error);
        return {
            success: false,
            error
        };
    }
};
const getPurchasesReport = async (filters)=>{
    try {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').select("\n        *,\n        suppliers (name, contact_person, phone),\n        purchase_invoice_items (\n          *,\n          medicines (name, category)\n        )\n      ").order('created_at', {
            ascending: false
        });
        if (filters.start_date) {
            query = query.gte('created_at', filters.start_date);
        }
        if (filters.end_date) {
            query = query.lte('created_at', filters.end_date);
        }
        if (filters.supplier_id) {
            query = query.eq('supplier_id', filters.supplier_id);
        }
        const { data, error } = await query;
        if (error) throw error;
        // Filter by medicine if specified
        let filteredData = data;
        if (filters.medicine_id) {
            filteredData = data.filter((invoice)=>invoice.purchase_invoice_items.some((item)=>{
                    var _item_medicines;
                    return ((_item_medicines = item.medicines) === null || _item_medicines === void 0 ? void 0 : _item_medicines.id) === filters.medicine_id;
                }));
        }
        return {
            success: true,
            data: filteredData
        };
    } catch (error) {
        console.error('Error fetching purchases report:', error);
        return {
            success: false,
            error
        };
    }
};
const getCustomerStatement = async (customerId, filters)=>{
    try {
        let salesQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_invoices').select("\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      ").eq('customer_id', customerId).order('created_at', {
            ascending: false
        });
        let returnsQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').select("\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      ").eq('customer_id', customerId).order('created_at', {
            ascending: false
        });
        if (filters.start_date) {
            salesQuery = salesQuery.gte('created_at', filters.start_date);
            returnsQuery = returnsQuery.gte('created_at', filters.start_date);
        }
        if (filters.end_date) {
            salesQuery = salesQuery.lte('created_at', filters.end_date);
            returnsQuery = returnsQuery.lte('created_at', filters.end_date);
        }
        const [salesResult, returnsResult] = await Promise.all([
            salesQuery,
            returnsQuery
        ]);
        if (salesResult.error) throw salesResult.error;
        if (returnsResult.error) throw returnsResult.error;
        return {
            success: true,
            data: {
                sales: salesResult.data,
                returns: returnsResult.data
            }
        };
    } catch (error) {
        console.error('Error fetching customer statement:', error);
        return {
            success: false,
            error
        };
    }
};
const getSupplierStatement = async (supplierId, filters)=>{
    try {
        let purchasesQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_invoices').select("\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      ").eq('supplier_id', supplierId).order('created_at', {
            ascending: false
        });
        let returnsQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').select("\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      ").eq('supplier_id', supplierId).order('created_at', {
            ascending: false
        });
        if (filters.start_date) {
            purchasesQuery = purchasesQuery.gte('created_at', filters.start_date);
            returnsQuery = returnsQuery.gte('created_at', filters.start_date);
        }
        if (filters.end_date) {
            purchasesQuery = purchasesQuery.lte('created_at', filters.end_date);
            returnsQuery = returnsQuery.lte('created_at', filters.end_date);
        }
        const [purchasesResult, returnsResult] = await Promise.all([
            purchasesQuery,
            returnsQuery
        ]);
        if (purchasesResult.error) throw purchasesResult.error;
        if (returnsResult.error) throw returnsResult.error;
        return {
            success: true,
            data: {
                purchases: purchasesResult.data,
                returns: returnsResult.data
            }
        };
    } catch (error) {
        console.error('Error fetching supplier statement:', error);
        return {
            success: false,
            error
        };
    }
};
const getMedicineMovementReport = async (medicineId, filters)=>{
    try {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('inventory_movements').select("\n        *,\n        medicine_batches (\n          batch_code,\n          expiry_date,\n          medicines (name, category)\n        )\n      ").order('created_at', {
            ascending: false
        });
        if (filters.start_date) {
            query = query.gte('created_at', filters.start_date);
        }
        if (filters.end_date) {
            query = query.lte('created_at', filters.end_date);
        }
        const { data, error } = await query;
        if (error) throw error;
        // Filter by medicine
        const filteredData = data.filter((movement)=>{
            var _movement_medicine_batches_medicines, _movement_medicine_batches;
            return ((_movement_medicine_batches = movement.medicine_batches) === null || _movement_medicine_batches === void 0 ? void 0 : (_movement_medicine_batches_medicines = _movement_medicine_batches.medicines) === null || _movement_medicine_batches_medicines === void 0 ? void 0 : _movement_medicine_batches_medicines.id) === medicineId;
        });
        return {
            success: true,
            data: filteredData
        };
    } catch (error) {
        console.error('Error fetching medicine movement report:', error);
        return {
            success: false,
            error
        };
    }
};
const getInventoryReport = async (filters)=>{
    try {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('medicine_batches').select("\n        *,\n        medicines (name, category, manufacturer)\n      ").order('expiry_date', {
            ascending: true
        });
        const { data, error } = await query;
        if (error) throw error;
        let filteredData = data;
        // Filter by category
        if (filters.category) {
            filteredData = filteredData.filter((batch)=>{
                var _batch_medicines;
                return ((_batch_medicines = batch.medicines) === null || _batch_medicines === void 0 ? void 0 : _batch_medicines.category) === filters.category;
            });
        }
        // Filter by low stock (less than 10 units)
        if (filters.low_stock) {
            filteredData = filteredData.filter((batch)=>batch.quantity < 10);
        }
        // Filter by expired
        if (filters.expired) {
            const today = new Date().toISOString().split('T')[0];
            filteredData = filteredData.filter((batch)=>batch.expiry_date < today);
        }
        // Filter by expiring soon (within 30 days)
        if (filters.expiring_soon) {
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
            const futureDate = thirtyDaysFromNow.toISOString().split('T')[0];
            const today = new Date().toISOString().split('T')[0];
            filteredData = filteredData.filter((batch)=>batch.expiry_date >= today && batch.expiry_date <= futureDate);
        }
        return {
            success: true,
            data: filteredData
        };
    } catch (error) {
        console.error('Error fetching inventory report:', error);
        return {
            success: false,
            error
        };
    }
};
const updateReturnStatus = async (returnId, status, rejectionReason)=>{
    const updates = {
        status
    };
    if (rejectionReason) {
        updates.rejection_reason = rejectionReason;
    }
    return updateReturn(returnId, updates);
};
const getReturnForPrint = async (returnId)=>{
    try {
        console.log('🔍 البحث عن المرتجع للطباعة:', returnId);
        // Check localStorage first
        const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]');
        const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]');
        const salesReturnItems = JSON.parse(localStorage.getItem('sales_return_items') || '[]');
        const purchaseReturnItems = JSON.parse(localStorage.getItem('purchase_return_items') || '[]');
        // Find in sales returns
        let foundReturn = salesReturns.find((ret)=>ret.id === returnId);
        if (foundReturn) {
            const items = salesReturnItems.filter((item)=>item.return_id === returnId);
            // Enhance items with medicine names from localStorage medicines data
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const medicineBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
            const enhancedItems = items.map((item)=>{
                let medicineName = item.medicine_name || item.medicineName || 'غير محدد';
                let batchCode = item.batch_code || item.batchCode || '';
                let expiryDate = item.expiry_date || item.expiryDate || '';
                // Try to get medicine name from batch if not available
                if (medicineName === 'غير محدد' && item.medicine_batch_id) {
                    const batch = medicineBatches.find((b)=>b.id === item.medicine_batch_id);
                    if (batch) {
                        batchCode = batch.batch_number || batchCode;
                        expiryDate = batch.expiry_date || expiryDate;
                        const medicine = medicines.find((m)=>m.id === batch.medicine_id);
                        if (medicine) {
                            medicineName = medicine.name || medicineName;
                        }
                    }
                }
                // Try to get medicine name directly if still not available
                if (medicineName === 'غير محدد' && item.medicine_id) {
                    const medicine = medicines.find((m)=>m.id === item.medicine_id);
                    if (medicine) {
                        medicineName = medicine.name || medicineName;
                    }
                }
                return {
                    ...item,
                    medicine_name: medicineName,
                    batch_code: batchCode,
                    expiry_date: expiryDate,
                    unit_price: item.unit_price || item.unitPrice || 0,
                    total_price: item.total_price || item.totalPrice || 0
                };
            });
            const returnData = {
                ...foundReturn,
                type: 'sales',
                return_type: 'sales',
                return_invoice_items: enhancedItems
            };
            console.log('✅ تم العثور على مرتجع مبيعات:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        // Find in purchase returns
        foundReturn = purchaseReturns.find((ret)=>ret.id === returnId);
        if (foundReturn) {
            const items = purchaseReturnItems.filter((item)=>item.return_id === returnId);
            // Enhance items with medicine names from localStorage medicines data
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const enhancedItems = items.map((item)=>{
                let medicineName = item.medicine_name || item.medicineName || 'غير محدد';
                // Try to get medicine name directly if not available
                if (medicineName === 'غير محدد' && item.medicine_id) {
                    const medicine = medicines.find((m)=>m.id === item.medicine_id);
                    if (medicine) {
                        medicineName = medicine.name || medicineName;
                    }
                }
                return {
                    ...item,
                    medicine_name: medicineName,
                    batch_code: item.batch_code || item.batchCode || '',
                    expiry_date: item.expiry_date || item.expiryDate || '',
                    unit_cost: item.unit_cost || item.unitCost || 0,
                    total_cost: item.total_cost || item.totalCost || 0
                };
            });
            const returnData = {
                ...foundReturn,
                type: 'purchase',
                return_type: 'purchase',
                return_invoice_items: enhancedItems
            };
            console.log('✅ تم العثور على مرتجع مشتريات:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        // If not found in localStorage, try Supabase
        console.log('⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...');
        // Try sales returns first
        const { data: salesReturn, error: salesError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').select("\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      ").eq('id', returnId).single();
        if (salesReturn && !salesError) {
            const returnData = {
                ...salesReturn,
                type: 'sales',
                return_type: 'sales',
                return_invoice_items: (salesReturn.sales_return_items || []).map((item)=>{
                    var _item_medicine_batches_medicines, _item_medicine_batches;
                    return {
                        ...item,
                        medicine_name: ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) || item.medicine_name || 'غير محدد',
                        unit_price: item.unit_price || 0,
                        total_price: item.total_price || 0
                    };
                })
            };
            console.log('✅ تم العثور على مرتجع مبيعات في Supabase:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        // Try purchase returns
        const { data: purchaseReturn, error: purchaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').select("\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      ").eq('id', returnId).single();
        if (purchaseReturn && !purchaseError) {
            const returnData = {
                ...purchaseReturn,
                type: 'purchase',
                return_type: 'purchase',
                return_invoice_items: (purchaseReturn.purchase_return_items || []).map((item)=>{
                    var _item_medicines;
                    return {
                        ...item,
                        medicine_name: ((_item_medicines = item.medicines) === null || _item_medicines === void 0 ? void 0 : _item_medicines.name) || item.medicine_name || 'غير محدد',
                        unit_cost: item.unit_cost || 0,
                        total_cost: item.total_cost || 0
                    };
                })
            };
            console.log('✅ تم العثور على مرتجع مشتريات في Supabase:', returnData);
            return {
                success: true,
                data: returnData
            };
        }
        console.log('❌ لم يتم العثور على المرتجع');
        return {
            success: false,
            error: 'Return not found'
        };
    } catch (error) {
        console.error('Error fetching return for print:', error);
        return {
            success: false,
            error
        };
    }
};
const updateReturn = async (returnId, updates)=>{
    try {
        // Update in localStorage first
        const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]');
        const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]');
        // Find and update in sales returns
        const salesIndex = salesReturns.findIndex((ret)=>ret.id === returnId);
        if (salesIndex !== -1) {
            salesReturns[salesIndex] = {
                ...salesReturns[salesIndex],
                ...updates,
                updated_at: new Date().toISOString()
            };
            localStorage.setItem('sales_returns', JSON.stringify(salesReturns));
            // Try to update in Supabase
            try {
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('sales_returns').update(updates).eq('id', returnId);
                if (error) {
                    console.warn('Failed to update return in Supabase:', error);
                }
            } catch (supabaseError) {
                console.warn('Supabase update failed, continuing with localStorage:', supabaseError);
            }
            return {
                success: true,
                data: salesReturns[salesIndex]
            };
        }
        // Find and update in purchase returns
        const purchaseIndex = purchaseReturns.findIndex((ret)=>ret.id === returnId);
        if (purchaseIndex !== -1) {
            purchaseReturns[purchaseIndex] = {
                ...purchaseReturns[purchaseIndex],
                ...updates,
                updated_at: new Date().toISOString()
            };
            localStorage.setItem('purchase_returns', JSON.stringify(purchaseReturns));
            // Try to update in Supabase
            try {
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('purchase_returns').update(updates).eq('id', returnId);
                if (error) {
                    console.warn('Failed to update return in Supabase:', error);
                }
            } catch (supabaseError) {
                console.warn('Supabase update failed, continuing with localStorage:', supabaseError);
            }
            return {
                success: true,
                data: purchaseReturns[purchaseIndex]
            };
        }
        return {
            success: false,
            error: 'Return not found'
        };
    } catch (error) {
        console.error('Error updating return:', error);
        return {
            success: false,
            error
        };
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/test-print/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>TestPrintPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AppLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/printer.js [app-client] (ecmascript) <export default as Printer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function TestPrintPage() {
    var _selectedInvoice_customers, _selectedInvoice_sales_invoice_items, _selectedInvoice_sales_invoice_items1;
    _s();
    const [invoices, setInvoices] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [selectedInvoice, setSelectedInvoice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [printData, setPrintData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TestPrintPage.useEffect": ()=>{
            loadInvoices();
        }
    }["TestPrintPage.useEffect"], []);
    const loadInvoices = async ()=>{
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSalesInvoices"])();
            if (result.success) {
                setInvoices(result.data || []);
            }
        } catch (error) {
            console.error('خطأ في تحميل الفواتير:', error);
        } finally{
            setLoading(false);
        }
    };
    const handleViewInvoice = async (invoiceId)=>{
        try {
            console.log('🔍 جلب بيانات الفاتورة للطباعة:', invoiceId);
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSalesInvoiceForPrint"])(invoiceId);
            if (result.success) {
                var _result_data_sales_invoice_items;
                console.log('✅ بيانات الفاتورة:', result.data);
                setSelectedInvoice(result.data);
                setPrintData(result.data);
                // Log medicine names for debugging - DETAILED
                console.log('🔍 تحليل مفصل لعناصر الفاتورة:');
                (_result_data_sales_invoice_items = result.data.sales_invoice_items) === null || _result_data_sales_invoice_items === void 0 ? void 0 : _result_data_sales_invoice_items.forEach((item, index)=>{
                    var _item_medicine_batches_medicines, _item_medicine_batches;
                    console.log("\n--- العنصر ".concat(index + 1, " ---"));
                    console.log('البيانات الكاملة:', item);
                    console.log('أسماء الأدوية المختلفة:', {
                        medicine_name: item.medicine_name,
                        medicineName: item.medicineName,
                        medicine_batches_name: (_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name,
                        batch_id: item.medicine_batch_id,
                        batch_data: item.medicine_batches
                    });
                    // Check localStorage for this batch
                    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');
                    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
                    const batch = batches.find((b)=>b.id === item.medicine_batch_id);
                    const medicine = medicines.find((m)=>m.id === (batch === null || batch === void 0 ? void 0 : batch.medicine_id));
                    console.log('بيانات من localStorage:', {
                        batch_found: !!batch,
                        medicine_found: !!medicine,
                        batch_data: batch,
                        medicine_data: medicine,
                        calculated_name: (medicine === null || medicine === void 0 ? void 0 : medicine.name) || 'غير موجود'
                    });
                });
            } else {
                console.error('❌ فشل في جلب بيانات الفاتورة:', result.error);
            }
        } catch (error) {
            console.error('❌ خطأ في جلب بيانات الفاتورة:', error);
        }
    };
    const handlePrint = ()=>{
        var _printData_customers, _printData_sales_invoice_items, _printData_subtotal, _printData_discount, _printData_tax, _printData_final_amount;
        if (!printData) return;
        const printWindow = window.open('', '_blank');
        if (!printWindow) return;
        const printContent = '\n      <!DOCTYPE html>\n      <html dir="rtl" lang="ar">\n      <head>\n        <meta charset="UTF-8">\n        <title>فاتورة مبيعات - '.concat(printData.invoice_number, '</title>\n        <style>\n          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n          .header { text-align: center; margin-bottom: 20px; }\n          .invoice-info { margin-bottom: 20px; }\n          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n          .items-table th { background-color: #f5f5f5; }\n          .total { text-align: left; font-weight: bold; }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>فاتورة مبيعات</h1>\n          <h2>رقم الفاتورة: ').concat(printData.invoice_number, '</h2>\n        </div>\n        \n        <div class="invoice-info">\n          <p><strong>العميل:</strong> ').concat(((_printData_customers = printData.customers) === null || _printData_customers === void 0 ? void 0 : _printData_customers.name) || 'عميل نقدي', "</p>\n          <p><strong>التاريخ:</strong> ").concat(new Date(printData.created_at).toLocaleDateString('ar-EG'), "</p>\n          <p><strong>طريقة الدفع:</strong> ").concat(printData.payment_method === 'cash' ? 'نقدي' : 'آجل', '</p>\n        </div>\n        \n        <table class="items-table">\n          <thead>\n            <tr>\n              <th>اسم الدواء</th>\n              <th>الكمية</th>\n              <th>سعر الوحدة</th>\n              <th>الإجمالي</th>\n              <th>هدية</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(((_printData_sales_invoice_items = printData.sales_invoice_items) === null || _printData_sales_invoice_items === void 0 ? void 0 : _printData_sales_invoice_items.map((item)=>{
            var _item_medicine_batches_medicines, _item_medicine_batches;
            const medicineName = ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) || item.medicine_name || item.medicineName || 'غير محدد';
            return "\n                <tr>\n                  <td>".concat(medicineName, "</td>\n                  <td>").concat(item.quantity, "</td>\n                  <td>").concat(item.unit_price.toFixed(2), "</td>\n                  <td>").concat(item.total_price.toFixed(2), "</td>\n                  <td>").concat(item.is_gift ? 'نعم' : 'لا', "</td>\n                </tr>\n              ");
        }).join('')) || '<tr><td colspan="5">لا توجد عناصر</td></tr>', '\n          </tbody>\n        </table>\n        \n        <div class="total">\n          <p><strong>المجموع الفرعي:</strong> ').concat(((_printData_subtotal = printData.subtotal) === null || _printData_subtotal === void 0 ? void 0 : _printData_subtotal.toFixed(2)) || '0.00', " جنيه</p>\n          <p><strong>الخصم:</strong> ").concat(((_printData_discount = printData.discount) === null || _printData_discount === void 0 ? void 0 : _printData_discount.toFixed(2)) || '0.00', " جنيه</p>\n          <p><strong>الضريبة:</strong> ").concat(((_printData_tax = printData.tax) === null || _printData_tax === void 0 ? void 0 : _printData_tax.toFixed(2)) || '0.00', " جنيه</p>\n          <p><strong>المجموع النهائي:</strong> ").concat(((_printData_final_amount = printData.final_amount) === null || _printData_final_amount === void 0 ? void 0 : _printData_final_amount.toFixed(2)) || '0.00', " جنيه</p>\n        </div>\n      </body>\n      </html>\n    ");
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center items-center h-64",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-lg",
                    children: "جاري التحميل..."
                }, void 0, false, {
                    fileName: "[project]/src/app/test-print/page.tsx",
                    lineNumber: 155,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/test-print/page.tsx",
                lineNumber: 154,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/test-print/page.tsx",
            lineNumber: 153,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-6",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-blue-50 p-3 rounded-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"], {
                                    className: "h-6 w-6 text-blue-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-print/page.tsx",
                                    lineNumber: 167,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/test-print/page.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold text-gray-900",
                                        children: "اختبار طباعة الفواتير"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 170,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-600",
                                        children: "اختبار عرض أسماء الأدوية في الفواتير المطبوعة"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 171,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/test-print/page.tsx",
                                lineNumber: 169,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/test-print/page.tsx",
                        lineNumber: 165,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-x-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                            className: "min-w-full divide-y divide-gray-200",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                    className: "bg-gray-50",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                children: "رقم الفاتورة"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 179,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                children: "العميل"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 182,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                children: "التاريخ"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 185,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                children: "المبلغ"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 188,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
                                                children: "الإجراءات"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 191,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 178,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-print/page.tsx",
                                    lineNumber: 177,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                    className: "bg-white divide-y divide-gray-200",
                                    children: invoices.map((invoice)=>{
                                        var _invoice_customers, _invoice_final_amount;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",
                                                    children: invoice.invoice_number
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                                                    children: ((_invoice_customers = invoice.customers) === null || _invoice_customers === void 0 ? void 0 : _invoice_customers.name) || 'عميل نقدي'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                                                    children: new Date(invoice.created_at).toLocaleDateString('ar-EG')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                                                    children: [
                                                        (_invoice_final_amount = invoice.final_amount) === null || _invoice_final_amount === void 0 ? void 0 : _invoice_final_amount.toFixed(2),
                                                        " جنيه"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 208,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>handleViewInvoice(invoice.id),
                                                            className: "text-blue-600 hover:text-blue-900 ml-2",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                                lineNumber: 216,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/test-print/page.tsx",
                                                            lineNumber: 212,
                                                            columnNumber: 23
                                                        }, this),
                                                        (selectedInvoice === null || selectedInvoice === void 0 ? void 0 : selectedInvoice.id) === invoice.id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: handlePrint,
                                                            className: "text-green-600 hover:text-green-900",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                                lineNumber: 223,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/test-print/page.tsx",
                                                            lineNumber: 219,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 211,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, invoice.id, true, {
                                            fileName: "[project]/src/app/test-print/page.tsx",
                                            lineNumber: 198,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-print/page.tsx",
                                    lineNumber: 196,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/test-print/page.tsx",
                            lineNumber: 176,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/test-print/page.tsx",
                        lineNumber: 175,
                        columnNumber: 11
                    }, this),
                    selectedInvoice && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-6 bg-gray-50 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "font-medium text-gray-900 mb-3",
                                children: "تفاصيل الفاتورة المحددة:"
                            }, void 0, false, {
                                fileName: "[project]/src/app/test-print/page.tsx",
                                lineNumber: 235,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "رقم الفاتورة:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 237,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            selectedInvoice.invoice_number
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 237,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "العميل:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 238,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            ((_selectedInvoice_customers = selectedInvoice.customers) === null || _selectedInvoice_customers === void 0 ? void 0 : _selectedInvoice_customers.name) || 'عميل نقدي'
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 238,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "عدد العناصر:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 239,
                                                columnNumber: 20
                                            }, this),
                                            " ",
                                            ((_selectedInvoice_sales_invoice_items = selectedInvoice.sales_invoice_items) === null || _selectedInvoice_sales_invoice_items === void 0 ? void 0 : _selectedInvoice_sales_invoice_items.length) || 0
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 239,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "font-medium mb-2",
                                                children: "عناصر الفاتورة:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/test-print/page.tsx",
                                                lineNumber: 242,
                                                columnNumber: 19
                                            }, this),
                                            (_selectedInvoice_sales_invoice_items1 = selectedInvoice.sales_invoice_items) === null || _selectedInvoice_sales_invoice_items1 === void 0 ? void 0 : _selectedInvoice_sales_invoice_items1.map((item, index)=>{
                                                var _item_medicine_batches_medicines, _item_medicine_batches;
                                                const medicineName = ((_item_medicine_batches = item.medicine_batches) === null || _item_medicine_batches === void 0 ? void 0 : (_item_medicine_batches_medicines = _item_medicine_batches.medicines) === null || _item_medicine_batches_medicines === void 0 ? void 0 : _item_medicine_batches_medicines.name) || item.medicine_name || item.medicineName || 'غير محدد';
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-white p-2 rounded border",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "اسم الدواء:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                                    lineNumber: 247,
                                                                    columnNumber: 28
                                                                }, this),
                                                                " ",
                                                                medicineName
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/test-print/page.tsx",
                                                            lineNumber: 247,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "الكمية:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                                    lineNumber: 248,
                                                                    columnNumber: 28
                                                                }, this),
                                                                " ",
                                                                item.quantity
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/test-print/page.tsx",
                                                            lineNumber: 248,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "سعر الوحدة:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                                    lineNumber: 249,
                                                                    columnNumber: 28
                                                                }, this),
                                                                " ",
                                                                item.unit_price,
                                                                " جنيه"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/test-print/page.tsx",
                                                            lineNumber: 249,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/app/test-print/page.tsx",
                                                    lineNumber: 246,
                                                    columnNumber: 23
                                                }, this);
                                            })
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/test-print/page.tsx",
                                        lineNumber: 241,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/test-print/page.tsx",
                                lineNumber: 236,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/test-print/page.tsx",
                        lineNumber: 234,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/test-print/page.tsx",
                lineNumber: 164,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/test-print/page.tsx",
            lineNumber: 163,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/test-print/page.tsx",
        lineNumber: 162,
        columnNumber: 5
    }, this);
}
_s(TestPrintPage, "1uGzTLTLeVg2FOGQBI+TMCkIacQ=");
_c = TestPrintPage;
var _c;
__turbopack_context__.k.register(_c, "TestPrintPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0023b6c3._.js.map