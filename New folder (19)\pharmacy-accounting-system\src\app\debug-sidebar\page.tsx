'use client'

import { useState } from 'react'
import { Menu, X } from 'lucide-react'

export default function DebugSidebarPage() {
  const [isOpen, setIsOpen] = useState(false)

  const toggleSidebar = () => {
    console.log('Toggle clicked, current state:', isOpen)
    setIsOpen(!isOpen)
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Simple Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-40">
        <div className="flex items-center justify-between h-full px-6">
          <div className="flex items-center gap-4">
            {/* Menu Button */}
            <button
              onClick={toggleSidebar}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center border-2 border-red-500"
              style={{ backgroundColor: 'yellow' }}
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
            <span className="text-lg font-bold">اختبار الشريط الجانبي</span>
          </div>
          <div className="text-sm">
            الحالة: {isOpen ? 'مفتوح' : 'مغلق'}
          </div>
        </div>
      </header>

      {/* Sidebar */}
      <div
        className={`bg-white shadow-2xl h-screen w-64 fixed right-0 top-0 z-50 border-l border-gray-200 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="p-6 h-full">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold">الشريط الجانبي</h2>
            <button
              onClick={toggleSidebar}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-100 rounded-lg">
              <p className="text-green-800">✅ الشريط الجانبي يعمل!</p>
            </div>
            <div className="p-4 bg-blue-100 rounded-lg">
              <p className="text-blue-800">الحالة الحالية: {isOpen ? 'مفتوح' : 'مغلق'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggleSidebar}
        />
      )}

      {/* Main Content */}
      <main className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h1 className="text-2xl font-bold mb-4">اختبار الشريط الجانبي المبسط</h1>
            
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="font-medium text-yellow-800 mb-2">تعليمات:</h3>
                <ul className="text-yellow-700 space-y-1 text-sm">
                  <li>• الزر الأصفر في الهيدر يجب أن يكون مرئياً</li>
                  <li>• اضغط على الزر لفتح/إغلاق الشريط الجانبي</li>
                  <li>• افتح وحدة التحكم (F12) لرؤية الرسائل</li>
                </ul>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-medium text-blue-800 mb-2">الحالة الحالية:</h3>
                <p className="text-blue-700">
                  الشريط الجانبي: <strong>{isOpen ? 'مفتوح' : 'مغلق'}</strong>
                </p>
              </div>

              <button
                onClick={toggleSidebar}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {isOpen ? 'إغلاق الشريط الجانبي' : 'فتح الشريط الجانبي'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
