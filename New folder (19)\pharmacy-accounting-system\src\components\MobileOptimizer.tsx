'use client'

import { useEffect } from 'react'

export default function MobileOptimizer() {
  useEffect(() => {
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    if (isMobile || isTouch) {
      // Add mobile-specific classes to body
      document.body.classList.add('mobile-device', 'touch-device')
      
      // Prevent zoom on input focus (iOS)
      const viewport = document.querySelector('meta[name=viewport]')
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover')
      }

      // Add touch-friendly classes to interactive elements
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')
      interactiveElements.forEach(element => {
        element.classList.add('touch-friendly')
      })

      // Optimize scrolling
      document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')
      document.body.style.setProperty('-webkit-overflow-scrolling', 'touch')

      // Prevent bounce scrolling on iOS
      document.addEventListener('touchmove', (e) => {
        if (e.target === document.body) {
          e.preventDefault()
        }
      }, { passive: false })

      // Add safe area support
      if (CSS.supports('padding: max(0px)')) {
        document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')
        document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')
        document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')
        document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')
      }

      // Optimize performance for mobile
      const optimizeElement = (element: Element) => {
        if (element instanceof HTMLElement) {
          element.style.setProperty('transform', 'translateZ(0)')
          element.style.setProperty('will-change', 'transform')
        }
      }

      // Apply optimizations to animated elements
      const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift')
      animatedElements.forEach(optimizeElement)

      // Handle orientation change
      const handleOrientationChange = () => {
        // Force repaint after orientation change
        setTimeout(() => {
          window.scrollTo(0, window.scrollY)
        }, 100)
      }

      window.addEventListener('orientationchange', handleOrientationChange)
      
      // Cleanup
      return () => {
        window.removeEventListener('orientationchange', handleOrientationChange)
      }
    }
  }, [])

  useEffect(() => {
    // Add PWA install prompt handling
    let deferredPrompt: any

    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      deferredPrompt = e
      
      // Show custom install button or banner
      const installBanner = document.getElementById('pwa-install-banner')
      if (installBanner) {
        installBanner.style.display = 'block'
      }
    }

    const handleAppInstalled = () => {
      console.log('PWA was installed')
      deferredPrompt = null
      
      // Hide install banner
      const installBanner = document.getElementById('pwa-install-banner')
      if (installBanner) {
        installBanner.style.display = 'none'
      }
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  return null // This component doesn't render anything
}
