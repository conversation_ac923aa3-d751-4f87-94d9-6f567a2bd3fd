'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import { useNotifications } from '@/contexts/NotificationContext'
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  XCircle,
  Menu,
  X,
  Bell,
  User,
  Settings
} from 'lucide-react'

export default function TestFixesPage() {
  const [testResults, setTestResults] = useState<string[]>([])
  const { addNotification } = useNotifications()

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, result])
  }

  const testSidebarButton = () => {
    addTestResult('✅ اختبار زر الشريط الجانبي - يجب أن يكون مرئياً في الهيدر')
  }

  const testNotificationPosition = () => {
    addNotification({
      type: 'success',
      title: 'اختبار التنبيه',
      message: 'هذا تنبيه تجريبي للتأكد من الموضع الصحيح',
      priority: 'medium'
    })
    addTestResult('✅ اختبار موضع التنبيهات - يجب أن تظهر في المكان الصحيح')
  }

  const testNotificationTypes = () => {
    const notifications = [
      { type: 'success', title: 'نجح', message: 'عملية ناجحة' },
      { type: 'warning', title: 'تحذير', message: 'انتبه لهذا الأمر' },
      { type: 'error', title: 'خطأ', message: 'حدث خطأ ما' },
      { type: 'info', title: 'معلومة', message: 'معلومة مفيدة' }
    ]

    notifications.forEach((notif, index) => {
      setTimeout(() => {
        addNotification({
          type: notif.type as any,
          title: notif.title,
          message: notif.message,
          priority: 'medium'
        })
      }, index * 1000)
    })
    
    addTestResult('✅ اختبار أنواع التنبيهات المختلفة - يجب أن تظهر بألوان مختلفة')
  }

  const testMobileResponsiveness = () => {
    addTestResult('📱 اختبار التجاوب مع الهواتف:')
    addTestResult('  • افتح أدوات المطور (F12)')
    addTestResult('  • فعل وضع الجهاز (Ctrl+Shift+M)')
    addTestResult('  • اختر هاتف من القائمة')
    addTestResult('  • تأكد من ظهور زر القائمة')
    addTestResult('  • تأكد من موضع التنبيهات')
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-8 w-8" />
            <div>
              <h1 className="text-2xl md:text-3xl font-bold">اختبار الإصلاحات</h1>
              <p className="text-green-100 mt-1">التأكد من حل مشاكل الأزرار والتنبيهات</p>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">اختبارات سريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={testSidebarButton}
              className="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors min-h-[80px] flex flex-col items-center justify-center gap-2"
            >
              <Menu className="h-6 w-6 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">اختبار زر القائمة</span>
            </button>

            <button
              onClick={testNotificationPosition}
              className="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors min-h-[80px] flex flex-col items-center justify-center gap-2"
            >
              <Bell className="h-6 w-6 text-green-600" />
              <span className="text-sm font-medium text-green-800">اختبار موضع التنبيه</span>
            </button>

            <button
              onClick={testNotificationTypes}
              className="p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors min-h-[80px] flex flex-col items-center justify-center gap-2"
            >
              <AlertTriangle className="h-6 w-6 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">اختبار أنواع التنبيهات</span>
            </button>

            <button
              onClick={testMobileResponsiveness}
              className="p-4 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors min-h-[80px] flex flex-col items-center justify-center gap-2"
            >
              <Settings className="h-6 w-6 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">اختبار التجاوب</span>
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">نتائج الاختبارات</h2>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              مسح النتائج
            </button>
          </div>
          
          {testResults.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Info className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>لم يتم تشغيل أي اختبارات بعد</p>
              <p className="text-sm mt-1">اضغط على أحد الأزرار أعلاه لبدء الاختبار</p>
            </div>
          ) : (
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className="p-3 bg-gray-50 rounded-lg border border-gray-200 font-mono text-sm"
                >
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Manual Test Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-2">تعليمات الاختبار اليدوي</h3>
              <div className="text-yellow-700 space-y-2 text-sm">
                <div>
                  <strong>1. اختبار زر الشريط الجانبي:</strong>
                  <ul className="mr-4 mt-1 space-y-1">
                    <li>• يجب أن يكون زر القائمة (☰) مرئياً في الهيدر</li>
                    <li>• عند الضغط عليه يجب أن يفتح/يغلق الشريط الجانبي</li>
                    <li>• يجب أن يعمل على جميع أحجام الشاشات</li>
                  </ul>
                </div>
                
                <div>
                  <strong>2. اختبار التنبيهات:</strong>
                  <ul className="mr-4 mt-1 space-y-1">
                    <li>• يجب أن تظهر التنبيهات في المكان الصحيح</li>
                    <li>• يجب ألا تتداخل مع الشريط الجانبي</li>
                    <li>• يجب أن تكون قابلة للقراءة والإغلاق</li>
                  </ul>
                </div>

                <div>
                  <strong>3. اختبار التجاوب:</strong>
                  <ul className="mr-4 mt-1 space-y-1">
                    <li>• افتح أدوات المطور (F12)</li>
                    <li>• فعل وضع الجهاز (Ctrl+Shift+M)</li>
                    <li>• اختبر على أحجام مختلفة</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="font-medium text-gray-900">زر الشريط الجانبي</p>
                <p className="text-sm text-gray-600">مرئي ويعمل بشكل صحيح</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="font-medium text-gray-900">موضع التنبيهات</p>
                <p className="text-sm text-gray-600">محسن للهواتف والكمبيوتر</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="font-medium text-gray-900">التجاوب العام</p>
                <p className="text-sm text-gray-600">متوافق مع جميع الأجهزة</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
