'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { usePermissions } from '@/contexts/AuthContext'
import {
  Home,
  ShoppingCart,
  Package,
  Users,
  UserCheck,
  RotateCcw,
  BarChart3,
  Settings,
  Pill,
  FileText,
  Wallet,
  Shield,
  Activity,
  Bell,
  Wrench,
  Printer,
  Bug
} from 'lucide-react'

interface MenuItem {
  title: string
  href: string
  icon: any
  permission?: string
  requireAny?: string[]
}

const getMenuItems = (permissions: any): MenuItem[] => [
  {
    title: 'الرئيسية',
    href: '/',
    icon: Home
  },
  {
    title: 'إدارة المخزون',
    href: '/inventory',
    icon: Package,
    permission: 'inventory_view'
  },
  {
    title: 'المبيعات',
    href: '/sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  },

  {
    title: 'المشتريات',
    href: '/purchases',
    icon: Pill,
    permission: 'purchases_view'
  },

  {
    title: 'العملاء',
    href: '/customers',
    icon: Users,
    permission: 'customers_view'
  },
  {
    title: 'الموردين',
    href: '/suppliers',
    icon: UserCheck,
    permission: 'suppliers_view'
  },
  {
    title: 'المرتجعات',
    href: '/returns',
    icon: RotateCcw,
    permission: 'returns_view'
  },

  {
    title: 'الصندوق',
    href: '/cashbox',
    icon: Wallet,
    permission: 'cashbox_view'
  },
  {
    title: 'التقارير',
    href: '/reports',
    icon: BarChart3,
    permission: 'reports_view'
  },
  {
    title: 'إدارة المستخدمين',
    href: '/users',
    icon: Shield,
    permission: 'users_view'
  },
  {
    title: 'سجل النشاطات',
    href: '/activity-log',
    icon: Activity,
    permission: 'users_view'
  },
  {
    title: 'التنبيهات',
    href: '/notifications',
    icon: Bell
  },
  {
    title: 'الإعدادات',
    href: '/settings',
    icon: Settings,
    permission: 'settings_view'
  },
  {
    title: 'إصلاح البيانات',
    href: '/fix-data',
    icon: Wrench,
    permission: 'settings_view'
  },
  {
    title: 'اختبار الطباعة',
    href: '/test-print',
    icon: Printer,
    permission: 'sales_view'
  },
  {
    title: 'تشخيص البيانات',
    href: '/debug-data',
    icon: Bug,
    permission: 'settings_view'
  },
  {
    title: 'اختبار المبيعات',
    href: '/debug-sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  }
]

interface SidebarProps {
  isOpen?: boolean
  onClose?: () => void
}

export default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {
  const pathname = usePathname()
  const { hasPermission, permissions } = usePermissions()



  const menuItems = getMenuItems(permissions)

  // إغلاق القائمة عند تغيير الصفحة
  useEffect(() => {
    if (onClose) {
      onClose()
    }
  }, [pathname, onClose])

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar')
      const menuButton = document.getElementById('mobile-menu-button')

      if (sidebar && !sidebar.contains(event.target as Node) &&
          menuButton && !menuButton.contains(event.target as Node)) {
        if (onClose) {
          onClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // تصفية العناصر بناءً على الصلاحيات
  const visibleMenuItems = menuItems.filter(item => {
    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)
    return hasPermission(item.permission as any)
  })

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar - نسخة جديدة تعمل بالتأكيد */}
      <div
        id="mobile-sidebar"
        style={{
          position: 'fixed',
          top: 0,
          right: isOpen ? '0px' : '-300px',
          height: '100vh',
          width: '280px',
          backgroundColor: 'white',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          borderLeft: '1px solid #e5e7eb',
          transition: 'right 0.3s ease-in-out',
          zIndex: 9999,
          overflow: 'auto'
        }}
      >
        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>
          {/* Header */}
          <div style={{
            padding: '16px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
            borderRadius: '12px',
            color: 'white',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>نظام الصيدلية</h1>
            <p style={{ fontSize: '14px', margin: '8px 0 0 0', opacity: 0.9 }}>مكتب لارين العلمي</p>
          </div>

          {/* Navigation Menu */}
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{
              margin: '0 0 16px 0',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#374151',
              borderBottom: '2px solid #e5e7eb',
              paddingBottom: '8px'
            }}>القائمة الرئيسية</h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {visibleMenuItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href

                return (
                  <a
                    key={item.href}
                    href={item.href}
                    onClick={onClose}
                    style={{
                      padding: '12px 16px',
                      backgroundColor: isActive ? '#3b82f6' : '#f8fafc',
                      color: isActive ? 'white' : '#374151',
                      textDecoration: 'none',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      border: isActive ? 'none' : '1px solid #e5e7eb',
                      transition: 'all 0.2s ease',
                      fontSize: '14px',
                      fontWeight: isActive ? 'bold' : 'normal'
                    }}
                  >
                    <Icon style={{ width: '20px', height: '20px' }} />
                    {item.title}
                  </a>
                )
              })}
            </div>
          </div>

          {/* Close Button */}
          <div style={{ marginTop: 'auto', paddingTop: '16px' }}>
            <button
              onClick={onClose}
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}
            >
              إغلاق القائمة
            </button>
          </div>

        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 9998
          }}
          onClick={onClose}
        />
      )}
    </>
  )
}

// تصدير دالة للتحكم في القائمة من مكونات أخرى
export const useSidebar = () => {
  const [isOpen, setIsOpen] = useState(false)
  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }
}
