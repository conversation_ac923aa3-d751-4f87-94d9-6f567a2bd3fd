'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { usePermissions } from '@/contexts/AuthContext'
import {
  Home,
  ShoppingCart,
  Package,
  Users,
  UserCheck,
  RotateCcw,
  BarChart3,
  Settings,
  Pill,
  FileText,
  Wallet,
  Shield,
  Activity,
  Bell,
  Wrench,
  Printer,
  Bug
} from 'lucide-react'

interface MenuItem {
  title: string
  href: string
  icon: any
  permission?: string
  requireAny?: string[]
}

const getMenuItems = (permissions: any): MenuItem[] => [
  {
    title: 'الرئيسية',
    href: '/',
    icon: Home
  },
  {
    title: 'إدارة المخزون',
    href: '/inventory',
    icon: Package,
    permission: 'inventory_view'
  },
  {
    title: 'المبيعات',
    href: '/sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  },

  {
    title: 'المشتريات',
    href: '/purchases',
    icon: Pill,
    permission: 'purchases_view'
  },

  {
    title: 'العملاء',
    href: '/customers',
    icon: Users,
    permission: 'customers_view'
  },
  {
    title: 'الموردين',
    href: '/suppliers',
    icon: UserCheck,
    permission: 'suppliers_view'
  },
  {
    title: 'المرتجعات',
    href: '/returns',
    icon: RotateCcw,
    permission: 'returns_view'
  },

  {
    title: 'الصندوق',
    href: '/cashbox',
    icon: Wallet,
    permission: 'cashbox_view'
  },
  {
    title: 'التقارير',
    href: '/reports',
    icon: BarChart3,
    permission: 'reports_view'
  },
  {
    title: 'إدارة المستخدمين',
    href: '/users',
    icon: Shield,
    permission: 'users_view'
  },
  {
    title: 'سجل النشاطات',
    href: '/activity-log',
    icon: Activity,
    permission: 'users_view'
  },
  {
    title: 'التنبيهات',
    href: '/notifications',
    icon: Bell
  },
  {
    title: 'الإعدادات',
    href: '/settings',
    icon: Settings,
    permission: 'settings_view'
  },
  {
    title: 'إصلاح البيانات',
    href: '/fix-data',
    icon: Wrench,
    permission: 'settings_view'
  },
  {
    title: 'اختبار الطباعة',
    href: '/test-print',
    icon: Printer,
    permission: 'sales_view'
  },
  {
    title: 'تشخيص البيانات',
    href: '/debug-data',
    icon: Bug,
    permission: 'settings_view'
  },
  {
    title: 'اختبار المبيعات',
    href: '/debug-sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  }
]

interface SidebarProps {
  isOpen?: boolean
  onClose?: () => void
}

export default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {
  const pathname = usePathname()
  const { hasPermission, permissions } = usePermissions()

  console.log('🔧 Sidebar render - isOpen:', isOpen, 'transform:', isOpen ? 'translateX(-256px)' : 'translateX(0)')

  const menuItems = getMenuItems(permissions)

  // إغلاق القائمة عند تغيير الصفحة
  useEffect(() => {
    if (onClose) {
      onClose()
    }
  }, [pathname, onClose])

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar')
      const menuButton = document.getElementById('mobile-menu-button')

      if (sidebar && !sidebar.contains(event.target as Node) &&
          menuButton && !menuButton.contains(event.target as Node)) {
        if (onClose) {
          onClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // تصفية العناصر بناءً على الصلاحيات
  const visibleMenuItems = menuItems.filter(item => {
    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)
    return hasPermission(item.permission as any)
  })

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar - نسخة مبسطة تعمل */}
      <div
        id="mobile-sidebar"
        style={{
          position: 'fixed',
          top: 0,
          right: 0,
          height: '100vh',
          width: '256px',
          backgroundColor: 'white',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          borderLeft: '1px solid #e5e7eb',
          transform: isOpen ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease-in-out',
          zIndex: 50,
          overflow: 'hidden'
        }}
      >
        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>
          <div style={{
            padding: '16px',
            backgroundColor: '#3b82f6',
            borderRadius: '12px',
            color: 'white',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>✅ الشريط الجانبي يعمل!</h1>
            <p style={{ fontSize: '14px', margin: '8px 0 0 0' }}>نظام الصيدلية - مكتب لارين العلمي</p>
          </div>

          <div style={{
            padding: '16px',
            backgroundColor: '#10b981',
            borderRadius: '8px',
            color: 'white',
            marginBottom: '16px',
            textAlign: 'center'
          }}>
            <p style={{ margin: 0, fontWeight: 'bold' }}>🎉 تم إصلاح المشكلة!</p>
            <p style={{ margin: '8px 0 0 0', fontSize: '12px' }}>الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>
          </div>

          <div style={{ marginBottom: '16px' }}>
            <button
              onClick={onClose}
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              إغلاق الشريط الجانبي
            </button>
          </div>

          <div style={{
            padding: '16px',
            backgroundColor: '#f3f4f6',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: 'bold' }}>القائمة الرئيسية:</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <a href="/sales" style={{
                padding: '8px 12px',
                backgroundColor: '#3b82f6',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                display: 'block',
                textAlign: 'center'
              }}>المبيعات</a>
              <a href="/purchases" style={{
                padding: '8px 12px',
                backgroundColor: '#3b82f6',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                display: 'block',
                textAlign: 'center'
              }}>المشتريات</a>
              <a href="/inventory" style={{
                padding: '8px 12px',
                backgroundColor: '#3b82f6',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                display: 'block',
                textAlign: 'center'
              }}>المخزون</a>
            </div>
          </div>

        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 40
          }}
          onClick={onClose}
        />
      )}
    </>
  )
}

// تصدير دالة للتحكم في القائمة من مكونات أخرى
export const useSidebar = () => {
  const [isOpen, setIsOpen] = useState(false)
  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }
}
