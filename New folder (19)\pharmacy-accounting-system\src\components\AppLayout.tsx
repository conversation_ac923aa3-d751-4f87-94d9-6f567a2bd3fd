'use client'

import { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import ErrorBoundary from './ErrorBoundary'
import ToastNotifications from './ToastNotifications'
import MobileOptimizer from './MobileOptimizer'
import PWAInstallBanner from './PWAInstallBanner'
import ScreenSizeIndicator from './ScreenSizeIndicator'

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MobileOptimizer />
      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />
      <Header
        onMobileMenuToggle={toggleMobileMenu}
        isMobileMenuOpen={isMobileMenuOpen}
      />
      <main className={`
        mr-0
        mt-16
        p-3 md:p-6
        main-content-mobile
        min-h-screen
        w-full
        max-w-full
        overflow-x-hidden
        safe-area-inset-bottom
      `}>
        <ErrorBoundary>
          <div className="max-w-full overflow-x-auto container">
            {children}
          </div>
        </ErrorBoundary>
      </main>
      <ToastNotifications />
      <PWAInstallBanner />
      <ScreenSizeIndicator />

      {/* Mobile overlay when sidebar is open */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  )
}
