{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { usePermissions } from '@/contexts/AuthContext'\nimport {\n  Home,\n  ShoppingCart,\n  Package,\n  Users,\n  UserCheck,\n  RotateCcw,\n  BarChart3,\n  Settings,\n  Pill,\n  FileText,\n  Wallet,\n  Shield,\n  Activity,\n  Bell,\n  Wrench,\n  Printer,\n  Bug\n} from 'lucide-react'\n\ninterface MenuItem {\n  title: string\n  href: string\n  icon: any\n  permission?: string\n  requireAny?: string[]\n}\n\nconst getMenuItems = (permissions: any): MenuItem[] => [\n  {\n    title: 'الرئيسية',\n    href: '/',\n    icon: Home\n  },\n  {\n    title: 'إدارة المخزون',\n    href: '/inventory',\n    icon: Package,\n    permission: 'inventory_view'\n  },\n  {\n    title: 'المبيعات',\n    href: '/sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  },\n\n  {\n    title: 'المشتريات',\n    href: '/purchases',\n    icon: Pill,\n    permission: 'purchases_view'\n  },\n\n  {\n    title: 'العملاء',\n    href: '/customers',\n    icon: Users,\n    permission: 'customers_view'\n  },\n  {\n    title: 'الموردين',\n    href: '/suppliers',\n    icon: UserCheck,\n    permission: 'suppliers_view'\n  },\n  {\n    title: 'المرتجعات',\n    href: '/returns',\n    icon: RotateCcw,\n    permission: 'returns_view'\n  },\n\n  {\n    title: 'الصندوق',\n    href: '/cashbox',\n    icon: Wallet,\n    permission: 'cashbox_view'\n  },\n  {\n    title: 'التقارير',\n    href: '/reports',\n    icon: BarChart3,\n    permission: 'reports_view'\n  },\n  {\n    title: 'إدارة المستخدمين',\n    href: '/users',\n    icon: Shield,\n    permission: 'users_view'\n  },\n  {\n    title: 'سجل النشاطات',\n    href: '/activity-log',\n    icon: Activity,\n    permission: 'users_view'\n  },\n  {\n    title: 'التنبيهات',\n    href: '/notifications',\n    icon: Bell\n  },\n  {\n    title: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    permission: 'settings_view'\n  },\n  {\n    title: 'إصلاح البيانات',\n    href: '/fix-data',\n    icon: Wrench,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار الطباعة',\n    href: '/test-print',\n    icon: Printer,\n    permission: 'sales_view'\n  },\n  {\n    title: 'تشخيص البيانات',\n    href: '/debug-data',\n    icon: Bug,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار المبيعات',\n    href: '/debug-sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  }\n]\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nexport default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {\n  const pathname = usePathname()\n  const { hasPermission, permissions } = usePermissions()\n\n\n\n  const menuItems = getMenuItems(permissions)\n\n  // إغلاق القائمة عند تغيير الصفحة\n  useEffect(() => {\n    if (onClose) {\n      onClose()\n    }\n  }, [pathname, onClose])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.getElementById('mobile-sidebar')\n      const menuButton = document.getElementById('mobile-menu-button')\n\n      if (sidebar && !sidebar.contains(event.target as Node) &&\n          menuButton && !menuButton.contains(event.target as Node)) {\n        if (onClose) {\n          onClose()\n        }\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  // تصفية العناصر بناءً على الصلاحيات\n  const visibleMenuItems = menuItems.filter(item => {\n    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)\n    return hasPermission(item.permission as any)\n  })\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Desktop Sidebar - Always visible on desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:right-0 md:z-30\">\n        <div className=\"flex flex-col flex-grow bg-gradient-to-b from-white to-gray-50 shadow-xl border-l border-gray-200\">\n          <div className=\"flex items-center gap-3 p-6 bg-gradient-to-r from-blue-600 to-indigo-700 text-white\">\n            <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n              <Pill className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-bold\">نظام الصيدلية</h1>\n              <p className=\"text-sm text-blue-100\">مكتب لارين العلمي</p>\n            </div>\n          </div>\n\n          <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n            {visibleMenuItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'\n                      : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800'\n                  }`}\n                >\n                  <Icon className={`h-5 w-5 ${isActive ? 'text-white' : ''}`} />\n                  <span className=\"font-medium\">{item.title}</span>\n                  {isActive && (\n                    <div className=\"mr-auto w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          <div className=\"p-4\">\n            <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200\">\n              <div className=\"flex items-center justify-center gap-2 mb-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <p className=\"text-xs text-gray-600 font-medium\">متصل</p>\n              </div>\n              <p className=\"text-xs text-gray-500\">© 2024 مكتب لارين العلمي</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Sidebar - Only visible on mobile when open */}\n      <div\n        id=\"mobile-sidebar\"\n        className=\"md:hidden\"\n        style={{\n          position: 'fixed',\n          top: 0,\n          right: isOpen ? '0px' : '-300px',\n          height: '100vh',\n          width: '280px',\n          backgroundColor: 'white',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          borderLeft: '1px solid #e5e7eb',\n          transition: 'right 0.3s ease-in-out',\n          zIndex: 9999,\n          overflow: 'auto'\n        }}\n      >\n        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>\n          {/* Header */}\n          <div style={{\n            padding: '16px',\n            background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n            borderRadius: '12px',\n            color: 'white',\n            marginBottom: '24px',\n            textAlign: 'center'\n          }}>\n            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>نظام الصيدلية</h1>\n            <p style={{ fontSize: '14px', margin: '8px 0 0 0', opacity: 0.9 }}>مكتب لارين العلمي</p>\n          </div>\n\n          {/* Navigation Menu */}\n          <div style={{ marginBottom: '24px' }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: '#374151',\n              borderBottom: '2px solid #e5e7eb',\n              paddingBottom: '8px'\n            }}>القائمة الرئيسية</h3>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              {visibleMenuItems.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <a\n                    key={item.href}\n                    href={item.href}\n                    onClick={onClose}\n                    style={{\n                      padding: '12px 16px',\n                      backgroundColor: isActive ? '#3b82f6' : '#f8fafc',\n                      color: isActive ? 'white' : '#374151',\n                      textDecoration: 'none',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '12px',\n                      border: isActive ? 'none' : '1px solid #e5e7eb',\n                      transition: 'all 0.2s ease',\n                      fontSize: '14px',\n                      fontWeight: isActive ? 'bold' : 'normal'\n                    }}\n                  >\n                    <Icon style={{ width: '20px', height: '20px' }} />\n                    {item.title}\n                  </a>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Close Button */}\n          <div style={{ marginTop: 'auto', paddingTop: '16px' }}>\n            <button\n              onClick={onClose}\n              style={{\n                width: '100%',\n                padding: '12px',\n                backgroundColor: '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                fontSize: '16px',\n                fontWeight: 'bold',\n                cursor: 'pointer',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}\n              onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}\n            >\n              إغلاق القائمة\n            </button>\n          </div>\n\n        </div>\n      </div>\n\n      {/* Overlay */}\n      {isOpen && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 9998\n          }}\n          onClick={onClose}\n        />\n      )}\n    </>\n  )\n}\n\n// تصدير دالة للتحكم في القائمة من مكونات أخرى\nexport const useSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAkCA,MAAM,eAAe,CAAC,cAAiC;QACrD;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;KACD;AAOc,SAAS;QAAQ,EAAE,SAAS,KAAK,EAAE,OAAO,EAAgB,GAAzC,iEAA4C,CAAC;;IAC3E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAIpD,MAAM,YAAY,aAAa;IAE/B,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;4BAAG;QAAC;QAAU;KAAQ;IAEtB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,aAAa,SAAS,cAAc,CAAC;oBAE3C,IAAI,WAAW,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KACzC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5D,IAAI,SAAS;4BACX;wBACF;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;gBACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;4BAAG;QAAC;QAAQ;KAAQ;IAEpB,oCAAoC;IACpC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,KAAK,6CAA6C;;QAC/E,OAAO,cAAc,KAAK,UAAU;IACtC;IAEA,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC;gCACrB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,kFAIX,OAHC,WACI,sEACA;;sDAGN,6LAAC;4CAAK,WAAW,AAAC,WAAuC,OAA7B,WAAW,eAAe;;;;;;sDACtD,6LAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;wCACxC,0BACC,6LAAC;4CAAI,WAAU;;;;;;;mCAXZ,KAAK,IAAI;;;;;4BAepB;;;;;;sCAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;kDAEnD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBACC,IAAG;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,OAAO,SAAS,QAAQ;oBACxB,QAAQ;oBACR,OAAO;oBACP,iBAAiB;oBACjB,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,UAAU;gBACZ;0BAEA,cAAA,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,QAAQ;wBAAQ,WAAW;oBAAO;;sCAE/D,6LAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,OAAO;gCACP,cAAc;gCACd,WAAW;4BACb;;8CACE,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAQ,YAAY;wCAAQ,QAAQ;oCAAE;8CAAG;;;;;;8CAChE,6LAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAQ,QAAQ;wCAAa,SAAS;oCAAI;8CAAG;;;;;;;;;;;;sCAIrE,6LAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CACjC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,cAAc;wCACd,eAAe;oCACjB;8CAAG;;;;;;8CAEH,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;8CAChE,iBAAiB,GAAG,CAAC,CAAC;wCACrB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wCAEvC,qBACE,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS;4CACT,OAAO;gDACL,SAAS;gDACT,iBAAiB,WAAW,YAAY;gDACxC,OAAO,WAAW,UAAU;gDAC5B,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,QAAQ,WAAW,SAAS;gDAC5B,YAAY;gDACZ,UAAU;gDACV,YAAY,WAAW,SAAS;4CAClC;;8DAEA,6LAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAQ,QAAQ;oDAAO;;;;;;gDAC5C,KAAK,KAAK;;2CAnBN,KAAK,IAAI;;;;;oCAsBpB;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAQ,YAAY;4BAAO;sCAClD,cAAA,6LAAC;gCACC,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,UAAU;oCACV,YAAY;oCACZ,QAAQ;oCACR,YAAY;gCACd;gCACA,aAAa,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;gCACrD,YAAY,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;0CACrD;;;;;;;;;;;;;;;;;;;;;;YASN,wBACC,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;;;AAKnB;GAnOwB;;QACL,qIAAA,CAAA,cAAW;QACW,kIAAA,CAAA,iBAAc;;;KAF/B;AAsOjB,MAAM,aAAa;;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO;QAAE;QAAQ;QAAW,eAAe,IAAM,UAAU,CAAC;IAAQ;AACtE;IAHa", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  X,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign\n} from 'lucide-react'\n\nexport default function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll\n  } = useNotifications()\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const filteredNotifications = activeTab === 'unread' \n    ? notifications.filter(n => !n.isRead)\n    : notifications\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-4 w-4 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-4 w-4 text-blue-500\" />\n    \n    // أيقونات حسب الفئة\n    if (category === 'inventory') return <Package className=\"h-4 w-4 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-4 w-4 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-4 w-4 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-4 w-4 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-4 w-4 text-gray-500\" />\n    \n    return <Bell className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'border-r-4 border-red-500 bg-red-50'\n      case 'high': return 'border-r-4 border-orange-500 bg-orange-50'\n      case 'medium': return 'border-r-4 border-yellow-500 bg-yellow-50'\n      case 'low': return 'border-r-4 border-blue-500 bg-blue-50'\n      default: return 'border-r-4 border-gray-500 bg-gray-50'\n    }\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n      setIsOpen(false)\n    }\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Bell className=\"h-5 w-5\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">التنبيهات</h3>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n            \n            {/* Tabs */}\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('all')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'all'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                الكل ({notifications.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('unread')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'unread'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                غير مقروءة ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Actions */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800\"\n                    >\n                      <CheckCheck className=\"h-3 w-3\" />\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n                <button\n                  onClick={clearAll}\n                  className=\"flex items-center gap-1 text-xs text-red-600 hover:text-red-800\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  مسح الكل\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500\">\n                  {activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                      !notification.isRead ? getPriorityColor(notification.priority) : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                              {notification.message}\n                            </p>\n                            \n                            {/* Action Button */}\n                            {notification.actionUrl && (\n                              <button className=\"inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2\">\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض التفاصيل'}\n                              </button>\n                            )}\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-1 mr-2\">\n                            {!notification.isRead && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation()\n                                  markAsRead(notification.id)\n                                }}\n                                className=\"p-1 text-gray-400 hover:text-blue-600\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                removeNotification(notification.id)\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-600\"\n                              title=\"حذف\"\n                            >\n                              <X className=\"h-3 w-3\" />\n                            </button>\n                          </div>\n                        </div>\n                        \n                        {/* Time */}\n                        <div className=\"flex items-center gap-1 mt-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(notification.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  router.push('/notifications')\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع التنبيهات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAwBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;qEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG,EAAE;IAEL,MAAM,wBAAwB,cAAc,WACxC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IACnC;IAEJ,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,oBAAoB;QACpB,IAAI,aAAa,aAAa,qBAAO,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,AAAC,OAAoB,OAAd,eAAc;QAEpD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,AAAC,OAAkB,OAAZ,aAAY;QAEhD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,AAAC,OAAiB,OAAX,YAAW;IAC3B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;YAClC,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,QACV,8BACA;;4CAEP;4CACQ,cAAc,MAAM;4CAAC;;;;;;;kDAE9B,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,WACV,8BACA;;4CAEP;4CACc;4CAAY;;;;;;;;;;;;;;;;;;;oBAM9B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,cAAc,mBACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAQtC,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CACV,cAAc,WAAW,+BAA+B;;;;;;;;;;;iDAI7D,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC;oCAEC,WAAW,AAAC,yDAEX,OADC,CAAC,aAAa,MAAM,GAAG,iBAAiB,aAAa,QAAQ,IAAI;oCAEnE,SAAS,IAAM,wBAAwB;8CAEvC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAW,AAAC,uBAEf,OADC,CAAC,aAAa,MAAM,GAAG,kBAAkB;kFAExC,aAAa,KAAK;;;;;;kFAErB,6LAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;oEAItB,aAAa,SAAS,kBACrB,6LAAC;wEAAO,WAAU;;0FAChB,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,aAAa,WAAW,IAAI;;;;;;;;;;;;;0EAMnC,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,WAAW,aAAa,EAAE;wEAC5B;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAGrB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;;;;;;;mCAhEtC,aAAa,EAAE;;;;;;;;;;;;;;;oBA2E7B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;gCACP,OAAO,IAAI,CAAC;gCACZ,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GA7QwB;;QAIP,qIAAA,CAAA,YAAS;QASpB,0IAAA,CAAA,mBAAgB;;;KAbE", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileMenuButton.tsx"], "sourcesContent": ["'use client'\n\nimport { Menu, X } from 'lucide-react'\n\ninterface MobileMenuButtonProps {\n  isOpen: boolean\n  onClick: () => void\n}\n\nexport default function MobileMenuButton({ isOpen, onClick }: MobileMenuButtonProps) {\n  const handleClick = () => {\n    onClick()\n  }\n\n  return (\n    <button\n      id=\"mobile-menu-button\"\n      onClick={handleClick}\n      className=\"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center\"\n      aria-label={isOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n    >\n      {isOpen ? (\n        <X className=\"h-6 w-6\" />\n      ) : (\n        <Menu className=\"h-6 w-6\" />\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AASe,SAAS,iBAAiB,KAA0C;QAA1C,EAAE,MAAM,EAAE,OAAO,EAAyB,GAA1C;IACvC,MAAM,cAAc;QAClB;IACF;IAEA,qBACE,6LAAC;QACC,IAAG;QACH,SAAS;QACT,WAAU;QACV,cAAY,SAAS,kBAAkB;kBAEtC,uBACC,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;iCAEb,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;KAnBwB", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport NotificationDropdown from './NotificationDropdown'\nimport MobileMenuButton from './MobileMenuButton'\nimport {\n  Search,\n  User,\n  LogOut,\n  Settings,\n  Shield,\n  ChevronDown,\n  Activity\n} from 'lucide-react'\n\ninterface HeaderProps {\n  onMobileMenuToggle?: () => void\n  isMobileMenuOpen?: boolean\n}\n\nexport default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, logout } = useAuth()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    const roleNames: Record<string, string> = {\n      admin: 'مدير النظام',\n      manager: 'مدير',\n      pharmacist: 'صيدلي',\n      cashier: 'كاشير',\n      viewer: 'مشاهد'\n    }\n    return roleNames[role] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors: Record<string, string> = {\n      admin: 'bg-red-600',\n      manager: 'bg-purple-600',\n      pharmacist: 'bg-blue-600',\n      cashier: 'bg-green-600',\n      viewer: 'bg-gray-600'\n    }\n    return colors[role] || 'bg-gray-600'\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 md:right-64 z-40 header-mobile\">\n      <div className=\"flex items-center justify-between h-full px-3 md:px-6\">\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Mobile Menu Button - Only visible on mobile */}\n          <div className=\"md:hidden\">\n            <MobileMenuButton\n              isOpen={isMobileMenuOpen}\n              onClick={onMobileMenuToggle || (() => console.log('⚠️ No onMobileMenuToggle function provided!'))}\n            />\n          </div>\n\n          {/* Mobile Search - Hidden on very small screens */}\n          <div className=\"relative hidden sm:hidden md:block\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث...\"\n              className=\"pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile\"\n            />\n          </div>\n\n          {/* Mobile Search Icon - Visible only on small screens */}\n          <button className=\"md:hidden p-2 hover:bg-gray-100 rounded-lg\">\n            <Search className=\"h-5 w-5 text-gray-600\" />\n          </button>\n        </div>\n\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Notifications */}\n          <NotificationDropdown />\n\n          {/* User Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]\"\n            >\n              {/* Hide user info text on mobile */}\n              <div className=\"text-right hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-800\">{user?.full_name || 'مستخدم'}</p>\n                <p className=\"text-xs text-gray-500\">{user ? getRoleDisplayName(user.role) : ''}</p>\n              </div>\n              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <ChevronDown className=\"h-4 w-4 text-gray-400 hidden md:block\" />\n            </button>\n\n            {/* Dropdown Menu */}\n            {showUserMenu && (\n              <div className=\"absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile\">\n                {/* User Info */}\n                <div className=\"px-4 py-3 border-b border-gray-100\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                      <p className=\"text-xs text-gray-500\">@{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :\n                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :\n                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :\n                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      <Shield className=\"h-3 w-3 ml-1\" />\n                      {user ? getRoleDisplayName(user.role) : ''}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/profile')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    الملف الشخصي\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/activity-log')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Activity className=\"h-4 w-4\" />\n                    سجل النشاطات\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/settings')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    الإعدادات\n                  </button>\n                </div>\n\n                {/* Logout */}\n                <div className=\"border-t border-gray-100 pt-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      handleLogout()\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAsBe,SAAS;QAAO,EAAE,kBAAkB,EAAE,mBAAmB,KAAK,EAAe,GAA7D,iEAAgE,CAAC;;IAC9F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAoC;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;oCACf,QAAQ;oCACR,SAAS,sBAAsB,CAAC,IAAM,QAAQ,GAAG,CAAC,8CAA8C;;;;;;;;;;;0CAKpG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6IAAA,CAAA,UAAoB;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAyB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;0DAE/E,6LAAC;gDAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0DAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0EAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAqC,iBAAA,2BAAA,KAAM,SAAS;;;;;;kFACjE,6LAAC;wEAAE,WAAU;;4EAAwB;4EAAE,iBAAA,2BAAA,KAAM,QAAQ;;;;;;;kFACrD,6LAAC;wEAAE,WAAU;kFAAyB,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,AAAC,uEAMjB,OALC,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU,4BACzB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,kCAC3B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,eAAe,8BAC9B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,gCAC3B;;8EAEA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAI9B,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIlC,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB;oDACF;oDACA,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC;GA9KwB;;QAEG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <AlertTriangle className=\"h-12 w-12 text-red-500\" />\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 mb-2\">حدث خطأ غير متوقع</h1>\n            \n            <p className=\"text-gray-600 mb-4\">\n              عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.\n            </p>\n            \n            {this.state.error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left\">\n                <p className=\"text-sm text-red-800 font-mono\">\n                  {this.state.error.message}\n                </p>\n              </div>\n            )}\n            \n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={this.retry}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                إعادة المحاولة\n              </button>\n              \n              <button\n                onClick={() => window.location.reload()}\n                className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\n              >\n                إعادة تحميل الصفحة\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  تفاصيل الخطأ (للمطورين)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                  {this.state.error?.stack}\n                  {'\\n\\n'}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for functional components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null)\n\n  const resetError = React.useCallback(() => {\n    setError(null)\n  }, [])\n\n  const handleError = React.useCallback((error: Error) => {\n    console.error('Error caught by useErrorHandler:', error)\n    setError(error)\n  }, [])\n\n  React.useEffect(() => {\n    if (error) {\n      throw error\n    }\n  }, [error])\n\n  return { handleError, resetError }\n}\n\n// Safe component wrapper\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;;AAsFa;;;AApFb;AACA;AAAA;;;;AAHA;;;AAgBA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAMzC,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAMA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAkDV,mBAEA;YAnDb,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAIjC,IAAI,CAAC,KAAK,CAAC,KAAK,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAI,CAAC,KAAK;oCACnB,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC7D,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;;yCACZ,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,cAAhB,wCAAA,kBAAkB,KAAK;wCACvB;yCACA,wBAAA,IAAI,CAAC,KAAK,CAAC,SAAS,cAApB,4CAAA,sBAAsB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;QAOnD;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAvFA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QAmBR,+KAAA,SAAQ;YACN,IAAI,CAAC,QAAQ,CAAC;gBAAE,UAAU;gBAAO,OAAO;gBAAW,WAAW;YAAU;QAC1E;QApBE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AAqFF;AAGO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,WAAW;mDAAC;YACnC,SAAS;QACX;kDAAG,EAAE;IAEL,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,WAAW;oDAAC,CAAC;YACrC,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;mDAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,OAAO;gBACT,MAAM;YACR;QACF;oCAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC;GAnBgB;AAsBT,SAAS,kBACd,SAAiC,EACjC,QAAmE;IAEnE,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;YAAc,UAAU;sBACvB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,AAAC,qBAA4D,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAE5F,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ToastNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  X,\n  CheckCircle,\n  AlertTriangle,\n  Info,\n  XCircle,\n  ExternalLink\n} from 'lucide-react'\n\ninterface ToastNotification {\n  id: string\n  type: 'success' | 'warning' | 'error' | 'info'\n  title: string\n  message: string\n  actionUrl?: string\n  actionLabel?: string\n  duration?: number\n}\n\nexport default function ToastNotifications() {\n  const [toasts, setToasts] = useState<ToastNotification[]>([])\n  const { notifications } = useNotifications()\n\n  // مراقبة التنبيهات الجديدة وعرضها كـ Toast\n  useEffect(() => {\n    const latestNotification = notifications[0]\n    if (latestNotification && !latestNotification.isRead) {\n      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)\n      const notificationTime = new Date(latestNotification.createdAt).getTime()\n      const now = new Date().getTime()\n      const diffInMinutes = (now - notificationTime) / (1000 * 60)\n      \n      if (diffInMinutes < 1) {\n        showToast({\n          id: latestNotification.id,\n          type: latestNotification.type,\n          title: latestNotification.title,\n          message: latestNotification.message,\n          actionUrl: latestNotification.actionUrl,\n          actionLabel: latestNotification.actionLabel,\n          duration: getDurationByPriority(latestNotification.priority)\n        })\n      }\n    }\n  }, [notifications])\n\n  const getDurationByPriority = (priority: string): number => {\n    switch (priority) {\n      case 'critical': return 10000 // 10 ثواني\n      case 'high': return 7000     // 7 ثواني\n      case 'medium': return 5000   // 5 ثواني\n      case 'low': return 3000      // 3 ثواني\n      default: return 5000\n    }\n  }\n\n  const showToast = (toast: ToastNotification) => {\n    // تجنب التكرار\n    if (toasts.find(t => t.id === toast.id)) return\n\n    setToasts(prev => [...prev, toast])\n\n    // إزالة التنبيه تلقائياً بعد المدة المحددة\n    setTimeout(() => {\n      removeToast(toast.id)\n    }, toast.duration || 5000)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const getToastIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getToastStyles = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-20 left-4 md:left-72 z-[60] space-y-3 max-w-sm\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className={`w-full shadow-lg rounded-lg border p-3 md:p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}\n        >\n          <div className=\"flex items-start gap-2 md:gap-3\">\n            {/* Icon */}\n            <div className=\"flex-shrink-0 mt-0.5\">\n              {getToastIcon(toast.type)}\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 min-w-0\">\n              <h4 className=\"text-xs md:text-sm font-medium mb-1\">\n                {toast.title}\n              </h4>\n              <p className=\"text-xs md:text-sm opacity-90 line-clamp-2\">\n                {toast.message}\n              </p>\n\n              {/* Action Button */}\n              {toast.actionUrl && (\n                <a\n                  href={toast.actionUrl}\n                  className=\"inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline min-h-[32px]\"\n                >\n                  <ExternalLink className=\"h-3 w-3\" />\n                  {toast.actionLabel || 'عرض التفاصيل'}\n                </a>\n              )}\n            </div>\n\n            {/* Close Button */}\n            <button\n              onClick={() => removeToast(toast.id)}\n              className=\"flex-shrink-0 p-2 hover:bg-black hover:bg-opacity-10 rounded min-h-[44px] min-w-[44px] flex items-center justify-center\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// إضافة الأنيميشن إلى CSS\nconst toastStyles = `\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`\n\n// إضافة الأنيميشن إلى الصفحة\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style')\n  styleElement.textContent = toastStyles\n  document.head.appendChild(styleElement)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAuBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,qBAAqB,aAAa,CAAC,EAAE;YAC3C,IAAI,sBAAsB,CAAC,mBAAmB,MAAM,EAAE;gBACpD,uDAAuD;gBACvD,MAAM,mBAAmB,IAAI,KAAK,mBAAmB,SAAS,EAAE,OAAO;gBACvE,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,MAAM,gBAAgB,CAAC,MAAM,gBAAgB,IAAI,CAAC,OAAO,EAAE;gBAE3D,IAAI,gBAAgB,GAAG;oBACrB,UAAU;wBACR,IAAI,mBAAmB,EAAE;wBACzB,MAAM,mBAAmB,IAAI;wBAC7B,OAAO,mBAAmB,KAAK;wBAC/B,SAAS,mBAAmB,OAAO;wBACnC,WAAW,mBAAmB,SAAS;wBACvC,aAAa,mBAAmB,WAAW;wBAC3C,UAAU,sBAAsB,mBAAmB,QAAQ;oBAC7D;gBACF;YACF;QACF;uCAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAY,OAAO,MAAM,WAAW;;YACzC,KAAK;gBAAQ,OAAO,KAAS,UAAU;;YACvC,KAAK;gBAAU,OAAO,KAAO,UAAU;;YACvC,KAAK;gBAAO,OAAO,KAAU,UAAU;;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,eAAe;QACf,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;QAEzC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,2CAA2C;QAC3C,WAAW;YACT,YAAY,MAAM,EAAE;QACtB,GAAG,MAAM,QAAQ,IAAI;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,WAAW,AAAC,6GAAuI,OAA3B,eAAe,MAAM,IAAI;0BAEjJ,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,IAAI;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,6LAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;gCAIf,MAAM,SAAS,kBACd,6LAAC;oCACC,MAAM,MAAM,SAAS;oCACrB,WAAU;;sDAEV,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,MAAM,WAAW,IAAI;;;;;;;;;;;;;sCAM5B,6LAAC;4BACC,SAAS,IAAM,YAAY,MAAM,EAAE;4BACnC,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;eAnCZ,MAAM,EAAE;;;;;;;;;;AA0CvB;GAnIwB;;QAEI,0IAAA,CAAA,mBAAgB;;;KAFpB;AAqIxB,0BAA0B;AAC1B,MAAM,cAAe;AAwBrB,6BAA6B;AAC7B,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileOptimizer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\nexport default function MobileOptimizer() {\n  useEffect(() => {\n    // Detect mobile device\n    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    if (isMobile || isTouch) {\n      // Add mobile-specific classes to body\n      document.body.classList.add('mobile-device', 'touch-device')\n      \n      // Prevent zoom on input focus (iOS)\n      const viewport = document.querySelector('meta[name=viewport]')\n      if (viewport) {\n        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover')\n      }\n\n      // Add touch-friendly classes to interactive elements\n      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')\n      interactiveElements.forEach(element => {\n        element.classList.add('touch-friendly')\n      })\n\n      // Optimize scrolling\n      document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')\n      document.body.style.setProperty('-webkit-overflow-scrolling', 'touch')\n\n      // Prevent bounce scrolling on iOS\n      document.addEventListener('touchmove', (e) => {\n        if (e.target === document.body) {\n          e.preventDefault()\n        }\n      }, { passive: false })\n\n      // Add safe area support\n      if (CSS.supports('padding: max(0px)')) {\n        document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')\n        document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')\n        document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')\n        document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')\n      }\n\n      // Optimize performance for mobile\n      const optimizeElement = (element: Element) => {\n        if (element instanceof HTMLElement) {\n          element.style.setProperty('transform', 'translateZ(0)')\n          element.style.setProperty('will-change', 'transform')\n        }\n      }\n\n      // Apply optimizations to animated elements\n      const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift')\n      animatedElements.forEach(optimizeElement)\n\n      // Handle orientation change\n      const handleOrientationChange = () => {\n        // Force repaint after orientation change\n        setTimeout(() => {\n          window.scrollTo(0, window.scrollY)\n        }, 100)\n      }\n\n      window.addEventListener('orientationchange', handleOrientationChange)\n      \n      // Cleanup\n      return () => {\n        window.removeEventListener('orientationchange', handleOrientationChange)\n      }\n    }\n  }, [])\n\n  useEffect(() => {\n    // Add PWA install prompt handling\n    let deferredPrompt: any\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      deferredPrompt = e\n      \n      // Show custom install button or banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'block'\n      }\n    }\n\n    const handleAppInstalled = () => {\n      console.log('PWA was installed')\n      deferredPrompt = null\n      \n      // Hide install banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'none'\n      }\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  return null // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,uBAAuB;YACvB,MAAM,WAAW,iEAAiE,IAAI,CAAC,UAAU,SAAS;YAC1G,MAAM,UAAU,kBAAkB,UAAU,UAAU,cAAc,GAAG;YAEvE,IAAI,YAAY,SAAS;gBACvB,sCAAsC;gBACtC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB;gBAE7C,oCAAoC;gBACpC,MAAM,WAAW,SAAS,aAAa,CAAC;gBACxC,IAAI,UAAU;oBACZ,SAAS,YAAY,CAAC,WAAW;gBACnC;gBAEA,qDAAqD;gBACrD,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;gBACtD,oBAAoB,OAAO;iDAAC,CAAA;wBAC1B,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;;gBAEA,qBAAqB;gBACrB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;gBACzE,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;gBAE9D,kCAAkC;gBAClC,SAAS,gBAAgB,CAAC;iDAAa,CAAC;wBACtC,IAAI,EAAE,MAAM,KAAK,SAAS,IAAI,EAAE;4BAC9B,EAAE,cAAc;wBAClB;oBACF;gDAAG;oBAAE,SAAS;gBAAM;gBAEpB,wBAAwB;gBACxB,IAAI,IAAI,QAAQ,CAAC,sBAAsB;oBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB;oBACpE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,4BAA4B;oBACvE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B;oBACrE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,2BAA2B;gBACxE;gBAEA,kCAAkC;gBAClC,MAAM;iEAAkB,CAAC;wBACvB,IAAI,mBAAmB,aAAa;4BAClC,QAAQ,KAAK,CAAC,WAAW,CAAC,aAAa;4BACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,eAAe;wBAC3C;oBACF;;gBAEA,2CAA2C;gBAC3C,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;gBACnD,iBAAiB,OAAO,CAAC;gBAEzB,4BAA4B;gBAC5B,MAAM;yEAA0B;wBAC9B,yCAAyC;wBACzC;iFAAW;gCACT,OAAO,QAAQ,CAAC,GAAG,OAAO,OAAO;4BACnC;gFAAG;oBACL;;gBAEA,OAAO,gBAAgB,CAAC,qBAAqB;gBAE7C,UAAU;gBACV;iDAAO;wBACL,OAAO,mBAAmB,CAAC,qBAAqB;oBAClD;;YACF;QACF;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,kCAAkC;YAClC,IAAI;YAEJ,MAAM;uEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,iBAAiB;oBAEjB,uCAAuC;oBACvC,MAAM,gBAAgB,SAAS,cAAc,CAAC;oBAC9C,IAAI,eAAe;wBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;oBAChC;gBACF;;YAEA,MAAM;gEAAqB;oBACzB,QAAQ,GAAG,CAAC;oBACZ,iBAAiB;oBAEjB,sBAAsB;oBACtB,MAAM,gBAAgB,SAAS,cAAc,CAAC;oBAC9C,IAAI,eAAe;wBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;6CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;oCAAG,EAAE;IAEL,OAAO,KAAK,yCAAyC;;AACvD;GA1GwB;KAAA", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/PWAInstallBanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Smartphone } from 'lucide-react'\n\nexport default function PWAInstallBanner() {\n  const [showBanner, setShowBanner] = useState(false)\n  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)\n\n  useEffect(() => {\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e)\n      setShowBanner(true)\n    }\n\n    const handleAppInstalled = () => {\n      setShowBanner(false)\n      setDeferredPrompt(null)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    // Check if app is already installed\n    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {\n      setShowBanner(false)\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  const handleInstallClick = async () => {\n    if (deferredPrompt) {\n      deferredPrompt.prompt()\n      const { outcome } = await deferredPrompt.userChoice\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt')\n      } else {\n        console.log('User dismissed the install prompt')\n      }\n      \n      setDeferredPrompt(null)\n      setShowBanner(false)\n    }\n  }\n\n  const handleDismiss = () => {\n    setShowBanner(false)\n    // Remember user dismissed the banner\n    localStorage.setItem('pwa-install-dismissed', 'true')\n  }\n\n  // Don't show if user previously dismissed\n  useEffect(() => {\n    const dismissed = localStorage.getItem('pwa-install-dismissed')\n    if (dismissed === 'true') {\n      setShowBanner(false)\n    }\n  }, [])\n\n  if (!showBanner) return null\n\n  return (\n    <div \n      id=\"pwa-install-banner\"\n      className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card\"\n    >\n      <div className=\"flex items-start gap-3\">\n        <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n          <Smartphone className=\"h-5 w-5\" />\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-sm md:text-base mb-1\">\n            تثبيت التطبيق\n          </h3>\n          <p className=\"text-blue-100 text-xs md:text-sm mb-3\">\n            ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت\n          </p>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleInstallClick}\n              className=\"bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1\"\n            >\n              <Download className=\"h-3 w-3 md:h-4 md:w-4\" />\n              تثبيت\n            </button>\n            <button\n              onClick={handleDismiss}\n              className=\"text-blue-100 hover:text-white transition-colors\"\n            >\n              <X className=\"h-4 w-4 md:h-5 md:w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;wEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,cAAc;gBAChB;;YAEA,MAAM;iEAAqB;oBACzB,cAAc;oBACd,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,oCAAoC;YACpC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;gBAChF,cAAc;YAChB;YAEA;8CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;qCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,eAAe,MAAM;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,qCAAqC;QACrC,aAAa,OAAO,CAAC,yBAAyB;IAChD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,cAAc,QAAQ;gBACxB,cAAc;YAChB;QACF;qCAAG,EAAE;IAEL,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;8BAExB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGhD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAjGwB;KAAA", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ScreenSizeIndicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Monitor, Tablet, Smartphone, Laptop } from 'lucide-react'\n\nexport default function ScreenSizeIndicator() {\n  const [screenInfo, setScreenInfo] = useState({\n    width: 0,\n    height: 0,\n    breakpoint: '',\n    device: '',\n    orientation: ''\n  })\n\n  useEffect(() => {\n    const updateScreenInfo = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n      \n      let breakpoint = ''\n      let device = ''\n      \n      if (width < 640) {\n        breakpoint = 'sm (< 640px)'\n        device = 'هاتف صغير'\n      } else if (width < 768) {\n        breakpoint = 'md (640px - 768px)'\n        device = 'هاتف كبير'\n      } else if (width < 1024) {\n        breakpoint = 'lg (768px - 1024px)'\n        device = 'تابلت'\n      } else if (width < 1280) {\n        breakpoint = 'xl (1024px - 1280px)'\n        device = 'لابتوب'\n      } else {\n        breakpoint = '2xl (> 1280px)'\n        device = 'كمبيوتر مكتبي'\n      }\n\n      const orientation = width > height ? 'أفقي' : 'عمودي'\n\n      setScreenInfo({\n        width,\n        height,\n        breakpoint,\n        device,\n        orientation\n      })\n    }\n\n    updateScreenInfo()\n    window.addEventListener('resize', updateScreenInfo)\n    window.addEventListener('orientationchange', updateScreenInfo)\n\n    return () => {\n      window.removeEventListener('resize', updateScreenInfo)\n      window.removeEventListener('orientationchange', updateScreenInfo)\n    }\n  }, [])\n\n  const getIcon = () => {\n    if (screenInfo.width < 768) return Smartphone\n    if (screenInfo.width < 1024) return Tablet\n    if (screenInfo.width < 1280) return Laptop\n    return Monitor\n  }\n\n  const Icon = getIcon()\n\n  return (\n    <div className=\"fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block\">\n      <div className=\"flex items-center gap-2 mb-1\">\n        <Icon className=\"h-4 w-4\" />\n        <span className=\"font-medium\">{screenInfo.device}</span>\n      </div>\n      <div className=\"space-y-1\">\n        <div>{screenInfo.width} × {screenInfo.height}</div>\n        <div>{screenInfo.breakpoint}</div>\n        <div>{screenInfo.orientation}</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;kEAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,MAAM,SAAS,OAAO,WAAW;oBAEjC,IAAI,aAAa;oBACjB,IAAI,SAAS;oBAEb,IAAI,QAAQ,KAAK;wBACf,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,KAAK;wBACtB,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,MAAM;wBACvB,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,MAAM;wBACvB,aAAa;wBACb,SAAS;oBACX,OAAO;wBACL,aAAa;wBACb,SAAS;oBACX;oBAEA,MAAM,cAAc,QAAQ,SAAS,SAAS;oBAE9C,cAAc;wBACZ;wBACA;wBACA;wBACA;wBACA;oBACF;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C;iDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,qBAAqB;gBAClD;;QACF;wCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI,WAAW,KAAK,GAAG,KAAK,OAAO,iNAAA,CAAA,aAAU;QAC7C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,yMAAA,CAAA,SAAM;QAC1C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,yMAAA,CAAA,SAAM;QAC1C,OAAO,2MAAA,CAAA,UAAO;IAChB;IAEA,MAAM,OAAO;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAe,WAAW,MAAM;;;;;;;;;;;;0BAElD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAK,WAAW,KAAK;4BAAC;4BAAI,WAAW,MAAM;;;;;;;kCAC5C,6LAAC;kCAAK,WAAW,UAAU;;;;;;kCAC3B,6LAAC;kCAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;AAIpC;GA7EwB;KAAA", "debugId": null}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport Header from './Header'\nimport ErrorBoundary from './ErrorBoundary'\nimport ToastNotifications from './ToastNotifications'\nimport MobileOptimizer from './MobileOptimizer'\nimport PWAInstallBanner from './PWAInstallBanner'\nimport ScreenSizeIndicator from './ScreenSizeIndicator'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <MobileOptimizer />\n      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n      <Header\n        onMobileMenuToggle={toggleMobileMenu}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n      <main className={`\n        mr-0 md:mr-64\n        mt-16\n        p-3 md:p-6\n        main-content-mobile\n        min-h-screen\n        w-full\n        max-w-full\n        overflow-x-hidden\n        safe-area-inset-bottom\n      `}>\n        <ErrorBoundary>\n          <div className=\"max-w-full overflow-x-auto container\">\n            {children}\n          </div>\n        </ErrorBoundary>\n      </main>\n      <ToastNotifications />\n      <PWAInstallBanner />\n      <ScreenSizeIndicator />\n\n      {/* Mobile overlay when sidebar is open */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAee,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IAChC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,gIAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;0BACtE,6LAAC,+HAAA,CAAA,UAAM;gBACL,oBAAoB;gBACpB,kBAAkB;;;;;;0BAEpB,6LAAC;gBAAK,WAAY;0BAWhB,cAAA,6LAAC,sIAAA,CAAA,UAAa;8BACZ,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;0BAIP,6LAAC,2IAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC,yIAAA,CAAA,UAAgB;;;;;0BACjB,6LAAC,4IAAA,CAAA,UAAmB;;;;;YAGnB,kCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAK7C;GA7CwB;KAAA", "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface Medicine {\n  id: string\n  name: string\n  category: string\n  manufacturer: string\n  active_ingredient: string\n  strength: string\n  form: string // tablet, capsule, syrup, etc.\n  unit_price: number\n  selling_price: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MedicineBatch {\n  id: string\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id: string\n  received_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Supplier {\n  id: string\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoice {\n  id: string\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  created_at: string\n}\n\nexport interface PurchaseInvoice {\n  id: string\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PurchaseInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  created_at: string\n}\n\nexport interface SalesReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface PurchaseReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface InventoryMovement {\n  id: string\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 2755, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\n\n// Medicine operations\nexport const addMedicine = async (medicineData: {\n  name: string\n  category: string\n  manufacturer?: string\n  active_ingredient?: string\n  strength?: string\n  form: string\n  unit_price: number\n  selling_price: number\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .insert([{\n        name: medicineData.name,\n        category: medicineData.category,\n        manufacturer: medicineData.manufacturer || '',\n        active_ingredient: medicineData.active_ingredient || '',\n        strength: medicineData.strength || '',\n        form: medicineData.form,\n        unit_price: medicineData.unit_price,\n        selling_price: medicineData.selling_price\n      }])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicines = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .select(`\n        *,\n        medicine_batches (\n          id,\n          batch_code,\n          expiry_date,\n          quantity,\n          cost_price,\n          selling_price,\n          supplier_id,\n          received_date\n        )\n      `)\n      .order('name')\n\n    if (error) {\n      console.warn('Supabase error fetching medicines, using localStorage:', error)\n      // Fallback to localStorage\n      return getMedicinesFromLocalStorage()\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching medicines:', error)\n    // Final fallback to localStorage\n    return getMedicinesFromLocalStorage()\n  }\n}\n\n// Helper function to get medicines from localStorage\nconst getMedicinesFromLocalStorage = () => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    // If no medicines in localStorage, create sample data\n    if (medicines.length === 0) {\n      console.log('🔄 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية...')\n      return createSampleMedicinesData()\n    }\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = medicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: batches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: batches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم تحميل ${medicinesWithBatches.length} دواء من localStorage`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error loading medicines from localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to create sample medicines data\nconst createSampleMedicinesData = () => {\n  try {\n    const sampleMedicines = [\n      {\n        id: 'med_1',\n        name: 'باراسيتامول 500 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الأدوية العراقية',\n        strength: '500mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_2',\n        name: 'أموكسيسيلين 250 مجم',\n        category: 'مضادات حيوية',\n        manufacturer: 'شركة بغداد للأدوية',\n        strength: '250mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_3',\n        name: 'أسبرين 100 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة النهرين',\n        strength: '100mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_4',\n        name: 'إيبوبروفين 400 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الرافدين',\n        strength: '400mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_5',\n        name: 'أوميبرازول 20 مجم',\n        category: 'أدوية المعدة',\n        manufacturer: 'شركة دجلة',\n        strength: '20mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    const sampleBatches = [\n      {\n        id: 'batch_1',\n        medicine_id: 'med_1',\n        batch_code: 'PAR001',\n        expiry_date: '2025-12-31',\n        quantity: 100,\n        cost_price: 500,\n        selling_price: 750,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_2',\n        medicine_id: 'med_2',\n        batch_code: 'AMX001',\n        expiry_date: '2025-06-30',\n        quantity: 50,\n        cost_price: 1000,\n        selling_price: 1500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_3',\n        medicine_id: 'med_3',\n        batch_code: 'ASP001',\n        expiry_date: '2026-03-31',\n        quantity: 200,\n        cost_price: 300,\n        selling_price: 500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_4',\n        medicine_id: 'med_4',\n        batch_code: 'IBU001',\n        expiry_date: '2025-09-30',\n        quantity: 75,\n        cost_price: 800,\n        selling_price: 1200,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_5',\n        medicine_id: 'med_5',\n        batch_code: 'OME001',\n        expiry_date: '2025-11-30',\n        quantity: 30,\n        cost_price: 1500,\n        selling_price: 2000,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Create sample customers\n    const sampleCustomers = [\n      {\n        id: 'cust_1',\n        name: 'أحمد محمد علي',\n        phone: '07701234567',\n        address: 'بغداد - الكرادة',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'cust_2',\n        name: 'فاطمة حسن',\n        phone: '07809876543',\n        address: 'بغداد - الجادرية',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Save to localStorage\n    localStorage.setItem('medicines', JSON.stringify(sampleMedicines))\n    localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))\n    localStorage.setItem('customers', JSON.stringify(sampleCustomers))\n\n    // Initialize empty arrays for invoices\n    localStorage.setItem('sales_invoices', JSON.stringify([]))\n    localStorage.setItem('sales_invoice_items', JSON.stringify([]))\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = sampleMedicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم إنشاء ${medicinesWithBatches.length} دواء تجريبي`)\n    console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)\n    console.log(`✅ تم إنشاء ${sampleCustomers.length} عميل تجريبي`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error creating sample medicines:', error)\n    return { success: false, error }\n  }\n}\n\n// Function to initialize system data\nexport const initializeSystemData = async () => {\n  try {\n    console.log('🔄 تهيئة بيانات النظام...')\n\n    // Check if we have basic data\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    if (medicines.length === 0 || batches.length === 0) {\n      console.log('📦 إنشاء البيانات الأساسية...')\n      return createSampleMedicinesData()\n    }\n\n    console.log(`✅ البيانات الأساسية موجودة: ${medicines.length} دواء، ${batches.length} دفعة`)\n    return { success: true, data: medicines }\n  } catch (error) {\n    console.error('Error initializing system data:', error)\n    return { success: false, error }\n  }\n}\n\n// Medicine batch operations\nexport const addMedicineBatch = async (batchData: {\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .insert([batchData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine batch:', error)\n    return { success: false, error }\n  }\n}\n\nexport const updateBatchQuantity = async (batchId: string, newQuantity: number) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .update({ quantity: newQuantity })\n      .eq('id', batchId)\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error updating batch quantity, using localStorage:', error)\n      // Fallback to localStorage\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found in localStorage' }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error updating batch quantity:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found' }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for batch update:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Sales operations\nexport const createSalesInvoice = async (invoiceData: {\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating sales invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addSalesInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for invoice items, using localStorage:', error)\n      // Fallback to localStorage - preserve existing medicine names\n      console.log('📦 العناصر الواردة للحفظ:', items)\n\n      const enhancedItems = items.map(item => {\n        // Use existing medicine name if available, otherwise enhance\n        const medicineName = item.medicine_name || item.medicineName\n\n        if (medicineName && medicineName !== 'غير محدد') {\n          console.log(`✅ استخدام اسم الدواء الموجود: ${medicineName}`)\n          return {\n            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicine_batches: {\n              batch_code: '',\n              expiry_date: '',\n              medicines: {\n                name: medicineName,\n                category: '',\n                manufacturer: '',\n                strength: '',\n                form: ''\n              }\n            },\n            created_at: new Date().toISOString()\n          }\n        } else {\n          console.log(`⚠️ لا يوجد اسم دواء، سيتم البحث عنه...`)\n          // Only enhance if no medicine name is available\n          return item\n        }\n      })\n\n      // Enhance items that still need medicine names\n      const itemsNeedingEnhancement = enhancedItems.filter(item =>\n        !item.medicine_name || item.medicine_name === 'غير محدد'\n      )\n\n      let finalItems = enhancedItems\n      if (itemsNeedingEnhancement.length > 0) {\n        console.log(`🔍 تحسين ${itemsNeedingEnhancement.length} عنصر يحتاج أسماء أدوية`)\n        const enhancedNeeded = await enhanceItemsWithMedicineNames(itemsNeedingEnhancement)\n\n        // Replace items that needed enhancement\n        finalItems = enhancedItems.map(item => {\n          if (!item.medicine_name || item.medicine_name === 'غير محدد') {\n            const enhanced = enhancedNeeded.find(e => e.medicine_batch_id === item.medicine_batch_id)\n            return enhanced || item\n          }\n          return item\n        })\n      }\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...finalItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ تم حفظ العناصر في localStorage:', finalItems)\n      return { success: true, data: finalItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding sales invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      console.log('🔄 Final fallback - حفظ العناصر مع الأسماء الموجودة')\n\n      const enhancedItems = items.map(item => {\n        const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        return {\n          id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          ...item,\n          medicine_name: medicineName,\n          medicineName: medicineName,\n          medicine_batches: {\n            batch_code: '',\n            expiry_date: '',\n            medicines: {\n              name: medicineName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          },\n          created_at: new Date().toISOString()\n        }\n      })\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...enhancedItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ Final fallback - تم حفظ العناصر:', enhancedItems)\n      return { success: true, data: enhancedItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Helper function to enhance items with medicine names\nconst enhanceItemsWithMedicineNames = async (items: any[]) => {\n  try {\n    // Get medicine data for names\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    return items.map(item => {\n      // Use existing medicine name if available, otherwise find from batch\n      let medicineName = item.medicine_name || item.medicineName\n\n      if (!medicineName || medicineName === 'غير محدد') {\n        // Find medicine name from batch\n        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n        const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n        medicineName = medicine?.name || 'غير محدد'\n      }\n\n      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      return {\n        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: medicineName,\n        medicineName: medicineName, // Add both for compatibility\n        medicine_batches: {\n          batch_code: batch?.batch_code || '',\n          expiry_date: batch?.expiry_date || '',\n          medicines: {\n            name: medicineName,\n            category: medicine?.category || '',\n            manufacturer: medicine?.manufacturer || '',\n            strength: medicine?.strength || '',\n            form: medicine?.form || ''\n          }\n        },\n        created_at: new Date().toISOString()\n      }\n    })\n  } catch (error) {\n    console.error('خطأ في تحسين العناصر بأسماء الأدوية:', error)\n    // Return items with basic structure if enhancement fails\n    return items.map(item => ({\n      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      ...item,\n      medicine_name: 'غير محدد',\n      created_at: new Date().toISOString()\n    }))\n  }\n}\n\n// Function to fix existing localStorage data with medicine names\nexport const fixLocalStorageInvoiceItems = () => {\n  try {\n    console.log('🔧 بدء إصلاح بيانات الفواتير في localStorage...')\n\n    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    console.log(`📦 عدد عناصر الفواتير: ${salesItems.length}`)\n    console.log(`💊 عدد الأدوية: ${medicines.length}`)\n    console.log(`📋 عدد الدفعات: ${batches.length}`)\n\n    let fixedCount = 0\n    let notFoundCount = 0\n    let createdBatchesCount = 0\n\n    const fixedItems = salesItems.map((item: any) => {\n      // Skip if already has proper medicine name structure\n      if (item.medicine_batches?.medicines?.name && item.medicine_batches.medicines.name !== 'غير محدد') {\n        return item\n      }\n\n      // Find medicine name from batch\n      let batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      let medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      // If batch not found, try to create a missing batch\n      if (!batch && item.medicine_batch_id) {\n        console.log(`🔍 محاولة إنشاء دفعة مفقودة: ${item.medicine_batch_id}`)\n\n        // Try to find medicine by name if available in item\n        if (item.medicine_name && item.medicine_name !== 'غير محدد') {\n          medicine = medicines.find((m: any) => m.name === item.medicine_name)\n        }\n\n        // If still no medicine found, use first available medicine as fallback\n        if (!medicine && medicines.length > 0) {\n          medicine = medicines[0]\n          console.log(`🔄 استخدام دواء افتراضي: ${medicine.name}`)\n        }\n\n        // Create missing batch if we have a medicine\n        if (medicine) {\n          batch = {\n            id: item.medicine_batch_id,\n            medicine_id: medicine.id,\n            batch_code: item.batch_code || `BATCH_${Date.now()}`,\n            expiry_date: item.expiry_date || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n            quantity: item.quantity || 0,\n            cost_price: item.unit_price || medicine.unit_price || 0,\n            selling_price: item.unit_price || medicine.selling_price || 0,\n            supplier_id: null,\n            received_date: new Date().toISOString().split('T')[0],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n\n          batches.push(batch)\n          createdBatchesCount++\n          console.log(`✅ تم إنشاء دفعة جديدة: ${batch.batch_code} للدواء: ${medicine.name}`)\n        }\n      }\n\n      if (medicine?.name) {\n        fixedCount++\n        console.log(`✅ إصلاح العنصر: ${medicine.name} (Batch: ${batch?.batch_code})`)\n\n        return {\n          ...item,\n          medicine_name: medicine.name,\n          medicineName: medicine.name, // Add both for compatibility\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: medicine.name,\n              category: medicine.category || '',\n              manufacturer: medicine.manufacturer || '',\n              strength: medicine.strength || '',\n              form: medicine.form || ''\n            }\n          }\n        }\n      } else {\n        notFoundCount++\n        console.log(`⚠️ لم يتم العثور على الدواء للعنصر: ${item.medicine_batch_id}`)\n\n        // Try to preserve any existing name\n        const existingName = item.medicine_name || item.medicineName || 'غير محدد'\n        return {\n          ...item,\n          medicine_name: existingName,\n          medicineName: existingName,\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: existingName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          }\n        }\n      }\n    })\n\n    // Save fixed data back to localStorage\n    localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems))\n\n    // Save updated batches if any were created\n    if (createdBatchesCount > 0) {\n      localStorage.setItem('medicine_batches', JSON.stringify(batches))\n      console.log(`📋 تم حفظ ${createdBatchesCount} دفعة جديدة`)\n    }\n\n    console.log(`✅ تم إصلاح ${fixedCount} عنصر من أصل ${salesItems.length}`)\n    console.log(`⚠️ لم يتم العثور على ${notFoundCount} عنصر`)\n\n    return {\n      success: true,\n      fixedCount,\n      notFoundCount,\n      createdBatchesCount,\n      totalCount: salesItems.length\n    }\n\n  } catch (error) {\n    console.error('❌ خطأ في إصلاح بيانات localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Enhanced function to clean and fix all localStorage data\nexport const cleanAndFixAllLocalStorageData = () => {\n  try {\n    console.log('🧹 بدء تنظيف وإصلاح جميع بيانات localStorage...')\n\n    // Step 1: Remove invalid or orphaned items\n    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n    const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n\n    console.log(`📊 البيانات الحالية:`)\n    console.log(`📦 عناصر الفواتير: ${salesItems.length}`)\n    console.log(`💊 الأدوية: ${medicines.length}`)\n    console.log(`📋 الدفعات: ${batches.length}`)\n    console.log(`🧾 الفواتير: ${invoices.length}`)\n\n    // Step 2: Remove duplicate items\n    const uniqueItems = salesItems.filter((item: any, index: number, self: any[]) =>\n      index === self.findIndex((i: any) => i.id === item.id)\n    )\n\n    // Step 3: Remove items with invalid invoice_id\n    const validInvoiceIds = new Set(invoices.map((inv: any) => inv.id))\n    const itemsWithValidInvoices = uniqueItems.filter((item: any) =>\n      validInvoiceIds.has(item.invoice_id)\n    )\n\n    // Step 4: Fix medicine batch references\n    const result = fixLocalStorageInvoiceItems()\n\n    // Step 5: Clean up orphaned batches (batches without medicines)\n    const validMedicineIds = new Set(medicines.map((med: any) => med.id))\n    const cleanBatches = batches.filter((batch: any) =>\n      validMedicineIds.has(batch.medicine_id)\n    )\n\n    // Save cleaned data\n    localStorage.setItem('sales_invoice_items', JSON.stringify(itemsWithValidInvoices))\n    localStorage.setItem('medicine_batches', JSON.stringify(cleanBatches))\n\n    const removedItems = salesItems.length - itemsWithValidInvoices.length\n    const removedBatches = batches.length - cleanBatches.length\n\n    console.log(`🧹 تم تنظيف البيانات:`)\n    console.log(`❌ تم حذف ${removedItems} عنصر مكرر أو غير صالح`)\n    console.log(`❌ تم حذف ${removedBatches} دفعة يتيمة`)\n    console.log(`✅ تم الاحتفاظ بـ ${itemsWithValidInvoices.length} عنصر صالح`)\n\n    return {\n      success: true,\n      removedItems,\n      removedBatches,\n      remainingItems: itemsWithValidInvoices.length,\n      fixResult: result\n    }\n\n  } catch (error) {\n    console.error('❌ خطأ في تنظيف البيانات:', error)\n    return { success: false, error }\n  }\n}\n\n// Function to create sample medicines if none exist\nexport const createSampleMedicinesIfNeeded = () => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n\n    if (medicines.length === 0) {\n      console.log('🏥 إنشاء أدوية تجريبية...')\n\n      const sampleMedicines = [\n        {\n          id: `medicine_${Date.now()}_1`,\n          name: 'باراسيتامول 500 مجم',\n          category: 'مسكنات',\n          manufacturer: 'شركة الأدوية المصرية',\n          active_ingredient: 'باراسيتامول',\n          strength: '500 مجم',\n          form: 'أقراص',\n          unit_price: 5.00,\n          selling_price: 8.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: `medicine_${Date.now()}_2`,\n          name: 'أموكسيسيلين 250 مجم',\n          category: 'مضادات حيوية',\n          manufacturer: 'شركة الأدوية العربية',\n          active_ingredient: 'أموكسيسيلين',\n          strength: '250 مجم',\n          form: 'كبسولات',\n          unit_price: 12.00,\n          selling_price: 18.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: `medicine_${Date.now()}_3`,\n          name: 'فيتامين سي 1000 مجم',\n          category: 'فيتامينات',\n          manufacturer: 'شركة الفيتامينات الطبيعية',\n          active_ingredient: 'حمض الأسكوربيك',\n          strength: '1000 مجم',\n          form: 'أقراص فوارة',\n          unit_price: 8.00,\n          selling_price: 12.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n      ]\n\n      localStorage.setItem('medicines', JSON.stringify(sampleMedicines))\n      console.log(`✅ تم إنشاء ${sampleMedicines.length} دواء تجريبي`)\n\n      // Create sample batches for these medicines\n      const sampleBatches = sampleMedicines.map((medicine, index) => ({\n        id: `batch_${Date.now()}_${index + 1}`,\n        medicine_id: medicine.id,\n        batch_code: `BATCH${String(index + 1).padStart(3, '0')}`,\n        expiry_date: new Date(Date.now() + (365 + index * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        quantity: 100 + index * 50,\n        cost_price: medicine.unit_price,\n        selling_price: medicine.selling_price,\n        supplier_id: null,\n        received_date: new Date().toISOString().split('T')[0],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }))\n\n      localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))\n      console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)\n\n      return {\n        success: true,\n        medicinesCreated: sampleMedicines.length,\n        batchesCreated: sampleBatches.length\n      }\n    }\n\n    return { success: true, medicinesCreated: 0, batchesCreated: 0 }\n\n  } catch (error) {\n    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to get medicine name from batch ID\nexport const getMedicineNameFromBatch = (batchId: string): string => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    const batch = batches.find((b: any) => b.id === batchId)\n    const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n    return medicine?.name || 'غير محدد'\n  } catch (error) {\n    console.error('خطأ في الحصول على اسم الدواء:', error)\n    return 'غير محدد'\n  }\n}\n\n// Purchase operations\nexport const createPurchaseInvoice = async (invoiceData: {\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating purchase invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase invoice:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addPurchaseInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice items, using localStorage:', error)\n      // Fallback to localStorage\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding purchase invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Inventory movement operations\nexport const addInventoryMovement = async (movementData: {\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('inventory_movements')\n      .insert([movementData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for inventory movement, using localStorage:', error)\n      // Fallback to localStorage\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding inventory movement:', error)\n\n    // Final fallback to localStorage\n    try {\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for inventory movement:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Customer operations\nexport const getCustomers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching customers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addCustomer = async (customerData: {\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .insert([customerData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding customer:', error)\n    return { success: false, error }\n  }\n}\n\n// Supplier operations\nexport const getSuppliers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching suppliers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addSupplier = async (supplierData: {\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .insert([supplierData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding supplier:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete sales transaction with inventory update\nexport const completeSalesTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    console.log('🔄 بدء معاملة المبيعات الكاملة...')\n    console.log('📄 بيانات الفاتورة:', invoiceData)\n    console.log('📦 العناصر:', items)\n\n    // Start transaction by creating invoice\n    console.log('📝 إنشاء الفاتورة...')\n    const invoiceResult = await createSalesInvoice(invoiceData)\n    console.log('📝 نتيجة إنشاء الفاتورة:', invoiceResult)\n\n    if (!invoiceResult.success) {\n      console.error('❌ فشل في إنشاء الفاتورة:', invoiceResult.error)\n      throw new Error(`فشل في إنشاء الفاتورة: ${invoiceResult.error?.message || 'خطأ غير معروف'}`)\n    }\n\n    const invoiceId = invoiceResult.data.id\n    console.log('✅ تم إنشاء الفاتورة بنجاح، ID:', invoiceId)\n\n    // Process each item\n    console.log('📦 معالجة عناصر الفاتورة...')\n    const itemsToAdd = []\n\n    for (const item of items) {\n      console.log('📦 معالجة العنصر:', item)\n      const batchId = item.medicine_batch_id || item.batchId\n\n      // Prepare item for batch insert with medicine name\n      itemsToAdd.push({\n        invoice_id: invoiceId,\n        medicine_batch_id: batchId,\n        quantity: item.quantity,\n        unit_price: item.unit_price || item.unitPrice,\n        total_price: item.total_price || item.totalPrice,\n        is_gift: item.is_gift || item.isGift || false,\n        medicine_name: item.medicine_name || item.medicineName || 'غير محدد'\n      })\n\n      // Update batch quantity (only for non-gift items)\n      if (!(item.is_gift || item.isGift)) {\n        try {\n          const currentBatch = await supabase\n            .from('medicine_batches')\n            .select('quantity')\n            .eq('id', batchId)\n            .single()\n\n          if (currentBatch.data) {\n            const newQuantity = Math.max(0, currentBatch.data.quantity - item.quantity)\n            await updateBatchQuantity(batchId, newQuantity)\n            console.log(`✅ تم تحديث كمية الدفعة ${batchId} إلى ${newQuantity}`)\n          }\n        } catch (batchError) {\n          console.warn('تحذير: فشل في تحديث كمية الدفعة:', batchError)\n        }\n      }\n\n      // Add inventory movement\n      try {\n        await addInventoryMovement({\n          medicine_batch_id: batchId,\n          movement_type: 'out',\n          quantity: item.quantity,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          notes: (item.is_gift || item.isGift) ? 'هدية' : undefined\n        })\n        console.log(`✅ تم إضافة حركة المخزون للدفعة ${batchId}`)\n      } catch (movementError) {\n        console.warn('تحذير: فشل في إضافة حركة المخزون:', movementError)\n      }\n    }\n\n    // Add all invoice items in batch\n    console.log('📝 إضافة عناصر الفاتورة...')\n    const itemsResult = await addSalesInvoiceItems(itemsToAdd)\n    if (!itemsResult.success) {\n      console.warn('تحذير: فشل في إضافة عناصر الفاتورة:', itemsResult.error)\n    } else {\n      console.log('✅ تم إضافة جميع عناصر الفاتورة بنجاح')\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      try {\n        await addCashTransaction({\n          transaction_type: 'income',\n          category: 'مبيعات',\n          amount: invoiceData.final_amount,\n          description: `فاتورة مبيعات رقم ${invoiceData.invoice_number}`,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          payment_method: 'cash',\n          notes: invoiceData.notes\n        })\n        console.log('✅ تم إضافة معاملة الصندوق')\n      } catch (cashError) {\n        console.warn('تحذير: فشل في إضافة معاملة الصندوق:', cashError)\n      }\n    }\n\n    console.log('🎉 تمت معاملة المبيعات بنجاح!')\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('❌ خطأ في إتمام معاملة المبيعات:', error)\n    return { success: false, error }\n  }\n}\n\n// Returns operations\nexport const getSalesInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for sales invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names are available\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\n// Get single sales invoice with full details for printing\nexport const getSalesInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names - FORCE REFRESH\n        console.log('🔧 بدء تحسين عناصر الفاتورة للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n        console.log('💊 عدد الأدوية المتاحة:', medicines.length)\n        console.log('📋 عدد الدفعات المتاحة:', batches.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Find medicine name from batch - ALWAYS recalculate\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          console.log('الدفعة الموجودة:', batch)\n\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n          console.log('الدواء الموجود:', medicine)\n\n          // Get the best available medicine name\n          const medicineName = medicine?.name || 'غير محدد'\n          console.log('اسم الدواء المحسوب:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName, // Add both for compatibility\n            medicine_batches: {\n              id: batch?.id,\n              batch_code: batch?.batch_code || item.batch_code || '',\n              expiry_date: batch?.expiry_date || item.expiry_date || '',\n              medicine_id: batch?.medicine_id,\n              medicines: {\n                id: medicine?.id,\n                name: medicineName,\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع العناصر')\n        console.log('النتيجة النهائية:', itemsWithNames)\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            sales_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Get single purchase invoice with full details for printing\nexport const getPurchaseInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single purchase invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names\n        console.log('🔧 بدء تحسين عناصر فاتورة المشتريات للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Use existing medicine name if available\n          const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n          console.log('اسم الدواء:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicines: {\n              name: medicineName,\n              category: item.category || '',\n              manufacturer: item.manufacturer || '',\n              strength: item.strength || '',\n              form: item.form || ''\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع عناصر المشتريات')\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            purchase_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchaseInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for purchase invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\nexport const createSalesReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase sales return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `sr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('sales_returns', JSON.stringify(existingReturns))\n\n      console.log('Sales return saved to localStorage:', returnWithId)\n      console.log('Total sales returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating sales return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const createPurchaseReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  supplier_id: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase purchase return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `pr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('purchase_returns', JSON.stringify(existingReturns))\n\n      console.log('Purchase return saved to localStorage:', returnWithId)\n      console.log('Total purchase returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating purchase return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const addReturnItems = async (items: Array<{\n  return_id: string\n  return_type: 'sales' | 'purchase'\n  medicine_batch_id?: string\n  medicine_id?: string\n  quantity: number\n  unit_price: number\n  total_price: number\n}>) => {\n  try {\n    // Try Supabase first\n    const tableName = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n    const { data, error } = await supabase\n      .from(tableName)\n      .insert(items.map(item => ({\n        return_id: item.return_id,\n        medicine_batch_id: item.medicine_batch_id,\n        medicine_id: item.medicine_id,\n        quantity: item.quantity,\n        unit_price: item.unit_price,\n        total_price: item.total_price\n      })))\n      .select()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase return items failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n      const itemsWithIds = items.map(item => ({\n        ...item,\n        id: `ri_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }))\n\n      const existingItems = JSON.parse(localStorage.getItem(storageKey) || '[]')\n      existingItems.push(...itemsWithIds)\n      localStorage.setItem(storageKey, JSON.stringify(existingItems))\n\n      return { success: true, data: itemsWithIds }\n    } catch (fallbackError) {\n      console.error('Error adding return items (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const processReturn = async (\n  returnType: 'sales' | 'purchase',\n  returnData: any,\n  items: any[]\n) => {\n  try {\n    // Create return record\n    const returnResult = returnType === 'sales'\n      ? await createSalesReturn(returnData)\n      : await createPurchaseReturn(returnData)\n\n    if (!returnResult.success) throw new Error('Failed to create return')\n\n    const returnId = returnResult.data.id\n\n    // Add return items\n    const returnItems = items.map(item => ({\n      return_id: returnId,\n      return_type: returnType,\n      medicine_batch_id: item.batchId,\n      medicine_id: item.medicineId,\n      quantity: item.quantity,\n      unit_price: item.unitPrice,\n      total_price: item.totalPrice\n    }))\n\n    await addReturnItems(returnItems)\n\n    // Try to update inventory (skip if Supabase is not available)\n    try {\n      // Update inventory for sales returns (add back to stock)\n      if (returnType === 'sales') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Add returned quantity back to stock\n                await updateBatchQuantity(item.batchId, batch.quantity + item.quantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'in',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مبيعات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n\n      // Update inventory for purchase returns (remove from stock)\n      if (returnType === 'purchase') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Remove returned quantity from stock\n                const newQuantity = Math.max(0, batch.quantity - item.quantity)\n                await updateBatchQuantity(item.batchId, newQuantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'out',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مشتريات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n    } catch (inventoryError) {\n      console.warn('Inventory update failed, but return was created successfully:', inventoryError)\n    }\n\n    return { success: true, data: { returnId } }\n  } catch (error) {\n    console.error('Error processing return:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getReturns = async () => {\n  // Always try localStorage first for faster response\n  try {\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    console.log('Loading returns from localStorage:', {\n      salesReturns: salesReturns.length,\n      purchaseReturns: purchaseReturns.length,\n      customers: customers.length,\n      suppliers: suppliers.length\n    })\n\n    // Enrich sales returns with customer data\n    const enrichedSalesReturns = salesReturns.map((returnItem: any) => {\n      const customer = customers.find((c: any) => c.id === returnItem.customer_id)\n      console.log(`Enriching sales return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'sales',\n        customers: customer ? {\n          name: customer.name,\n          phone: customer.phone,\n          address: customer.address\n        } : null,\n        customer_name: customer?.name || returnItem.customer_name || 'عميل غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // Enrich purchase returns with supplier data\n    const enrichedPurchaseReturns = purchaseReturns.map((returnItem: any) => {\n      const supplier = suppliers.find((s: any) => s.id === returnItem.supplier_id)\n      console.log(`Enriching purchase return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'purchase',\n        suppliers: supplier ? {\n          name: supplier.name,\n          phone: supplier.phone,\n          address: supplier.address\n        } : null,\n        supplier_name: supplier?.name || returnItem.supplier_name || 'مورد غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // If we have local data, return it immediately\n    if (enrichedSalesReturns.length > 0 || enrichedPurchaseReturns.length > 0) {\n      const allReturns = [\n        ...enrichedSalesReturns,\n        ...enrichedPurchaseReturns\n      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      console.log('Returning enriched returns from localStorage:', allReturns.slice(0, 2))\n      return { success: true, data: allReturns }\n    }\n  } catch (localError) {\n    console.warn('Error reading from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for returns...')\n    const [salesReturns, purchaseReturns] = await Promise.all([\n      supabase\n        .from('sales_returns')\n        .select(`\n          *,\n          customers (name, phone),\n          sales_return_items (\n            *,\n            medicine_batches (\n              batch_code,\n              medicines (name)\n            )\n          )\n        `)\n        .order('created_at', { ascending: false }),\n\n      supabase\n        .from('purchase_returns')\n        .select(`\n          *,\n          suppliers (name, contact_person),\n          purchase_return_items (\n            *,\n            medicines (name)\n          )\n        `)\n        .order('created_at', { ascending: false })\n    ])\n\n    const allReturns = [\n      ...(salesReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'sales',\n        customer_name: item.customers?.name || 'عميل غير محدد'\n      })),\n      ...(purchaseReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'purchase',\n        supplier_name: item.suppliers?.name || 'مورد غير محدد'\n      }))\n    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    console.log('Returning returns from Supabase:', allReturns.slice(0, 2))\n    return { success: true, data: allReturns }\n  } catch (error) {\n    console.warn('Supabase returns failed, returning empty array:', error)\n\n    // Return empty array if both localStorage and Supabase fail\n    return { success: true, data: [] }\n  }\n}\n\n// Get return by ID with full details\nexport const getReturnById = async (returnId: string) => {\n  try {\n    // Try localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    let foundReturn = salesReturns.find((r: any) => r.id === returnId)\n    let returnType = 'sales'\n\n    if (!foundReturn) {\n      foundReturn = purchaseReturns.find((r: any) => r.id === returnId)\n      returnType = 'purchase'\n    }\n\n    if (foundReturn) {\n      // Enrich with customer/supplier data\n      if (returnType === 'sales') {\n        const customer = customers.find((c: any) => c.id === foundReturn.customer_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'sales',\n          customers: customer ? {\n            name: customer.name,\n            phone: customer.phone,\n            address: customer.address\n          } : null,\n          customer_name: customer?.name || foundReturn.customer_name || 'عميل غير محدد'\n        }\n      } else {\n        const supplier = suppliers.find((s: any) => s.id === foundReturn.supplier_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'purchase',\n          suppliers: supplier ? {\n            name: supplier.name,\n            phone: supplier.phone,\n            address: supplier.address\n          } : null,\n          supplier_name: supplier?.name || foundReturn.supplier_name || 'مورد غير محدد'\n        }\n      }\n\n      console.log('Found enriched return in localStorage:', foundReturn)\n      return { success: true, data: foundReturn }\n    }\n\n    // If not in localStorage, try Supabase\n    if (!supabase) {\n      console.warn('Supabase not available, return not found')\n      return { success: false, error: 'Return not found' }\n    }\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            *,\n            medicines (name)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        return_type: 'sales',\n        return_items: salesReturn.sales_return_items || []\n      }\n      console.log('Found sales return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        return_type: 'purchase',\n        return_items: purchaseReturn.purchase_return_items || []\n      }\n      console.log('Found purchase return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.warn('Return not found:', returnId)\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error getting return by ID:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete purchase transaction with inventory update\nexport const completePurchaseTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    // Create purchase invoice\n    const invoiceResult = await createPurchaseInvoice(invoiceData)\n    if (!invoiceResult.success) throw new Error('Failed to create purchase invoice')\n\n    const invoiceId = invoiceResult.data.id\n\n    // Process each item\n    for (const item of items) {\n      let medicineId = item.medicineId\n\n      // If medicine doesn't exist, create it\n      if (!medicineId) {\n        const newMedicineResult = await addMedicine({\n          name: item.medicineName,\n          category: item.category || 'أخرى',\n          manufacturer: item.manufacturer || '',\n          active_ingredient: item.activeIngredient || '',\n          strength: item.strength || '',\n          form: item.form || 'tablet',\n          unit_price: item.unitCost,\n          selling_price: item.sellingPrice || item.unitCost * 1.5\n        })\n\n        if (newMedicineResult.success) {\n          medicineId = newMedicineResult.data.id\n        } else {\n          console.error('Failed to create medicine:', newMedicineResult.error)\n          continue\n        }\n      }\n\n      // Add purchase invoice item\n      await addPurchaseInvoiceItems([{\n        invoice_id: invoiceId,\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        quantity: item.quantity,\n        unit_cost: item.unitCost,\n        total_cost: item.totalCost,\n        expiry_date: item.expiryDate,\n        medicine_name: item.medicineName || 'غير محدد'\n      }])\n\n      // Create or update medicine batch\n      const batchResult = await addMedicineBatch({\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        expiry_date: item.expiryDate,\n        quantity: item.quantity,\n        cost_price: item.unitCost,\n        selling_price: item.sellingPrice || item.unitCost * 1.5, // Default markup\n        supplier_id: invoiceData.supplier_id\n      })\n\n      if (batchResult.success) {\n        // Add inventory movement\n        await addInventoryMovement({\n          medicine_batch_id: batchResult.data.id,\n          movement_type: 'in',\n          quantity: item.quantity,\n          reference_type: 'purchase',\n          reference_id: invoiceId\n        })\n      }\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      await addCashTransaction({\n        transaction_type: 'expense',\n        category: 'مشتريات',\n        amount: invoiceData.final_amount,\n        description: `فاتورة مشتريات رقم ${invoiceData.invoice_number}`,\n        reference_type: 'purchase',\n        reference_id: invoiceId,\n        payment_method: 'cash',\n        notes: invoiceData.notes\n      })\n    }\n\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('Error completing purchase transaction:', error)\n    return { success: false, error }\n  }\n}\n\n// Cash Box Operations\nexport const addCashTransaction = async (transactionData: {\n  transaction_type: 'income' | 'expense'\n  category: string\n  amount: number\n  description: string\n  reference_type?: string\n  reference_id?: string\n  payment_method: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .insert([transactionData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transaction failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const transactionWithId = {\n        ...transactionData,\n        id: `ct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingTransactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n      existingTransactions.push(transactionWithId)\n      localStorage.setItem('cash_transactions', JSON.stringify(existingTransactions))\n\n      console.log('Cash transaction saved to localStorage:', transactionWithId)\n      console.log('Total cash transactions in localStorage:', existingTransactions.length)\n\n      return { success: true, data: transactionWithId }\n    } catch (fallbackError) {\n      console.error('Error adding cash transaction (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const getCashTransactions = async (filters?: {\n  start_date?: string\n  end_date?: string\n  transaction_type?: string\n  category?: string\n}) => {\n  // Always try localStorage first for faster response\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    // If we have local data, filter and return it\n    if (transactions.length > 0) {\n      console.log('Loading cash transactions from localStorage:', transactions.length)\n\n      let filteredTransactions = transactions\n\n      if (filters?.start_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at >= filters.start_date)\n      }\n      if (filters?.end_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at <= filters.end_date)\n      }\n      if (filters?.transaction_type) {\n        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === filters.transaction_type)\n      }\n      if (filters?.category) {\n        filteredTransactions = filteredTransactions.filter(t => t.category === filters.category)\n      }\n\n      // Sort by created_at descending\n      filteredTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      return { success: true, data: filteredTransactions }\n    }\n  } catch (localError) {\n    console.warn('Error reading cash transactions from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash transactions...')\n    let query = supabase\n      .from('cash_transactions')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (filters?.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters?.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters?.transaction_type) {\n      query = query.eq('transaction_type', filters.transaction_type)\n    }\n    if (filters?.category) {\n      query = query.eq('category', filters.category)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transactions failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getCashBalance = async () => {\n  // Try localStorage first\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    if (transactions.length > 0) {\n      console.log('Calculating cash balance from localStorage:', transactions.length, 'transactions')\n\n      const balance = transactions.reduce((total: number, transaction: any) => {\n        return transaction.transaction_type === 'income'\n          ? total + transaction.amount\n          : total - transaction.amount\n      }, 0)\n\n      return { success: true, data: balance }\n    }\n  } catch (localError) {\n    console.warn('Error calculating balance from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash balance...')\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .select('transaction_type, amount')\n\n    if (error) throw error\n\n    const balance = data.reduce((total, transaction) => {\n      return transaction.transaction_type === 'income'\n        ? total + transaction.amount\n        : total - transaction.amount\n    }, 0)\n\n    return { success: true, data: balance }\n  } catch (error) {\n    console.warn('Supabase cash balance failed, returning 0:', error)\n    return { success: true, data: 0 }\n  }\n}\n\n// Customer and Supplier Debts\nexport const getCustomerDebts = async () => {\n  // Try localStorage first\n  try {\n    const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n\n    if (salesInvoices.length > 0) {\n      console.log('Loading customer debts from localStorage:', salesInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = salesInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found customer debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No sales invoices found, creating sample customer debts')\n      const sampleDebts = [\n        {\n          id: 'debt_1',\n          invoice_number: 'INV-001',\n          customer_id: 'cust_1',\n          customer_name: 'أحمد محمد علي',\n          final_amount: 150000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'أحمد محمد علي', phone: '07901111111' }\n        },\n        {\n          id: 'debt_2',\n          invoice_number: 'INV-003',\n          customer_id: 'cust_2',\n          customer_name: 'فاطمة حسن محمد',\n          final_amount: 85000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'فاطمة حسن محمد', phone: '07802222222' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading customer debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for customer debts...')\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        customer_id,\n        customer_name,\n        final_amount,\n        payment_status,\n        created_at,\n        customers (name, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase customer debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getSupplierDebts = async () => {\n  // Try localStorage first\n  try {\n    const purchaseInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n\n    if (purchaseInvoices.length > 0) {\n      console.log('Loading supplier debts from localStorage:', purchaseInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = purchaseInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found supplier debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No purchase invoices found, creating sample supplier debts')\n      const sampleDebts = [\n        {\n          id: 'debt_3',\n          invoice_number: 'PUR-001',\n          supplier_id: 'sup_1',\n          final_amount: 2500000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' }\n        },\n        {\n          id: 'debt_4',\n          invoice_number: 'PUR-004',\n          supplier_id: 'sup_2',\n          final_amount: 1800000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading supplier debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for supplier debts...')\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        supplier_id,\n        final_amount,\n        payment_status,\n        created_at,\n        suppliers (name, contact_person, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase supplier debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const updatePaymentStatus = async (\n  invoiceType: 'sales' | 'purchase',\n  invoiceId: string,\n  paymentStatus: string,\n  paidAmount?: number\n) => {\n  try {\n    // Try Supabase first\n    const tableName = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n\n    const { data, error } = await supabase\n      .from(tableName)\n      .update({\n        payment_status: paymentStatus,\n        ...(paidAmount && { paid_amount: paidAmount })\n      })\n      .eq('id', invoiceId)\n      .select()\n      .single()\n\n    if (error) throw error\n\n    // Add cash transaction if payment is completed\n    if (paymentStatus === 'paid' && paidAmount) {\n      const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n      const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n      await addCashTransaction({\n        transaction_type: transactionType,\n        category,\n        amount: paidAmount,\n        description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${data.invoice_number}`,\n        reference_type: invoiceType,\n        reference_id: invoiceId,\n        payment_method: 'cash'\n      })\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase payment update failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n      const invoices = JSON.parse(localStorage.getItem(storageKey) || '[]')\n\n      const invoiceIndex = invoices.findIndex((inv: any) => inv.id === invoiceId)\n      if (invoiceIndex !== -1) {\n        invoices[invoiceIndex].payment_status = paymentStatus\n        if (paidAmount) {\n          invoices[invoiceIndex].paid_amount = paidAmount\n        }\n\n        localStorage.setItem(storageKey, JSON.stringify(invoices))\n\n        // Add cash transaction if payment is completed\n        if (paymentStatus === 'paid' && paidAmount) {\n          const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n          const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n          await addCashTransaction({\n            transaction_type: transactionType,\n            category,\n            amount: paidAmount,\n            description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${invoices[invoiceIndex].invoice_number}`,\n            reference_type: invoiceType,\n            reference_id: invoiceId,\n            payment_method: 'cash'\n          })\n        }\n\n        console.log('Payment status updated in localStorage:', invoices[invoiceIndex])\n        return { success: true, data: invoices[invoiceIndex] }\n      } else {\n        throw new Error('Invoice not found in localStorage')\n      }\n    } catch (fallbackError) {\n      console.error('Error updating payment status (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\n// Advanced Reports Functions\nexport const getSalesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  customer_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            medicines (name, category)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.customer_id) {\n      query = query.eq('customer_id', filters.customer_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.sales_invoice_items.some((item: any) =>\n          item.medicine_batches?.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching sales report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchasesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  supplier_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone),\n        purchase_invoice_items (\n          *,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.supplier_id) {\n      query = query.eq('supplier_id', filters.supplier_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.purchase_invoice_items.some((item: any) =>\n          item.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching purchases report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getCustomerStatement = async (customerId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let salesQuery = supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('sales_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      salesQuery = salesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      salesQuery = salesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [salesResult, returnsResult] = await Promise.all([\n      salesQuery,\n      returnsQuery\n    ])\n\n    if (salesResult.error) throw salesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        sales: salesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching customer statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getSupplierStatement = async (supplierId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let purchasesQuery = supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('purchase_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      purchasesQuery = purchasesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      purchasesQuery = purchasesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [purchasesResult, returnsResult] = await Promise.all([\n      purchasesQuery,\n      returnsQuery\n    ])\n\n    if (purchasesResult.error) throw purchasesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        purchases: purchasesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching supplier statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicineMovementReport = async (medicineId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let query = supabase\n      .from('inventory_movements')\n      .select(`\n        *,\n        medicine_batches (\n          batch_code,\n          expiry_date,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine\n    const filteredData = data.filter(movement =>\n      movement.medicine_batches?.medicines?.id === medicineId\n    )\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching medicine movement report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getInventoryReport = async (filters: {\n  category?: string\n  low_stock?: boolean\n  expired?: boolean\n  expiring_soon?: boolean\n}) => {\n  try {\n    let query = supabase\n      .from('medicine_batches')\n      .select(`\n        *,\n        medicines (name, category, manufacturer)\n      `)\n      .order('expiry_date', { ascending: true })\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    let filteredData = data\n\n    // Filter by category\n    if (filters.category) {\n      filteredData = filteredData.filter(batch =>\n        batch.medicines?.category === filters.category\n      )\n    }\n\n    // Filter by low stock (less than 10 units)\n    if (filters.low_stock) {\n      filteredData = filteredData.filter(batch => batch.quantity < 10)\n    }\n\n    // Filter by expired\n    if (filters.expired) {\n      const today = new Date().toISOString().split('T')[0]\n      filteredData = filteredData.filter(batch => batch.expiry_date < today)\n    }\n\n    // Filter by expiring soon (within 30 days)\n    if (filters.expiring_soon) {\n      const thirtyDaysFromNow = new Date()\n      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)\n      const futureDate = thirtyDaysFromNow.toISOString().split('T')[0]\n      const today = new Date().toISOString().split('T')[0]\n\n      filteredData = filteredData.filter(batch =>\n        batch.expiry_date >= today && batch.expiry_date <= futureDate\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching inventory report:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status (simplified function)\nexport const updateReturnStatus = async (returnId: string, status: string, rejectionReason?: string) => {\n  const updates: any = { status }\n  if (rejectionReason) {\n    updates.rejection_reason = rejectionReason\n  }\n  return updateReturn(returnId, updates)\n}\n\n// Get return for printing with full details\nexport const getReturnForPrint = async (returnId: string) => {\n  try {\n    console.log('🔍 البحث عن المرتجع للطباعة:', returnId)\n\n    // Check localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const salesReturnItems = JSON.parse(localStorage.getItem('sales_return_items') || '[]')\n    const purchaseReturnItems = JSON.parse(localStorage.getItem('purchase_return_items') || '[]')\n\n    // Find in sales returns\n    let foundReturn = salesReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = salesReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const medicineBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n        let batchCode = item.batch_code || item.batchCode || ''\n        let expiryDate = item.expiry_date || item.expiryDate || ''\n\n        // Try to get medicine name from batch if not available\n        if (medicineName === 'غير محدد' && item.medicine_batch_id) {\n          const batch = medicineBatches.find((b: any) => b.id === item.medicine_batch_id)\n          if (batch) {\n            batchCode = batch.batch_number || batchCode\n            expiryDate = batch.expiry_date || expiryDate\n\n            const medicine = medicines.find((m: any) => m.id === batch.medicine_id)\n            if (medicine) {\n              medicineName = medicine.name || medicineName\n            }\n          }\n        }\n\n        // Try to get medicine name directly if still not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: batchCode,\n          expiry_date: expiryDate,\n          unit_price: item.unit_price || item.unitPrice || 0,\n          total_price: item.total_price || item.totalPrice || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Find in purchase returns\n    foundReturn = purchaseReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = purchaseReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        // Try to get medicine name directly if not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: item.batch_code || item.batchCode || '',\n          expiry_date: item.expiry_date || item.expiryDate || '',\n          unit_cost: item.unit_cost || item.unitCost || 0,\n          total_cost: item.total_cost || item.totalCost || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // If not found in localStorage, try Supabase\n    console.log('⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...')\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: (salesReturn.sales_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_price: item.unit_price || 0,\n          total_price: item.total_price || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: (purchaseReturn.purchase_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_cost: item.unit_cost || 0,\n          total_cost: item.total_cost || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.log('❌ لم يتم العثور على المرتجع')\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error fetching return for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status\nexport const updateReturn = async (returnId: string, updates: { status?: string, rejection_reason?: string }) => {\n  try {\n    // Update in localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n\n    // Find and update in sales returns\n    const salesIndex = salesReturns.findIndex((ret: any) => ret.id === returnId)\n    if (salesIndex !== -1) {\n      salesReturns[salesIndex] = { ...salesReturns[salesIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('sales_returns', JSON.stringify(salesReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('sales_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: salesReturns[salesIndex] }\n    }\n\n    // Find and update in purchase returns\n    const purchaseIndex = purchaseReturns.findIndex((ret: any) => ret.id === returnId)\n    if (purchaseIndex !== -1) {\n      purchaseReturns[purchaseIndex] = { ...purchaseReturns[purchaseIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('purchase_returns', JSON.stringify(purchaseReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('purchase_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: purchaseReturns[purchaseIndex] }\n    }\n\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error updating return:', error)\n    return { success: false, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,cAAc,OAAO;IAUhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;gBACP,MAAM,aAAa,IAAI;gBACvB,UAAU,aAAa,QAAQ;gBAC/B,cAAc,aAAa,YAAY,IAAI;gBAC3C,mBAAmB,aAAa,iBAAiB,IAAI;gBACrD,UAAU,aAAa,QAAQ,IAAI;gBACnC,MAAM,aAAa,IAAI;gBACvB,YAAY,aAAa,UAAU;gBACnC,eAAe,aAAa,aAAa;YAC3C;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAE,oPAaR,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,0DAA0D;YACvE,2BAA2B;YAC3B,OAAO;QACT;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,iCAAiC;QACjC,OAAO;IACT;AACF;AAEA,qDAAqD;AACrD,MAAM,+BAA+B;IACnC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,sDAAsD;QACtD,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,uCAAuC;QACvC,MAAM,uBAAuB,UAAU,GAAG,CAAC,CAAC,WAAkB,CAAC;gBAC7D,GAAG,QAAQ;gBACX,kBAAkB,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBAClF,SAAS,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YAC3E,CAAC;QAED,QAAQ,GAAG,CAAC,AAAC,cAAyC,OAA5B,qBAAqB,MAAM,EAAC;QACtD,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEA,kDAAkD;AAClD,MAAM,4BAA4B;IAChC,IAAI;QACF,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,MAAM,gBAAgB;YACpB;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,0BAA0B;QAC1B,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,uBAAuB;QACvB,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QACxD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QAEjD,uCAAuC;QACvC,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,EAAE;QACxD,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC,EAAE;QAE7D,uCAAuC;QACvC,MAAM,uBAAuB,gBAAgB,GAAG,CAAC,CAAC,WAAkB,CAAC;gBACnE,GAAG,QAAQ;gBACX,kBAAkB,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBACxF,SAAS,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YACjF,CAAC;QAED,QAAQ,GAAG,CAAC,AAAC,cAAyC,OAA5B,qBAAqB,MAAM,EAAC;QACtD,QAAQ,GAAG,CAAC,AAAC,cAAkC,OAArB,cAAc,MAAM,EAAC;QAC/C,QAAQ,GAAG,CAAC,AAAC,cAAoC,OAAvB,gBAAgB,MAAM,EAAC;QACjD,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,uBAAuB;IAClC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,IAAI,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,AAAC,+BAAwD,OAA1B,UAAU,MAAM,EAAC,WAAwB,OAAf,QAAQ,MAAM,EAAC;QACpF,OAAO;YAAE,SAAS;YAAM,MAAM;QAAU;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB,OAAO;IASrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAU,EAClB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB,OAAO,SAAiB;IACzD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,UAAU;QAAY,GAC/B,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,+DAA+D;YAC5E,2BAA2B;YAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,qBAAqB,OAAO;IAYvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,uCAAuC;YACpD,2BAA2B;YAC3B,MAAM,YAAY,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,yDAAyD;YACtE,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,6DAA6D;gBAC7D,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;gBAE5D,IAAI,gBAAgB,iBAAiB,YAAY;oBAC/C,QAAQ,GAAG,CAAC,AAAC,iCAA6C,OAAb;oBAC7C,OAAO;wBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;wBAC/D,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,YAAY;4BACZ,aAAa;4BACb,WAAW;gCACT,MAAM;gCACN,UAAU;gCACV,cAAc;gCACd,UAAU;gCACV,MAAM;4BACR;wBACF;wBACA,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAE;oBACb,gDAAgD;oBAChD,OAAO;gBACT;YACF;YAEA,+CAA+C;YAC/C,MAAM,0BAA0B,cAAc,MAAM,CAAC,CAAA,OACnD,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK;YAGhD,IAAI,aAAa;YACjB,IAAI,wBAAwB,MAAM,GAAG,GAAG;gBACtC,QAAQ,GAAG,CAAC,AAAC,YAA0C,OAA/B,wBAAwB,MAAM,EAAC;gBACvD,MAAM,iBAAiB,MAAM,8BAA8B;gBAE3D,wCAAwC;gBACxC,aAAa,cAAc,GAAG,CAAC,CAAA;oBAC7B,IAAI,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,YAAY;wBAC5D,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,KAAK,iBAAiB;wBACxF,OAAO,YAAY;oBACrB;oBACA,OAAO;gBACT;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAEhE,OAAO;oBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBAC/D,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY;wBACZ,aAAa;wBACb,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;oBACA,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAc;QAC9C,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEA,uDAAuD;AACvD,MAAM,gCAAgC,OAAO;IAC3C,IAAI;QACF,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,qEAAqE;YACrE,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;YAE1D,IAAI,CAAC,gBAAgB,iBAAiB,YAAY;gBAChD,gCAAgC;gBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;gBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;gBACvE,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;YACnC;YAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;YAEvE,OAAO;gBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC/D,GAAG,IAAI;gBACP,eAAe;gBACf,cAAc;gBACd,kBAAkB;oBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;oBACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;oBACnC,WAAW;wBACT,MAAM;wBACN,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;wBAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;wBACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;wBAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;oBAC1B;gBACF;gBACA,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,yDAAyD;QACzD,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC/D,GAAG,IAAI;gBACP,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC,CAAC;IACH;AACF;AAGO,MAAM,8BAA8B;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,QAAQ,GAAG,CAAC,AAAC,0BAA2C,OAAlB,WAAW,MAAM;QACvD,QAAQ,GAAG,CAAC,AAAC,mBAAmC,OAAjB,UAAU,MAAM;QAC/C,QAAQ,GAAG,CAAC,AAAC,mBAAiC,OAAf,QAAQ,MAAM;QAE7C,IAAI,aAAa;QACjB,IAAI,gBAAgB;QACpB,IAAI,sBAAsB;QAE1B,MAAM,aAAa,WAAW,GAAG,CAAC,CAAC;gBAE7B,kCAAA;YADJ,qDAAqD;YACrD,IAAI,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY;gBACjG,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAI,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACpE,IAAI,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;YAErE,oDAAoD;YACpD,IAAI,CAAC,SAAS,KAAK,iBAAiB,EAAE;gBACpC,QAAQ,GAAG,CAAC,AAAC,gCAAsD,OAAvB,KAAK,iBAAiB;gBAElE,oDAAoD;gBACpD,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,YAAY;oBAC3D,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK,KAAK,aAAa;gBACrE;gBAEA,uEAAuE;gBACvE,IAAI,CAAC,YAAY,UAAU,MAAM,GAAG,GAAG;oBACrC,WAAW,SAAS,CAAC,EAAE;oBACvB,QAAQ,GAAG,CAAC,AAAC,4BAAyC,OAAd,SAAS,IAAI;gBACvD;gBAEA,6CAA6C;gBAC7C,IAAI,UAAU;oBACZ,QAAQ;wBACN,IAAI,KAAK,iBAAiB;wBAC1B,aAAa,SAAS,EAAE;wBACxB,YAAY,KAAK,UAAU,IAAI,AAAC,SAAmB,OAAX,KAAK,GAAG;wBAChD,aAAa,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC7G,UAAU,KAAK,QAAQ,IAAI;wBAC3B,YAAY,KAAK,UAAU,IAAI,SAAS,UAAU,IAAI;wBACtD,eAAe,KAAK,UAAU,IAAI,SAAS,aAAa,IAAI;wBAC5D,aAAa;wBACb,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrD,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEA,QAAQ,IAAI,CAAC;oBACb;oBACA,QAAQ,GAAG,CAAC,AAAC,0BAAqD,OAA5B,MAAM,UAAU,EAAC,aAAyB,OAAd,SAAS,IAAI;gBACjF;YACF;YAEA,IAAI,qBAAA,+BAAA,SAAU,IAAI,EAAE;gBAClB;gBACA,QAAQ,GAAG,CAAC,AAzoBpB,AAyoBqB,0BAAkB,SAAS,IAAI,EAAC,aAA6B,OAAlB,kBAAA,4BAAA,MAAO,UAAU,EAAC;gBAE1E,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe,SAAS,IAAI;oBAC5B,cAAc,SAAS,IAAI;oBAC3B,kBAAkB;wBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;wBACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM,SAAS,IAAI;4BACnB,UAAU,SAAS,QAAQ,IAAI;4BAC/B,cAAc,SAAS,YAAY,IAAI;4BACvC,UAAU,SAAS,QAAQ,IAAI;4BAC/B,MAAM,SAAS,IAAI,IAAI;wBACzB;oBACF;gBACF;YACF,OAAO;gBACL;gBACA,QAAQ,GAAG,CAAC,AAAC,uCAA6D,OAAvB,KAAK,iBAAiB;gBAEzE,oCAAoC;gBACpC,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAChE,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;wBACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;QACF;QAEA,uCAAuC;QACvC,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAE3D,2CAA2C;QAC3C,IAAI,sBAAsB,GAAG;YAC3B,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC,AAAC,aAAgC,OAApB,qBAAoB;QAC/C;QAEA,QAAQ,GAAG,CAAC,AAAC,cAAuC,OAA1B,YAAW,iBAAiC,OAAlB,WAAW,MAAM;QACrE,QAAQ,GAAG,CAAC,AAAC,wBAAqC,OAAd,eAAc;QAElD,OAAO;YACL,SAAS;YACT;YACA;YACA;YACA,YAAY,WAAW,MAAM;QAC/B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,iCAAiC;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,2CAA2C;QAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QACvE,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QAEtE,QAAQ,GAAG,CAAE;QACb,QAAQ,GAAG,CAAC,AAAC,sBAAuC,OAAlB,WAAW,MAAM;QACnD,QAAQ,GAAG,CAAC,AAAC,eAA+B,OAAjB,UAAU,MAAM;QAC3C,QAAQ,GAAG,CAAC,AAAC,eAA6B,OAAf,QAAQ,MAAM;QACzC,QAAQ,GAAG,CAAC,AAAC,gBAA+B,OAAhB,SAAS,MAAM;QAE3C,iCAAiC;QACjC,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,MAAW,OAAe,OAC/D,UAAU,KAAK,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,EAAE;QAGvD,+CAA+C;QAC/C,MAAM,kBAAkB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,MAAa,IAAI,EAAE;QACjE,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAC,OACjD,gBAAgB,GAAG,CAAC,KAAK,UAAU;QAGrC,wCAAwC;QACxC,MAAM,SAAS;QAEf,gEAAgE;QAChE,MAAM,mBAAmB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC,MAAa,IAAI,EAAE;QACnE,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,QACnC,iBAAiB,GAAG,CAAC,MAAM,WAAW;QAGxC,oBAAoB;QACpB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC3D,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAExD,MAAM,eAAe,WAAW,MAAM,GAAG,uBAAuB,MAAM;QACtE,MAAM,iBAAiB,QAAQ,MAAM,GAAG,aAAa,MAAM;QAE3D,QAAQ,GAAG,CAAE;QACb,QAAQ,GAAG,CAAC,AAAC,YAAwB,OAAb,cAAa;QACrC,QAAQ,GAAG,CAAC,AAAC,YAA0B,OAAf,gBAAe;QACvC,QAAQ,GAAG,CAAC,AAAC,oBAAiD,OAA9B,uBAAuB,MAAM,EAAC;QAE9D,OAAO;YACL,SAAS;YACT;YACA;YACA,gBAAgB,uBAAuB,MAAM;YAC7C,WAAW;QACb;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,gCAAgC;IAC3C,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YAEZ,MAAM,kBAAkB;gBACtB;oBACE,IAAI,AAAC,YAAsB,OAAX,KAAK,GAAG,IAAG;oBAC3B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA;oBACE,IAAI,AAAC,YAAsB,OAAX,KAAK,GAAG,IAAG;oBAC3B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA;oBACE,IAAI,AAAC,YAAsB,OAAX,KAAK,GAAG,IAAG;oBAC3B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;aACD;YAED,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,QAAQ,GAAG,CAAC,AAAC,cAAoC,OAAvB,gBAAgB,MAAM,EAAC;YAEjD,4CAA4C;YAC5C,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;oBAC9D,IAAI,AAAC,SAAsB,OAAd,KAAK,GAAG,IAAG,KAAa,OAAV,QAAQ;oBACnC,aAAa,SAAS,EAAE;oBACxB,YAAY,AAAC,QAA0C,OAAnC,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAClD,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,MAAM,QAAQ,EAAE,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACxG,UAAU,MAAM,QAAQ;oBACxB,YAAY,SAAS,UAAU;oBAC/B,eAAe,SAAS,aAAa;oBACrC,aAAa;oBACb,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrD,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC,AAAC,cAAkC,OAArB,cAAc,MAAM,EAAC;YAE/C,OAAO;gBACL,SAAS;gBACT,kBAAkB,gBAAgB,MAAM;gBACxC,gBAAgB,cAAc,MAAM;YACtC;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,kBAAkB;YAAG,gBAAgB;QAAE;IAEjE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QAChD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;QAEvE,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAGO,MAAM,wBAAwB,OAAO;IAW1C,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4DAA4D;YACzE,2BAA2B;YAC3B,MAAM,YAAY,AAAC,oBAAiC,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,AAAC,oBAAiC,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAU5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,kEAAkE;YAC/E,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,AAAC,iBAA8B,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBACxE,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QAEtD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,AAAC,iBAA8B,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBACxE,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,oDAAoD;YAClE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,uBAAuB,OAAO;IAQzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,8DAA8D;YAC3E,2BAA2B;YAC3B,MAAM,WAAW;gBACf,IAAI,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBACnE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,WAAW;gBACf,IAAI,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBACnE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,wDAAwD;YACtE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAOhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAQhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,OACtC,aACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,eAAe;QAE3B,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,mBAAmB;QAC/C,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAEgB;YAD1C,QAAQ,KAAK,CAAC,4BAA4B,cAAc,KAAK;YAC7D,MAAM,IAAI,MAAM,AAAC,0BAAyE,OAAhD,EAAA,uBAAA,cAAc,KAAK,cAAnB,2CAAA,qBAAqB,OAAO,KAAI;QAC5E;QAEA,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QACvC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,oBAAoB;QACpB,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,EAAE;QAErB,KAAK,MAAM,QAAQ,MAAO;YACxB,QAAQ,GAAG,CAAC,qBAAqB;YACjC,MAAM,UAAU,KAAK,iBAAiB,IAAI,KAAK,OAAO;YAEtD,mDAAmD;YACnD,WAAW,IAAI,CAAC;gBACd,YAAY;gBACZ,mBAAmB;gBACnB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS;gBAC7C,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU;gBAChD,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI;gBACxC,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;YAC5D;YAEA,kDAAkD;YAClD,IAAI,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;gBAClC,IAAI;oBACF,MAAM,eAAe,MAAM,yHAAA,CAAA,WAAQ,CAChC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SACT,MAAM;oBAET,IAAI,aAAa,IAAI,EAAE;wBACrB,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,aAAa,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;wBAC1E,MAAM,oBAAoB,SAAS;wBACnC,QAAQ,GAAG,CAAC,AAAC,0BAAwC,OAAf,SAAQ,SAAmB,OAAZ;oBACvD;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,oCAAoC;gBACnD;YACF;YAEA,yBAAyB;YACzB,IAAI;gBACF,MAAM,qBAAqB;oBACzB,mBAAmB;oBACnB,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;oBACd,OAAO,AAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAI,SAAS;gBAClD;gBACA,QAAQ,GAAG,CAAC,AAAC,kCAAyC,OAAR;YAChD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,qCAAqC;YACpD;QACF;QAEA,iCAAiC;QACjC,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,qBAAqB;QAC/C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,QAAQ,IAAI,CAAC,uCAAuC,YAAY,KAAK;QACvE,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,IAAI;gBACF,MAAM,mBAAmB;oBACvB,kBAAkB;oBAClB,UAAU;oBACV,QAAQ,YAAY,YAAY;oBAChC,aAAa,AAAC,qBAA+C,OAA3B,YAAY,cAAc;oBAC5D,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,OAAO,YAAY,KAAK;gBAC1B;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,WAAW;gBAClB,QAAQ,IAAI,CAAC,uCAAuC;YACtD;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,+RAYR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,4EAA4E;YAC5E,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;wBAC3B,kCAAA;oBAAJ,KAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,EAAE;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBAEvE,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;4BACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;4BACnC,WAAW;gCACT,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;gCAC9C,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,8DAA8D;YAC9D,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;wBAC3B,kCAAA;oBAAJ,KAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,EAAE;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBAEvE,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;4BACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;4BACnC,WAAW;gCACT,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;gCAC9C,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,+RAYR,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oDAAoD;gBACpD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAC3C,QAAQ,GAAG,CAAC,2BAA2B,UAAU,MAAM;gBACvD,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,MAAM;gBAErD,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,AAAC,gBAAyB,OAAV,QAAQ,GAAE;oBACtC,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,qDAAqD;oBACrD,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBACvE,QAAQ,GAAG,CAAC,mBAAmB;oBAE/B,uCAAuC;oBACvC,MAAM,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;oBACvC,QAAQ,GAAG,CAAC,uBAAuB;oBAEnC,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,EAAE,EAAE,kBAAA,4BAAA,MAAO,EAAE;4BACb,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;4BACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;4BACvD,WAAW,EAAE,kBAAA,4BAAA,MAAO,WAAW;4BAC/B,WAAW;gCACT,EAAE,EAAE,qBAAA,+BAAA,SAAU,EAAE;gCAChB,MAAM;gCACN,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,qBAAqB;gBAEjC,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,qBAAqB;oBACvB;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,kNAQR,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4EAA4E;YACzF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAE3C,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,AAAC,gBAAyB,OAAV,QAAQ,GAAE;oBACtC,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,0CAA0C;oBAC1C,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;oBAChE,QAAQ,GAAG,CAAC,eAAe;oBAE3B,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,WAAW;4BACT,MAAM;4BACN,UAAU,KAAK,QAAQ,IAAI;4BAC3B,cAAc,KAAK,YAAY,IAAI;4BACnC,UAAU,KAAK,QAAQ,IAAI;4BAC3B,MAAM,KAAK,IAAI,IAAI;wBACrB;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,wBAAwB;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,kNAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,sEAAsE;YACnF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IAUtC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC5E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,QAAQ,GAAG,CAAC,uCAAuC;YACnD,QAAQ,GAAG,CAAC,wCAAwC,gBAAgB,MAAM;YAE1E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iEAAiE;QAE9E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,QAAQ,GAAG,CAAC,0CAA0C;YACtD,QAAQ,GAAG,CAAC,2CAA2C,gBAAgB,MAAM;YAE7E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO;IASnC,IAAI;YAEgB;QADlB,qBAAqB;QACrB,MAAM,YAAY,EAAA,UAAA,KAAK,CAAC,EAAE,cAAR,8BAAA,QAAU,WAAW,MAAK,UAAU,uBAAuB;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzB,WAAW,KAAK,SAAS;gBACzB,mBAAmB,KAAK,iBAAiB;gBACzC,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B,CAAC,IACA,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;gBACiB;YAAnB,MAAM,aAAa,EAAA,WAAA,KAAK,CAAC,EAAE,cAAR,+BAAA,SAAU,WAAW,MAAK,UAAU,uBAAuB;YAC9E,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtC,GAAG,IAAI;oBACP,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBAC7D,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YACrE,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,gBAAgB,OAC3B,YACA,YACA;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,eAAe,eAAe,UAChC,MAAM,kBAAkB,cACxB,MAAM,qBAAqB;QAE/B,IAAI,CAAC,aAAa,OAAO,EAAE,MAAM,IAAI,MAAM;QAE3C,MAAM,WAAW,aAAa,IAAI,CAAC,EAAE;QAErC,mBAAmB;QACnB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACrC,WAAW;gBACX,aAAa;gBACb,mBAAmB,KAAK,OAAO;gBAC/B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;YAC9B,CAAC;QAED,MAAM,eAAe;QAErB,8DAA8D;QAC9D,IAAI;YACF,yDAAyD;YACzD,IAAI,eAAe,SAAS;gBAC1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,oBAAoB,KAAK,OAAO,EAAE,MAAM,QAAQ,GAAG,KAAK,QAAQ;4BACxE;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,AAAC,kBAAmC,OAAlB,WAAW,MAAM;4BAC5C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;YAEA,4DAA4D;YAC5D,IAAI,eAAe,YAAY;gBAC7B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,GAAG,KAAK,QAAQ;gCAC9D,MAAM,oBAAoB,KAAK,OAAO,EAAE;4BAC1C;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,AAAC,mBAAoC,OAAlB,WAAW,MAAM;4BAC7C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,IAAI,CAAC,iEAAiE;QAChF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAS;QAAE;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,aAAa;IACxB,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,QAAQ,GAAG,CAAC,sCAAsC;YAChD,cAAc,aAAa,MAAM;YACjC,iBAAiB,gBAAgB,MAAM;YACvC,WAAW,UAAU,MAAM;YAC3B,WAAW,UAAU,MAAM;QAC7B;QAEA,0CAA0C;QAC1C,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAC;gBAI9B;YAHf,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,AAAC,0BAAuC,OAAd,WAAW,EAAE,EAAC,MAAI;gBACtD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,EAAA,2BAAA,WAAW,YAAY,cAAvB,+CAAA,yBAAyB,MAAM,KAAI;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,6CAA6C;QAC7C,MAAM,0BAA0B,gBAAgB,GAAG,CAAC,CAAC;gBAIpC;YAHf,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,AAAC,6BAA0C,OAAd,WAAW,EAAE,EAAC,MAAI;gBACzD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,EAAA,2BAAA,WAAW,YAAY,cAAvB,+CAAA,yBAAyB,MAAM,KAAI;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,+CAA+C;QAC/C,IAAI,qBAAqB,MAAM,GAAG,KAAK,wBAAwB,MAAM,GAAG,GAAG;YACzE,MAAM,aAAa;mBACd;mBACA;aACJ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAElF,QAAQ,GAAG,CAAC,iDAAiD,WAAW,KAAK,CAAC,GAAG;YACjF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,oCAAoC;IACnD;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACxD,yHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,iBACL,MAAM,CAAE,uOAWR,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,yHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,oBACL,MAAM,CAAE,uKAQR,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;SAC3C;QAED,MAAM,aAAa;eACd,CAAC,aAAa,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBAGhB;uBAHyB;oBACxC,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBACzC;;eACG,CAAC,gBAAgB,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBAGnB;uBAH4B;oBAC3C,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBACzC;;SACD,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAElF,QAAQ,GAAG,CAAC,oCAAoC,WAAW,KAAK,CAAC,GAAG;QACpE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAW;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mDAAmD;QAEhE,4DAA4D;QAC5D,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,yBAAyB;QACzB,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACzD,IAAI,aAAa;QAEjB,IAAI,CAAC,aAAa;YAChB,cAAc,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;YACxD,aAAa;QACf;QAEA,IAAI,aAAa;YACf,qCAAqC;YACrC,IAAI,eAAe,SAAS;gBAC1B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,YAAY,aAAa,IAAI;gBAChE;YACF,OAAO;gBACL,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,YAAY,aAAa,IAAI;gBAChE;YACF;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YACtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;QAEA,uCAAuC;QACvC,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmB;QACrD;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAE,mNAWR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,aAAa;gBACb,cAAc,YAAY,kBAAkB,IAAI,EAAE;YACpD;YACA,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAE,yJAQR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,aAAa;gBACb,cAAc,eAAe,qBAAqB,IAAI,EAAE;YAC1D;YACA,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,IAAI,CAAC,qBAAqB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,8BAA8B,OACzC,aACA;IAEA,IAAI;QACF,0BAA0B;QAC1B,MAAM,gBAAgB,MAAM,sBAAsB;QAClD,IAAI,CAAC,cAAc,OAAO,EAAE,MAAM,IAAI,MAAM;QAE5C,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QAEvC,oBAAoB;QACpB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,aAAa,KAAK,UAAU;YAEhC,uCAAuC;YACvC,IAAI,CAAC,YAAY;gBACf,MAAM,oBAAoB,MAAM,YAAY;oBAC1C,MAAM,KAAK,YAAY;oBACvB,UAAU,KAAK,QAAQ,IAAI;oBAC3B,cAAc,KAAK,YAAY,IAAI;oBACnC,mBAAmB,KAAK,gBAAgB,IAAI;oBAC5C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,QAAQ;oBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACtD;gBAEA,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,aAAa,kBAAkB,IAAI,CAAC,EAAE;gBACxC,OAAO;oBACL,QAAQ,KAAK,CAAC,8BAA8B,kBAAkB,KAAK;oBACnE;gBACF;YACF;YAEA,4BAA4B;YAC5B,MAAM,wBAAwB;gBAAC;oBAC7B,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,SAAS;oBAC1B,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,QAAQ;oBACxB,YAAY,KAAK,SAAS;oBAC1B,aAAa,KAAK,UAAU;oBAC5B,eAAe,KAAK,YAAY,IAAI;gBACtC;aAAE;YAEF,kCAAkC;YAClC,MAAM,cAAc,MAAM,iBAAiB;gBACzC,aAAa;gBACb,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,QAAQ;gBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACpD,aAAa,YAAY,WAAW;YACtC;YAEA,IAAI,YAAY,OAAO,EAAE;gBACvB,yBAAyB;gBACzB,MAAM,qBAAqB;oBACzB,mBAAmB,YAAY,IAAI,CAAC,EAAE;oBACtC,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;gBAChB;YACF;QACF;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB,UAAU;gBACV,QAAQ,YAAY,YAAY;gBAChC,aAAa,AAAC,sBAAgD,OAA3B,YAAY,cAAc;gBAC7D,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,OAAO,YAAY,KAAK;YAC1B;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO;IAUvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAgB,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kEAAkE;QAE/E,2BAA2B;QAC3B,IAAI;YACF,MAAM,oBAAoB;gBACxB,GAAG,eAAe;gBAClB,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,uBAAuB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACrF,qBAAqB,IAAI,CAAC;YAC1B,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,QAAQ,GAAG,CAAC,2CAA2C;YACvD,QAAQ,GAAG,CAAC,4CAA4C,qBAAqB,MAAM;YAEnF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,sBAAsB,OAAO;IAMxC,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,8CAA8C;QAC9C,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,gDAAgD,aAAa,MAAM;YAE/E,IAAI,uBAAuB;YAE3B,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;gBACvB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,UAAU;YAC5F;YACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,QAAQ;YAC1F;YACA,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;gBAC7B,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,QAAQ,gBAAgB;YACzG;YACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;YACzF;YAEA,gCAAgC;YAChC,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAErG,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAqB;QACrD;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,sDAAsD;IACrE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;YACvB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;YAC7B,QAAQ,MAAM,EAAE,CAAC,oBAAoB,QAAQ,gBAAgB;QAC/D;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;QAC/C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6DAA6D;QAC1E,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,iBAAiB;IAC5B,yBAAyB;IACzB,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,+CAA+C,aAAa,MAAM,EAAE;YAEhF,MAAM,UAAU,aAAa,MAAM,CAAC,CAAC,OAAe;gBAClD,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;YAChC,GAAG;YAEH,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,gDAAgD;IAC/D;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QAEjB,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,OAAO;YAClC,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;QAChC,GAAG;QAEH,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAE;IAClC;AACF;AAGO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QAE3E,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CAAC,6CAA6C,cAAc,MAAM,EAAE;YAE/E,mCAAmC;YACnC,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,UAC5C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAiB,OAAO;oBAAc;gBAC3D;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAkB,OAAO;oBAAc;gBAC5D;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,sMAUR,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAEjF,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,QAAQ,GAAG,CAAC,6CAA6C,iBAAiB,MAAM,EAAE;YAElF,mCAAmC;YACnC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,UAC/C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAyB,gBAAgB;wBAAa,OAAO;oBAAc;gBAChG;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAsB,gBAAgB;wBAAa,OAAO;oBAAc;gBAC7F;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,8LASR,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,sBAAsB,OACjC,aACA,WACA,eACA;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,YAAY,gBAAgB,UAAU,mBAAmB;QAE/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;YACN,gBAAgB;YAChB,GAAI,cAAc;gBAAE,aAAa;YAAW,CAAC;QAC/C,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,+CAA+C;QAC/C,IAAI,kBAAkB,UAAU,YAAY;YAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;YAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;YAEtD,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB;gBACA,QAAQ;gBACR,aAAa,AAAC,cAAmE,OAAtD,gBAAgB,UAAU,WAAW,WAAU,SAA2B,OAApB,KAAK,cAAc;gBACpG,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;YAClB;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gEAAgE;QAE7E,2BAA2B;QAC3B,IAAI;YACF,MAAM,aAAa,gBAAgB,UAAU,mBAAmB;YAChE,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YAEhE,MAAM,eAAe,SAAS,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YACjE,IAAI,iBAAiB,CAAC,GAAG;gBACvB,QAAQ,CAAC,aAAa,CAAC,cAAc,GAAG;gBACxC,IAAI,YAAY;oBACd,QAAQ,CAAC,aAAa,CAAC,WAAW,GAAG;gBACvC;gBAEA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,+CAA+C;gBAC/C,IAAI,kBAAkB,UAAU,YAAY;oBAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;oBAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;oBAEtD,MAAM,mBAAmB;wBACvB,kBAAkB;wBAClB;wBACA,QAAQ;wBACR,aAAa,AAAC,cAAmE,OAAtD,gBAAgB,UAAU,WAAW,WAAU,SAA6C,OAAtC,QAAQ,CAAC,aAAa,CAAC,cAAc;wBACtH,gBAAgB;wBAChB,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,QAAQ,GAAG,CAAC,2CAA2C,QAAQ,CAAC,aAAa;gBAC7E,OAAO;oBAAE,SAAS;oBAAM,MAAM,QAAQ,CAAC,aAAa;gBAAC;YACvD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IAMnC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,kBACL,MAAM,CAAE,8NAWR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBAChC,kCAAA;2BAAA,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,EAAE,MAAK,QAAQ,WAAW;;QAGlE;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAE,2KAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,sBAAsB,CAAC,IAAI,CAAC,CAAC;wBACnC;2BAAA,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,EAAE,MAAK,QAAQ,WAAW;;QAGhD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,aAAa,yHAAA,CAAA,WAAQ,CACtB,IAAI,CAAC,kBACL,MAAM,CAAE,gNAWR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,yHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,iBACL,MAAM,CAAE,8HAQR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,UAAU;YAC5D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAC1D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,aAAa,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrD;YACA;SACD;QAED,IAAI,YAAY,KAAK,EAAE,MAAM,YAAY,KAAK;QAC9C,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,OAAO,YAAY,IAAI;gBACvB,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,iBAAiB,yHAAA,CAAA,WAAQ,CAC1B,IAAI,CAAC,qBACL,MAAM,CAAE,gNAWR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,yHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,oBACL,MAAM,CAAE,8HAQR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,UAAU;YACpE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAClE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,iBAAiB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACzD;YACA;SACD;QAED,IAAI,gBAAgB,KAAK,EAAE,MAAM,gBAAgB,KAAK;QACtD,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,WAAW,gBAAgB,IAAI;gBAC/B,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,4BAA4B,OAAO,YAAoB;IAIlE,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,uBACL,MAAM,CAAE,oJAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,qBAAqB;QACrB,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;gBAC/B,sCAAA;mBAAA,EAAA,6BAAA,SAAS,gBAAgB,cAAzB,kDAAA,uCAAA,2BAA2B,SAAS,cAApC,2DAAA,qCAAsC,EAAE,MAAK;;QAG/C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAE,0EAIR,KAAK,CAAC,eAAe;YAAE,WAAW;QAAK;QAE1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,IAAI,eAAe;QAEnB,qBAAqB;QACrB,IAAI,QAAQ,QAAQ,EAAE;YACpB,eAAe,aAAa,MAAM,CAAC,CAAA;oBACjC;uBAAA,EAAA,mBAAA,MAAM,SAAS,cAAf,uCAAA,iBAAiB,QAAQ,MAAK,QAAQ,QAAQ;;QAElD;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,SAAS,EAAE;YACrB,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,GAAG;QAC/D;QAEA,oBAAoB;QACpB,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG;QAClE;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,aAAa,EAAE;YACzB,MAAM,oBAAoB,IAAI;YAC9B,kBAAkB,OAAO,CAAC,kBAAkB,OAAO,KAAK;YACxD,MAAM,aAAa,kBAAkB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChE,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,eAAe,aAAa,MAAM,CAAC,CAAA,QACjC,MAAM,WAAW,IAAI,SAAS,MAAM,WAAW,IAAI;QAEvD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO,UAAkB,QAAgB;IACzE,MAAM,UAAe;QAAE;IAAO;IAC9B,IAAI,iBAAiB;QACnB,QAAQ,gBAAgB,GAAG;IAC7B;IACA,OAAO,aAAa,UAAU;AAChC;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,2BAA2B;QAC3B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,yBAAyB;QAClF,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,4BAA4B;QAExF,wBAAwB;QACxB,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC7D,IAAI,aAAa;YACf,MAAM,QAAQ,iBAAiB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAExE,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAE/E,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAC9D,IAAI,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACrD,IAAI,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBAExD,uDAAuD;gBACvD,IAAI,iBAAiB,cAAc,KAAK,iBAAiB,EAAE;oBACzD,MAAM,QAAQ,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBAC9E,IAAI,OAAO;wBACT,YAAY,MAAM,YAAY,IAAI;wBAClC,aAAa,MAAM,WAAW,IAAI;wBAElC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,MAAM,WAAW;wBACtE,IAAI,UAAU;4BACZ,eAAe,SAAS,IAAI,IAAI;wBAClC;oBACF;gBACF;gBAEA,2DAA2D;gBAC3D,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBACtD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,2BAA2B;QAC3B,cAAc,gBAAgB,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC5D,IAAI,aAAa;YACf,MAAM,QAAQ,oBAAoB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAE3E,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAElE,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAE9D,qDAAqD;gBACrD,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;oBACpD,WAAW,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI;oBAC9C,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACnD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAE,8RAYR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,YAAY,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;wBAEjD,kCAAA;2BAFgE;wBAC/E,GAAG,IAAI;wBACP,eAAe,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,aAAa,IAAI;wBAC/E,YAAY,KAAK,UAAU,IAAI;wBAC/B,aAAa,KAAK,WAAW,IAAI;oBACnC;;YACF;YACA,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAE,iNAQR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,eAAe,qBAAqB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;wBAEvD;2BAFsE;wBACrF,GAAG,IAAI;wBACP,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI;wBAC7D,WAAW,KAAK,SAAS,IAAI;wBAC7B,YAAY,KAAK,UAAU,IAAI;oBACjC;;YACF;YACA,QAAQ,GAAG,CAAC,8CAA8C;YAC1D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAE/E,mCAAmC;QACnC,MAAM,aAAa,aAAa,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACnE,IAAI,eAAe,CAAC,GAAG;YACrB,YAAY,CAAC,WAAW,GAAG;gBAAE,GAAG,YAAY,CAAC,WAAW;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YAC3G,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,YAAY,CAAC,WAAW;YAAC;QACzD;QAEA,sCAAsC;QACtC,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACzE,IAAI,kBAAkB,CAAC,GAAG;YACxB,eAAe,CAAC,cAAc,GAAG;gBAAE,GAAG,eAAe,CAAC,cAAc;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YACvH,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,eAAe,CAAC,cAAc;YAAC;QAC/D;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 5524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport AppLayout from '@/components/AppLayout'\nimport {\n  BarChart3,\n  FileText,\n  Calendar,\n  Users,\n  Package,\n  TrendingUp,\n  DollarSign,\n  Activity,\n  Eye,\n  RefreshCw,\n  Filter,\n  Settings,\n  X,\n  Printer,\n  Download,\n  FileSpreadsheet,\n  Plus,\n  Minus,\n  ChevronDown,\n  ChevronRight,\n  Receipt,\n  User,\n  Building,\n  Package2,\n  Info,\n  Zap,\n  Database,\n  CheckCircle,\n  Clock,\n  Target,\n  PieChart,\n  ExternalLink,\n  Edit,\n  Phone,\n  Mail,\n  MapPin,\n  CreditCard\n} from 'lucide-react'\n\nimport {\n  getSalesInvoices,\n  getPurchaseInvoices,\n  getCashTransactions,\n  getReturns,\n  getCustomers,\n  getSuppliers,\n  getMedicines\n} from '@/lib/database'\n\n// تعريف أنواع التقارير\ntype ReportType = \n  | 'sales_summary'\n  | 'purchases_summary'\n  | 'financial_summary'\n  | 'inventory_report'\n  | 'customer_statement'\n  | 'supplier_statement'\n  | 'profit_loss'\n  | 'cash_flow'\n  | 'top_products'\n  | 'customer_analysis'\n\n// تعريف فلاتر التقارير\ninterface ReportFilters {\n  dateRange: {\n    start: string\n    end: string\n    preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'\n  }\n  customer?: string\n  supplier?: string\n  paymentStatus?: 'all' | 'paid' | 'partial' | 'pending'\n  paymentMethod?: 'all' | 'cash' | 'credit'\n  includeReturns?: boolean\n}\n\n// تعريف بيانات التقرير\ninterface ReportData {\n  title: string\n  summary: {\n    totalRecords: number\n    totalAmount: number\n    averageAmount: number\n    paidAmount?: number\n    pendingAmount?: number\n    totalRevenue?: number\n    totalExpenses?: number\n    netProfit?: number\n    profitMargin?: number\n    cashIncome?: number\n    cashExpenses?: number\n  }\n  data: any[]\n  charts?: {\n    type: 'bar' | 'pie' | 'line'\n    data: any[]\n    labels: string[]\n  }[]\n}\n\n// قائمة التقارير المتاحة\nconst AVAILABLE_REPORTS = [\n  {\n    id: 'sales_summary' as ReportType,\n    title: 'تقرير المبيعات الشامل',\n    description: 'تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية',\n    icon: BarChart3,\n    category: 'sales',\n    color: 'bg-blue-500'\n  },\n  {\n    id: 'purchases_summary' as ReportType,\n    title: 'تقرير المشتريات الشامل',\n    description: 'تقرير مفصل عن جميع عمليات المشتريات والموردين',\n    icon: Package,\n    category: 'purchases',\n    color: 'bg-green-500'\n  },\n  {\n    id: 'financial_summary' as ReportType,\n    title: 'التقرير المالي الشامل',\n    description: 'تحليل مالي شامل للإيرادات والمصروفات والأرباح',\n    icon: DollarSign,\n    category: 'financial',\n    color: 'bg-emerald-500'\n  },\n  {\n    id: 'inventory_report' as ReportType,\n    title: 'تقرير المخزون',\n    description: 'حالة المخزون الحالية والأدوية المتوفرة',\n    icon: Package2,\n    category: 'inventory',\n    color: 'bg-purple-500'\n  },\n  {\n    id: 'customer_statement' as ReportType,\n    title: 'كشف حساب العملاء',\n    description: 'تفاصيل حسابات العملاء والمبالغ المستحقة',\n    icon: Users,\n    category: 'customers',\n    color: 'bg-indigo-500'\n  },\n  {\n    id: 'supplier_statement' as ReportType,\n    title: 'كشف حساب الموردين',\n    description: 'تفاصيل حسابات الموردين والمبالغ المستحقة',\n    icon: Building,\n    category: 'suppliers',\n    color: 'bg-cyan-500'\n  },\n  {\n    id: 'profit_loss' as ReportType,\n    title: 'تقرير الأرباح والخسائر',\n    description: 'تحليل الأرباح والخسائر للفترة المحددة',\n    icon: TrendingUp,\n    category: 'financial',\n    color: 'bg-orange-500'\n  },\n  {\n    id: 'cash_flow' as ReportType,\n    title: 'تقرير التدفق النقدي',\n    description: 'تتبع التدفقات النقدية الداخلة والخارجة',\n    icon: Activity,\n    category: 'financial',\n    color: 'bg-teal-500'\n  },\n  {\n    id: 'top_products' as ReportType,\n    title: 'أفضل المنتجات مبيعاً',\n    description: 'تحليل أداء المنتجات والأدوية الأكثر مبيعاً',\n    icon: FileText,\n    category: 'analytics',\n    color: 'bg-pink-500'\n  },\n  {\n    id: 'customer_analysis' as ReportType,\n    title: 'تحليل سلوك العملاء',\n    description: 'تحليل أنماط شراء العملاء وسلوكهم',\n    icon: Users,\n    category: 'analytics',\n    color: 'bg-violet-500'\n  }\n]\n\nexport default function ReportsPage() {\n  const router = useRouter()\n  const [loading, setLoading] = useState(false)\n  const [showFilters, setShowFilters] = useState(false)\n  const [showReportModal, setShowReportModal] = useState(false)\n  const [selectedReport, setSelectedReport] = useState<ReportType | null>(null)\n  const [reportData, setReportData] = useState<ReportData | null>(null)\n  const [customers, setCustomers] = useState<any[]>([])\n  const [suppliers, setSuppliers] = useState<any[]>([])\n  const [medicines, setMedicines] = useState<any[]>([])\n  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())\n\n  // الفلاتر الافتراضية\n  const [filters, setFilters] = useState<ReportFilters>({\n    dateRange: {\n      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      end: new Date().toISOString().split('T')[0],\n      preset: 'month'\n    },\n    paymentStatus: 'all',\n    paymentMethod: 'all',\n    includeReturns: true\n  })\n\n  // تحميل البيانات الأساسية\n  useEffect(() => {\n    loadBasicData()\n  }, [])\n\n  const loadBasicData = async () => {\n    try {\n      const [customersResult, suppliersResult, medicinesResult] = await Promise.all([\n        getCustomers(),\n        getSuppliers(),\n        getMedicines()\n      ])\n      \n      setCustomers(customersResult.data || [])\n      setSuppliers(suppliersResult.data || [])\n      setMedicines(medicinesResult.data || [])\n    } catch (error) {\n      console.error('Error loading basic data:', error)\n    }\n  }\n\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 flex items-center gap-3\">\n                <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n                نظام التقارير الاحترافي\n              </h1>\n              <p className=\"text-gray-600 mt-1\">تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Reports Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {AVAILABLE_REPORTS.map((report) => {\n            const Icon = report.icon\n            return (\n              <div\n                key={report.id}\n                className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className={`p-3 rounded-lg ${report.color} text-white`}>\n                      <Icon className=\"h-6 w-6\" />\n                    </div>\n                    <button\n                      onClick={() => alert('قريباً...')}\n                      disabled={loading}\n                      className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1\"\n                    >\n                      <Eye className=\"h-4 w-4\" />\n                      عرض\n                    </button>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{report.title}</h3>\n                  <p className=\"text-sm text-gray-600\">{report.description}</p>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    </AppLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwCA;;;AA7CA;;;;;;AA0GA,yBAAyB;AACzB,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,YAAS;QACf,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,2MAAA,CAAA,UAAO;QACb,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,aAAU;QAChB,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,iNAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,aAAU;QAChB,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,iNAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;QACV,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAElE,qBAAqB;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,WAAW;YACT,OAAO,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAClF,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3C,QAAQ;QACV;QACA,eAAe;QACf,eAAe;QACf,gBAAgB;IAClB;IAEA,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,CAAC,iBAAiB,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5E,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;aACZ;YAED,aAAa,gBAAgB,IAAI,IAAI,EAAE;YACvC,aAAa,gBAAgB,IAAI,IAAI,EAAE;YACvC,aAAa,gBAAgB,IAAI,IAAI,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,qBACE,6LAAC,kIAAA,CAAA,UAAS;kBACR,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGjD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC;wBACtB,MAAM,OAAO,OAAO,IAAI;wBACxB,qBACE,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,kBAA8B,OAAb,OAAO,KAAK,EAAC;0DAC7C,cAAA,6LAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,MAAM;gDACrB,UAAU;gDACV,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;kDAI/B,6LAAC;wCAAG,WAAU;kDAA4C,OAAO,KAAK;;;;;;kDACtE,6LAAC;wCAAE,WAAU;kDAAyB,OAAO,WAAW;;;;;;;;;;;;2BAlBrD,OAAO,EAAE;;;;;oBAsBpB;;;;;;;;;;;;;;;;;AAKV;GA9FwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}