'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Monitor,
  Tablet,
  Smartphone,
  Eye,
  CheckCircle,
  AlertCircle,
  Laptop,
  Grid3X3,
  Layout,
  Type,
  MousePointer,
  Hand
} from 'lucide-react'

export default function TestResponsivePage() {
  const [currentView, setCurrentView] = useState('desktop')

  const viewports = [
    {
      id: 'mobile',
      name: 'هاتف محمول',
      icon: Smartphone,
      width: '375px',
      height: '667px',
      description: 'iPhone SE / صغير',
      breakpoint: 'max-width: 640px'
    },
    {
      id: 'mobile-large',
      name: 'هاتف كبير',
      icon: Smartphone,
      width: '414px',
      height: '896px',
      description: 'iPhone 11 Pro / كبير',
      breakpoint: 'max-width: 768px'
    },
    {
      id: 'tablet',
      name: 'تابلت',
      icon: Tablet,
      width: '768px',
      height: '1024px',
      description: 'iPad / تابلت',
      breakpoint: '768px - 1024px'
    },
    {
      id: 'laptop',
      name: 'لابتوب',
      icon: Laptop,
      width: '1024px',
      height: '768px',
      description: 'لابتوب صغير',
      breakpoint: '1024px - 1280px'
    },
    {
      id: 'desktop',
      name: 'كمبيوتر مكتبي',
      icon: Monitor,
      width: '1920px',
      height: '1080px',
      description: 'شاشة كبيرة',
      breakpoint: 'min-width: 1280px'
    }
  ]

  const testFeatures = [
    {
      name: 'التخطيط العام',
      icon: Layout,
      tests: [
        'الشريط الجانبي يظهر/يختفي حسب حجم الشاشة',
        'الهيدر يتكيف مع العرض المتاح',
        'المحتوى الرئيسي يستغل المساحة بكفاءة'
      ]
    },
    {
      name: 'الأزرار والتفاعل',
      icon: Hand,
      tests: [
        'الأزرار بحجم مناسب للمس (44px+)',
        'المساحات بين العناصر كافية',
        'التفاعل سهل ومريح'
      ]
    },
    {
      name: 'النصوص والأيقونات',
      icon: Type,
      tests: [
        'النصوص قابلة للقراءة على جميع الأحجام',
        'الأيقونات واضحة ومناسبة',
        'التباين جيد للرؤية'
      ]
    },
    {
      name: 'الشبكات والجداول',
      icon: Grid3X3,
      tests: [
        'الشبكات تتكيف مع حجم الشاشة',
        'الجداول قابلة للتمرير أفقياً',
        'البيانات منظمة ومقروءة'
      ]
    }
  ]

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Eye className="h-8 w-8" />
            <div>
              <h1 className="text-2xl md:text-3xl font-bold">اختبار التوافق المتجاوب</h1>
              <p className="text-blue-100 mt-1">اختبار النظام على أحجام شاشات مختلفة</p>
            </div>
          </div>
        </div>

        {/* Viewport Selector */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">اختر حجم الشاشة للاختبار</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {viewports.map((viewport) => {
              const Icon = viewport.icon
              return (
                <button
                  key={viewport.id}
                  onClick={() => setCurrentView(viewport.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    currentView === viewport.id
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <Icon className="h-8 w-8 mx-auto mb-2" />
                  <div className="text-sm font-medium">{viewport.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{viewport.description}</div>
                  <div className="text-xs text-gray-400 mt-1">{viewport.width} × {viewport.height}</div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Current Viewport Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الشاشة الحالية</h3>
          {(() => {
            const current = viewports.find(v => v.id === currentView)
            const Icon = current?.icon || Monitor
            return (
              <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <Icon className="h-12 w-12 text-blue-600" />
                <div>
                  <h4 className="font-medium text-gray-900">{current?.name}</h4>
                  <p className="text-sm text-gray-600">{current?.description}</p>
                  <p className="text-sm text-gray-500">الأبعاد: {current?.width} × {current?.height}</p>
                  <p className="text-sm text-gray-500">نقطة التوقف: {current?.breakpoint}</p>
                </div>
              </div>
            )
          })()}
        </div>

        {/* Test Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">قائمة فحص التوافق</h3>
          <div className="space-y-6">
            {testFeatures.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Icon className="h-5 w-5 text-blue-600" />
                    <h4 className="font-medium text-gray-900">{feature.name}</h4>
                  </div>
                  <div className="space-y-2">
                    {feature.tests.map((test, testIndex) => (
                      <div key={testIndex} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-700">{test}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-2">تعليمات الاختبار</h3>
              <ul className="text-yellow-700 space-y-1 text-sm">
                <li>• اضغط F12 لفتح أدوات المطور</li>
                <li>• اختر "Device Toolbar" أو اضغط Ctrl+Shift+M</li>
                <li>• اختر جهاز من القائمة أو أدخل أبعاد مخصصة</li>
                <li>• تنقل بين الصفحات لاختبار جميع الميزات</li>
                <li>• تأكد من سهولة الاستخدام على كل حجم شاشة</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">روابط سريعة للاختبار</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="/" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
              <div className="font-medium text-gray-900">الصفحة الرئيسية</div>
              <div className="text-sm text-gray-500">اختبار البطاقات والإحصائيات</div>
            </a>
            <a href="/sales" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
              <div className="font-medium text-gray-900">المبيعات</div>
              <div className="text-sm text-gray-500">اختبار النماذج والجداول</div>
            </a>
            <a href="/inventory" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
              <div className="font-medium text-gray-900">المخزون</div>
              <div className="text-sm text-gray-500">اختبار قوائم البيانات</div>
            </a>
            <a href="/fix-data" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
              <div className="font-medium text-gray-900">إصلاح البيانات</div>
              <div className="text-sm text-gray-500">اختبار الأزرار والرسائل</div>
            </a>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
