/* [project]/src/styles/classic-print.css [app-client] (css) */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  body {
    color: #000;
    direction: rtl;
    background: #fff;
    margin: 0;
    padding: 0;
    font-family: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
    font-size: 12px;
    line-height: 1.3;
  }

  .print-content {
    max-width: none;
    box-shadow: none;
    page-break-inside: avoid;
    background: #fff;
    border: none;
    margin: 0;
    padding: 8mm;
  }

  .no-print, button, .btn, .print-button, .fixed, .modal, .dropdown {
    display: none !important;
  }

  .page-break {
    page-break-before: always;
    break-before: page;
  }

  .avoid-break {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .invoice-header {
    page-break-inside: avoid;
    border: 3px solid #000;
    margin-bottom: 8px;
  }

  .header-row {
    table-layout: fixed;
    border-collapse: collapse;
    width: 100%;
    display: table;
  }

  .header-cell {
    vertical-align: middle;
    text-align: center;
    border-right: 2px solid #000;
    height: 80px;
    padding: 8px;
    display: table-cell;
  }

  .header-cell:last-child {
    border-right: none;
  }

  .header-cell:first-child {
    text-align: center;
    vertical-align: middle;
  }

  .logo-container {
    background: #f8f8f8;
    border: 2px solid #000;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
  }

  .invoice-table {
    border-collapse: collapse;
    page-break-inside: avoid;
    border: 2px solid #000;
    width: 100%;
    margin: 8px 0;
  }

  .invoice-table th, .invoice-table td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid #000;
    padding: 6px 4px;
    font-size: 11px;
    line-height: 1.2;
  }

  .invoice-table th {
    background-color: #f0f0f0;
    height: 30px;
    font-weight: bold;
  }

  .invoice-table td {
    height: 28px;
  }

  .col-serial {
    width: 6%;
    min-width: 30px;
  }

  .col-medicine {
    width: 40%;
    min-width: 120px;
  }

  .col-quantity {
    width: 10%;
    min-width: 50px;
  }

  .col-price, .col-total {
    width: 15%;
    min-width: 70px;
  }

  .col-batch {
    width: 8%;
    min-width: 50px;
  }

  .col-expiry {
    width: 6%;
    min-width: 40px;
  }

  .medicine-cell {
    text-align: right !important;
    vertical-align: top !important;
    padding-right: 8px !important;
  }

  .number-cell {
    text-align: center !important;
    font-family: Courier New, monospace !important;
    font-weight: bold !important;
  }

  .text-right {
    text-align: right !important;
  }

  .text-center {
    text-align: center !important;
  }

  .text-left {
    text-align: left !important;
  }

  .font-bold {
    font-weight: bold !important;
  }

  .font-normal {
    font-weight: normal !important;
  }

  .text-xl {
    font-size: 18px !important;
  }

  .text-lg {
    font-size: 16px !important;
  }

  .text-md {
    font-size: 14px !important;
  }

  .text-sm {
    font-size: 12px !important;
  }

  .text-xs {
    font-size: 10px !important;
  }

  .p-1 {
    padding: 2px !important;
  }

  .p-2 {
    padding: 4px !important;
  }

  .p-3 {
    padding: 6px !important;
  }

  .p-4 {
    padding: 8px !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .mb-1 {
    margin-bottom: 2px !important;
  }

  .mb-2 {
    margin-bottom: 4px !important;
  }

  .mb-4 {
    margin-bottom: 8px !important;
  }

  .mt-1 {
    margin-top: 2px !important;
  }

  .mt-2 {
    margin-top: 4px !important;
  }

  .border {
    border: 1px solid #000 !important;
  }

  .border-2 {
    border: 2px solid #000 !important;
  }

  .border-3 {
    border: 3px solid #000 !important;
  }

  .border-black {
    border-color: #000 !important;
  }

  .border-t {
    border-top: 1px solid #000 !important;
  }

  .border-b {
    border-bottom: 1px solid #000 !important;
  }

  .border-l {
    border-left: 1px solid #000 !important;
  }

  .border-r {
    border-right: 1px solid #000 !important;
  }

  .border-t-2 {
    border-top: 2px solid #000 !important;
  }

  .border-b-2 {
    border-bottom: 2px solid #000 !important;
  }

  .border-l-2 {
    border-left: 2px solid #000 !important;
  }

  .border-r-2 {
    border-right: 2px solid #000 !important;
  }

  .grid {
    table-layout: fixed;
    width: 100%;
    display: table;
  }

  .grid-cols-2 > div {
    vertical-align: top;
    width: 50%;
    display: table-cell;
  }

  .grid-cols-3 > div {
    vertical-align: top;
    width: 33.333%;
    display: table-cell;
  }

  .grid-cols-4 > div {
    vertical-align: top;
    width: 25%;
    display: table-cell;
  }

  .notes-section {
    border: 2px solid #000;
    min-height: 40px;
    margin: 8px 0;
    padding: 6px;
  }

  .signature-area {
    page-break-inside: avoid;
    margin-top: 20px;
  }

  .signature-circle {
    text-align: center;
    border: 2px solid #000;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 80px;
    margin: 0 auto;
    font-size: 11px;
    font-weight: bold;
    display: flex;
  }

  .print-footer {
    text-align: center;
    border-top: 2px solid #000;
    margin-top: 15px;
    padding-top: 6px;
    font-size: 10px;
  }

  .text-blue-600, .text-green-600, .text-red-600, .text-yellow-600, .text-purple-600 {
    color: #000 !important;
  }

  .bg-blue-100, .bg-green-100, .bg-red-100, .bg-yellow-100, .bg-purple-100, .bg-gray-100 {
    background-color: #f5f5f5 !important;
  }

  .bg-gray-50 {
    background-color: #fafafa !important;
  }

  .rounded, .rounded-lg, .rounded-md, .rounded-sm {
    border-radius: 0 !important;
  }

  .shadow, .shadow-sm, .shadow-md, .shadow-lg {
    box-shadow: none !important;
  }

  .text-gray-600, .text-gray-700, .text-gray-800 {
    color: #333 !important;
  }

  .text-gray-500 {
    color: #666 !important;
  }

  .font-mono {
    font-family: Courier New, Lucida Console, monospace !important;
  }

  .arabic-text {
    text-align: right;
    direction: rtl;
    font-family: Tahoma, Arial, sans-serif !important;
  }

  .english-text {
    text-align: left;
    direction: ltr;
    font-family: Arial, Times New Roman, serif !important;
  }

  table {
    border-collapse: collapse !important;
  }

  @page {
    margin: 15mm;
    size: A4;
  }
}

@media screen {
  .print-content {
    background: #fff;
    max-width: 210mm;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, .1);
  }
}

/*# sourceMappingURL=src_styles_classic-print_6f8c824a.css.map*/