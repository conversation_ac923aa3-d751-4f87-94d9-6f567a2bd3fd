(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/xlsx/xlsx.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_xlsx_xlsx_mjs_ad755052._.js",
  "static/chunks/node_modules_xlsx_xlsx_mjs_323d4cba._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/xlsx/xlsx.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_7173dfde._.js",
  "static/chunks/node_modules_9beba542._.js",
  "static/chunks/node_modules_jspdf_dist_jspdf_es_min_323d4cba.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
    });
});
}),
}]);