'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AppLayout from '@/components/AppLayout'
import {
  BarChart3,
  FileText,
  Calendar,
  Users,
  Building2,
  Package,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  Printer,
  Eye,
  DollarSign,
  ShoppingCart,
  Wallet,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RotateCcw,
  PieChart,
  LineChart,
  Activity,
  Target,
  Layers,
  Database,
  RefreshCw,
  Settings,
  ChevronDown,
  ChevronRight,
  Plus,
  Minus,
  CalendarDays,
  FileSpreadsheet,
  X,
  ExternalLink,
  Edit,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Receipt,
  User,
  Building,
  Package2,
  Calendar as CalendarIcon,
  ArrowRight,
  Info,
  Zap
} from 'lucide-react'
import {
  getSalesInvoices,
  getPurchaseInvoices,
  getCashTransactions,
  getReturns,
  getCustomers,
  getSuppliers,
  getMedicines
} from '@/lib/database'

// تعريف أنواع التقارير
type ReportType = 
  | 'sales_summary'
  | 'purchases_summary'
  | 'financial_summary'
  | 'inventory_report'
  | 'customer_statement'
  | 'supplier_statement'
  | 'profit_loss'
  | 'cash_flow'
  | 'top_products'
  | 'customer_analysis'

// تعريف فلاتر التقارير
interface ReportFilters {
  dateRange: {
    start: string
    end: string
    preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'
  }
  customer?: string
  supplier?: string
  product?: string
  paymentStatus?: 'all' | 'paid' | 'partial' | 'pending'
  paymentMethod?: 'all' | 'cash' | 'credit'
  includeReturns?: boolean
}

// تعريف بيانات التقرير
interface ReportData {
  title: string
  summary: {
    totalRecords: number
    totalAmount: number
    averageAmount: number
    [key: string]: any
  }
  data: any[]
  charts?: {
    type: 'bar' | 'pie' | 'line'
    data: any[]
    labels: string[]
  }[]
}

// قائمة التقارير المتاحة
const AVAILABLE_REPORTS = [
  {
    id: 'sales_summary' as ReportType,
    title: 'تقرير المبيعات الشامل',
    description: 'تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية',
    icon: ShoppingCart,
    category: 'sales',
    color: 'bg-blue-500'
  },
  {
    id: 'purchases_summary' as ReportType,
    title: 'تقرير المشتريات الشامل',
    description: 'تقرير مفصل عن جميع عمليات المشتريات مع تحليل الموردين',
    icon: Package,
    category: 'purchases',
    color: 'bg-green-500'
  },
  {
    id: 'financial_summary' as ReportType,
    title: 'التقرير المالي الشامل',
    description: 'ملخص شامل للوضع المالي مع الأرباح والخسائر',
    icon: DollarSign,
    category: 'financial',
    color: 'bg-purple-500'
  },
  {
    id: 'inventory_report' as ReportType,
    title: 'تقرير المخزون',
    description: 'حالة المخزون والكميات المتاحة مع تحليل الحركة',
    icon: Database,
    category: 'inventory',
    color: 'bg-orange-500'
  },
  {
    id: 'customer_statement' as ReportType,
    title: 'كشف حساب العملاء',
    description: 'تفاصيل حسابات العملاء والمديونيات مع الجدول الزمني',
    icon: Users,
    category: 'customers',
    color: 'bg-indigo-500'
  },
  {
    id: 'supplier_statement' as ReportType,
    title: 'كشف حساب الموردين',
    description: 'تفاصيل حسابات الموردين والمستحقات مع تحليل الأداء',
    icon: Building2,
    category: 'suppliers',
    color: 'bg-teal-500'
  },
  {
    id: 'profit_loss' as ReportType,
    title: 'تقرير الأرباح والخسائر',
    description: 'تحليل مفصل للأرباح والخسائر مع المقارنات الزمنية',
    icon: TrendingUp,
    category: 'financial',
    color: 'bg-emerald-500'
  },
  {
    id: 'cash_flow' as ReportType,
    title: 'تقرير التدفق النقدي',
    description: 'حركة النقد الداخل والخارج مع التوقعات المستقبلية',
    icon: Wallet,
    category: 'financial',
    color: 'bg-cyan-500'
  },
  {
    id: 'top_products' as ReportType,
    title: 'أفضل المنتجات مبيعاً',
    description: 'تحليل أداء المنتجات والأدوية الأكثر ربحية',
    icon: Target,
    category: 'analytics',
    color: 'bg-rose-500'
  },
  {
    id: 'customer_analysis' as ReportType,
    title: 'تحليل العملاء',
    description: 'تحليل سلوك العملاء وأنماط الشراء مع التوصيات',
    icon: Activity,
    category: 'analytics',
    color: 'bg-violet-500'
  }
]

// الفترات الزمنية المحددة مسبقاً
const DATE_PRESETS = [
  { id: 'today', label: 'اليوم', days: 0 },
  { id: 'week', label: 'هذا الأسبوع', days: 7 },
  { id: 'month', label: 'هذا الشهر', days: 30 },
  { id: 'quarter', label: 'هذا الربع', days: 90 },
  { id: 'year', label: 'هذا العام', days: 365 },
  { id: 'custom', label: 'فترة مخصصة', days: -1 }
]

export default function ProfessionalReportsPage() {
  const router = useRouter()
  const [selectedReport, setSelectedReport] = useState<ReportType | null>(null)
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [showReportModal, setShowReportModal] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [medicines, setMedicines] = useState<any[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())

  // الفلاتر الافتراضية
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0],
      preset: 'month'
    },
    paymentStatus: 'all',
    paymentMethod: 'all',
    includeReturns: true
  })

  // تحميل البيانات الأساسية
  useEffect(() => {
    loadBasicData()
  }, [])

  const loadBasicData = async () => {
    try {
      const [customersResult, suppliersResult, medicinesResult] = await Promise.all([
        getCustomers(),
        getSuppliers(),
        getMedicines()
      ])
      
      setCustomers(customersResult.data || [])
      setSuppliers(suppliersResult.data || [])
      setMedicines(medicinesResult.data || [])
    } catch (error) {
      console.error('Error loading basic data:', error)
    }
  }

  // تحديث الفترة الزمنية حسب الاختيار المحدد مسبقاً
  const updateDatePreset = (preset: string) => {
    const today = new Date()
    let startDate = new Date()
    
    switch (preset) {
      case 'today':
        startDate = new Date(today)
        break
      case 'week':
        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'quarter':
        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'year':
        startDate = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        return
    }

    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        start: startDate.toISOString().split('T')[0],
        end: today.toISOString().split('T')[0],
        preset: preset as any
      }
    }))
  }

  // دوال التنقل والتفاعل
  const navigateToInvoice = (invoiceId: string, type: 'sales' | 'purchases') => {
    if (!invoiceId) {
      alert('⚠️ معرف الفاتورة غير متوفر')
      return
    }

    if (type === 'sales') {
      router.push(`/sales?invoice=${invoiceId}`)
      // إشعار للمستخدم
      setTimeout(() => {
        alert('🔄 جاري الانتقال إلى صفحة المبيعات...')
      }, 100)
    } else {
      router.push(`/purchases?invoice=${invoiceId}`)
      setTimeout(() => {
        alert('🔄 جاري الانتقال إلى صفحة المشتريات...')
      }, 100)
    }
  }

  const navigateToCustomer = (customerId: string) => {
    if (!customerId) {
      alert('⚠️ معرف العميل غير متوفر')
      return
    }
    router.push(`/customers?customer=${customerId}`)
    setTimeout(() => {
      alert('🔄 جاري الانتقال إلى صفحة العملاء...')
    }, 100)
  }

  const navigateToSupplier = (supplierId: string) => {
    if (!supplierId) {
      alert('⚠️ معرف المورد غير متوفر')
      return
    }
    router.push(`/suppliers?supplier=${supplierId}`)
    setTimeout(() => {
      alert('🔄 جاري الانتقال إلى صفحة الموردين...')
    }, 100)
  }

  const navigateToMedicine = (medicineId: string) => {
    if (!medicineId) {
      alert('⚠️ معرف الدواء غير متوفر')
      return
    }
    router.push(`/inventory?medicine=${medicineId}`)
    setTimeout(() => {
      alert('🔄 جاري الانتقال إلى صفحة المخزون...')
    }, 100)
  }

  const toggleRowExpansion = (rowIndex: number) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(rowIndex)) {
      newExpanded.delete(rowIndex)
    } else {
      newExpanded.add(rowIndex)
    }
    setExpandedRows(newExpanded)
  }

  const showInvoiceDetails = (invoice: any) => {
    alert(`تفاصيل الفاتورة:\nرقم الفاتورة: ${invoice.invoice_number}\nالتاريخ: ${new Date(invoice.created_at).toLocaleDateString('ar-EG')}\nالمبلغ: ${invoice.total_amount} د.ع`)
  }

  const showCustomerDetails = (customer: any) => {
    alert(`تفاصيل العميل:\nالاسم: ${customer.name}\nالهاتف: ${customer.phone || 'غير محدد'}\nالعنوان: ${customer.address || 'غير محدد'}`)
  }

  // توليد التقرير
  const generateReport = async (reportType: ReportType) => {
    setLoading(true)
    setSelectedReport(reportType)

    try {
      const data = await generateReportData(reportType)
      setReportData(data)
      setShowReportModal(true)
    } catch (error) {
      console.error('Error generating report:', error)
      alert('حدث خطأ أثناء توليد التقرير')
    } finally {
      setLoading(false)
    }
  }

  // توليد بيانات التقرير
  const generateReportData = async (reportType: ReportType): Promise<ReportData> => {
    switch (reportType) {
      case 'sales_summary':
        return await generateSalesReport()
      case 'purchases_summary':
        return await generatePurchasesReport()
      case 'financial_summary':
        return await generateFinancialReport()
      case 'inventory_report':
        return await generateInventoryReport()
      case 'customer_statement':
        return await generateCustomerStatement()
      case 'supplier_statement':
        return await generateSupplierStatement()
      case 'profit_loss':
        return await generateProfitLossReport()
      case 'cash_flow':
        return await generateCashFlowReport()
      case 'top_products':
        return await generateTopProductsReport()
      case 'customer_analysis':
        return await generateCustomerAnalysisReport()
      default:
        return {
          title: AVAILABLE_REPORTS.find(r => r.id === reportType)?.title || 'تقرير',
          summary: {
            totalRecords: 0,
            totalAmount: 0,
            averageAmount: 0
          },
          data: []
        }
    }
  }

  // تقرير المبيعات
  const generateSalesReport = async (): Promise<ReportData> => {
    const result = await getSalesInvoices()
    const sales = result.data || []

    // فلترة البيانات حسب التاريخ والفلاتر
    const filteredSales = sales.filter(sale => {
      const saleDate = new Date(sale.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')

      if (saleDate < startDate || saleDate > endDate) return false
      if (filters.paymentStatus && filters.paymentStatus !== 'all' && sale.payment_status !== filters.paymentStatus) return false
      if (filters.paymentMethod && filters.paymentMethod !== 'all' && sale.payment_method !== filters.paymentMethod) return false
      if (filters.customer && sale.customer_id !== filters.customer) return false

      return true
    })

    const totalAmount = filteredSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
    const averageAmount = filteredSales.length > 0 ? totalAmount / filteredSales.length : 0
    const paidAmount = filteredSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)
    const pendingAmount = totalAmount - paidAmount

    // إضافة تفاصيل المواد لكل فاتورة
    const salesWithItems = filteredSales.map(sale => {
      const items = sale.sales_invoice_items || []
      const itemsText = items.length > 0
        ? items.map((item: any) => {
            const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد'
            return `${medicineName} (${item.quantity})`
          }).join(', ')
        : 'لا توجد مواد'

      return {
        ...sale,
        itemsText
      }
    })

    return {
      title: 'تقرير المبيعات الشامل',
      summary: {
        totalRecords: filteredSales.length,
        totalAmount,
        averageAmount,
        paidAmount,
        pendingAmount
      },
      data: salesWithItems.map(sale => ({
        'رقم الفاتورة': sale.invoice_number,
        'العميل': sale.customer_name || 'عميل نقدي',
        'التاريخ': new Date(sale.created_at).toLocaleDateString('ar-EG'),
        'الوقت': new Date(sale.created_at).toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' }),
        'المبلغ الإجمالي': (sale.final_amount || 0).toLocaleString() + ' د.ع',
        'الخصم': (sale.discount || 0).toLocaleString() + ' د.ع',
        'المبلغ المدفوع': (sale.paid_amount || 0).toLocaleString() + ' د.ع',
        'المبلغ المتبقي': ((sale.final_amount || 0) - (sale.paid_amount || 0)).toLocaleString() + ' د.ع',
        'حالة الدفع': sale.payment_status === 'paid' ? 'مدفوع' : sale.payment_status === 'partial' ? 'جزئي' : 'معلق',
        'طريقة الدفع': sale.payment_method === 'cash' ? 'نقداً' : 'آجل',
        'عدد الأصناف': (sale.sales_invoice_items || []).length,
        'المواد': sale.itemsText,
        'ملاحظات': sale.notes || 'لا توجد ملاحظات',
        'معرف الفاتورة': sale.id,
        'معرف العميل': sale.customer_id || null,
        'نوع السجل': 'فاتورة مبيعات'
      })),
      charts: [
        {
          type: 'pie' as const,
          data: [
            { name: 'مدفوع', value: filteredSales.filter(s => s.payment_status === 'paid').length },
            { name: 'جزئي', value: filteredSales.filter(s => s.payment_status === 'partial').length },
            { name: 'معلق', value: filteredSales.filter(s => s.payment_status === 'pending').length }
          ],
          labels: ['مدفوع', 'جزئي', 'معلق']
        }
      ]
    }
  }

  // تقرير المشتريات
  const generatePurchasesReport = async (): Promise<ReportData> => {
    const result = await getPurchaseInvoices()
    const purchases = result.data || []

    const filteredPurchases = purchases.filter(purchase => {
      const purchaseDate = new Date(purchase.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')

      if (purchaseDate < startDate || purchaseDate > endDate) return false
      if (filters.paymentStatus && filters.paymentStatus !== 'all' && purchase.payment_status !== filters.paymentStatus) return false
      if (filters.supplier && purchase.supplier_id !== filters.supplier) return false

      return true
    })

    const totalAmount = filteredPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)
    const averageAmount = filteredPurchases.length > 0 ? totalAmount / filteredPurchases.length : 0
    const paidAmount = filteredPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)
    const pendingAmount = totalAmount - paidAmount

    // إضافة تفاصيل المواد لكل فاتورة
    const purchasesWithItems = filteredPurchases.map(purchase => {
      const items = purchase.purchase_invoice_items || []
      const itemsText = items.length > 0
        ? items.map((item: any) => {
            const medicineName = item.medicines?.name || item.medicine_name || 'غير محدد'
            return `${medicineName} (${item.quantity})`
          }).join(', ')
        : 'لا توجد مواد'

      return {
        ...purchase,
        itemsText
      }
    })

    return {
      title: 'تقرير المشتريات الشامل',
      summary: {
        totalRecords: filteredPurchases.length,
        totalAmount,
        averageAmount,
        paidAmount,
        pendingAmount
      },
      data: purchasesWithItems.map(purchase => ({
        'رقم الفاتورة': purchase.invoice_number,
        'المورد': purchase.suppliers?.name || 'غير محدد',
        'التاريخ': new Date(purchase.created_at).toLocaleDateString('ar-EG'),
        'المبلغ الإجمالي': (purchase.final_amount || 0).toLocaleString() + ' د.ع',
        'المبلغ المدفوع': (purchase.paid_amount || 0).toLocaleString() + ' د.ع',
        'المبلغ المتبقي': ((purchase.final_amount || 0) - (purchase.paid_amount || 0)).toLocaleString() + ' د.ع',
        'حالة الدفع': purchase.payment_status === 'paid' ? 'مدفوع' : purchase.payment_status === 'partial' ? 'جزئي' : 'معلق',
        'المواد': purchase.itemsText
      })),
      charts: [
        {
          type: 'pie' as const,
          data: [
            { name: 'مدفوع', value: filteredPurchases.filter(p => p.payment_status === 'paid').length },
            { name: 'جزئي', value: filteredPurchases.filter(p => p.payment_status === 'partial').length },
            { name: 'معلق', value: filteredPurchases.filter(p => p.payment_status === 'pending').length }
          ],
          labels: ['مدفوع', 'جزئي', 'معلق']
        }
      ]
    }
  }

  // التقرير المالي
  const generateFinancialReport = async (): Promise<ReportData> => {
    const [salesResult, purchasesResult, cashResult] = await Promise.all([
      getSalesInvoices(),
      getPurchaseInvoices(),
      getCashTransactions()
    ])

    const sales = salesResult.data || []
    const purchases = purchasesResult.data || []
    const transactions = cashResult.data || []

    // فلترة البيانات حسب التاريخ
    const filteredSales = sales.filter(sale => {
      const saleDate = new Date(sale.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return saleDate >= startDate && saleDate <= endDate
    })

    const filteredPurchases = purchases.filter(purchase => {
      const purchaseDate = new Date(purchase.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return purchaseDate >= startDate && purchaseDate <= endDate
    })

    const filteredTransactions = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return transactionDate >= startDate && transactionDate <= endDate
    })

    const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
    const totalExpenses = filteredPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)
    const netProfit = totalRevenue - totalExpenses
    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0

    // حساب التدفق النقدي
    const cashIncome = filteredTransactions
      .filter(t => t.transaction_type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
    const cashExpenses = filteredTransactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)
    const netCashFlow = cashIncome - cashExpenses

    return {
      title: 'التقرير المالي الشامل',
      summary: {
        totalRecords: filteredSales.length + filteredPurchases.length,
        totalAmount: totalRevenue,
        averageAmount: totalRevenue / (filteredSales.length || 1),
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin,
        cashIncome,
        cashExpenses,
        netCashFlow
      },
      data: [
        { 'البند': 'إجمالي الإيرادات', 'المبلغ': totalRevenue.toLocaleString() + ' د.ع', 'النوع': 'إيرادات' },
        { 'البند': 'إجمالي المصروفات', 'المبلغ': totalExpenses.toLocaleString() + ' د.ع', 'النوع': 'مصروفات' },
        { 'البند': 'صافي الربح', 'المبلغ': netProfit.toLocaleString() + ' د.ع', 'النوع': 'ربح' },
        { 'البند': 'هامش الربح', 'المبلغ': profitMargin.toFixed(2) + '%', 'النوع': 'نسبة' },
        { 'البند': 'النقد الداخل', 'المبلغ': cashIncome.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' },
        { 'البند': 'النقد الخارج', 'المبلغ': cashExpenses.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' },
        { 'البند': 'صافي التدفق النقدي', 'المبلغ': netCashFlow.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' }
      ],
      charts: [
        {
          type: 'pie' as const,
          data: [
            { name: 'الإيرادات', value: totalRevenue },
            { name: 'المصروفات', value: totalExpenses }
          ],
          labels: ['الإيرادات', 'المصروفات']
        }
      ]
    }
  }

  // باقي دوال التقارير (مبسطة للآن)
  const generateInventoryReport = async (): Promise<ReportData> => {
    const result = await getMedicines()
    const medicines = result.data || []

    return {
      title: 'تقرير المخزون',
      summary: {
        totalRecords: medicines.length,
        totalAmount: 0,
        averageAmount: 0
      },
      data: medicines.map(medicine => ({
        'اسم الدواء': medicine.name,
        'الشركة المصنعة': medicine.manufacturer || 'غير محدد',
        'الكمية المتاحة': medicine.medicine_batches?.reduce((sum: number, batch: any) => sum + (batch.quantity || 0), 0) || 0,
        'تاريخ الانتهاء': medicine.medicine_batches?.[0]?.expiry_date ? new Date(medicine.medicine_batches[0].expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'
      }))
    }
  }

  const generateCustomerStatement = async (): Promise<ReportData> => {
    const [salesResult, customersResult] = await Promise.all([
      getSalesInvoices(),
      getCustomers()
    ])

    const sales = salesResult.data || []
    const customers = customersResult.data || []

    // فلترة المبيعات حسب التاريخ
    const filteredSales = sales.filter(sale => {
      const saleDate = new Date(sale.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return saleDate >= startDate && saleDate <= endDate
    })

    // إذا تم تحديد عميل معين، نعرض تفاصيل فواتيره
    if (filters.customer) {
      const selectedCustomer = customers.find(c => c.id === filters.customer)
      const customerSales = filteredSales.filter(sale => sale.customer_id === filters.customer)

      if (selectedCustomer && customerSales.length > 0) {
        // عرض تفاصيل فواتير العميل مع المواد
        const salesWithItems = customerSales.map(sale => {
          const items = sale.sales_invoice_items || []
          const itemsText = items.length > 0
            ? items.map((item: any) => {
                const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد'
                return `${medicineName} (${item.quantity} × ${(item.unit_price || 0).toLocaleString()} = ${(item.total_price || 0).toLocaleString()} د.ع)`
              }).join(' | ')
            : 'لا توجد مواد'

          return {
            'رقم الفاتورة': sale.invoice_number,
            'التاريخ': new Date(sale.created_at).toLocaleDateString('ar-EG'),
            'المبلغ الإجمالي': (sale.final_amount || 0).toLocaleString() + ' د.ع',
            'المبلغ المدفوع': (sale.paid_amount || 0).toLocaleString() + ' د.ع',
            'المبلغ المتبقي': ((sale.final_amount || 0) - (sale.paid_amount || 0)).toLocaleString() + ' د.ع',
            'حالة الدفع': sale.payment_status === 'paid' ? 'مدفوع' : sale.payment_status === 'partial' ? 'جزئي' : 'معلق',
            'المواد المشتراة': itemsText
          }
        })

        const totalAmount = customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
        const paidAmount = customerSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)

        return {
          title: `كشف حساب العميل: ${selectedCustomer.name}`,
          summary: {
            totalRecords: customerSales.length,
            totalAmount,
            averageAmount: customerSales.length > 0 ? totalAmount / customerSales.length : 0,
            paidAmount,
            pendingAmount: totalAmount - paidAmount
          },
          data: salesWithItems
        }
      }
    }

    // عرض ملخص جميع العملاء
    const customersWithSales = customers.map(customer => {
      const customerSales = filteredSales.filter(sale => sale.customer_id === customer.id)
      const totalAmount = customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
      const paidAmount = customerSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)

      return {
        customer,
        totalAmount,
        paidAmount,
        salesCount: customerSales.length
      }
    }).filter(item => item.salesCount > 0) // فقط العملاء الذين لديهم مبيعات

    return {
      title: 'كشف حساب العملاء',
      summary: {
        totalRecords: customersWithSales.length,
        totalAmount: customersWithSales.reduce((sum, item) => sum + item.totalAmount, 0),
        averageAmount: customersWithSales.length > 0 ?
          customersWithSales.reduce((sum, item) => sum + item.totalAmount, 0) / customersWithSales.length : 0
      },
      data: customersWithSales.map(item => ({
        'اسم العميل': item.customer.name,
        'الهاتف': item.customer.phone || 'غير محدد',
        'العنوان': item.customer.address || 'غير محدد',
        'عدد الفواتير': item.salesCount,
        'إجمالي المبلغ': item.totalAmount.toLocaleString() + ' د.ع',
        'المبلغ المدفوع': item.paidAmount.toLocaleString() + ' د.ع',
        'المبلغ المتبقي': (item.totalAmount - item.paidAmount).toLocaleString() + ' د.ع'
      }))
    }
  }

  const generateSupplierStatement = async (): Promise<ReportData> => {
    const [purchasesResult, suppliersResult] = await Promise.all([
      getPurchaseInvoices(),
      getSuppliers()
    ])

    const purchases = purchasesResult.data || []
    const suppliers = suppliersResult.data || []

    // فلترة المشتريات حسب التاريخ
    const filteredPurchases = purchases.filter(purchase => {
      const purchaseDate = new Date(purchase.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return purchaseDate >= startDate && purchaseDate <= endDate
    })

    // إذا تم تحديد مورد معين، نعرض تفاصيل فواتيره
    if (filters.supplier) {
      const selectedSupplier = suppliers.find(s => s.id === filters.supplier)
      const supplierPurchases = filteredPurchases.filter(purchase => purchase.supplier_id === filters.supplier)

      if (selectedSupplier && supplierPurchases.length > 0) {
        // عرض تفاصيل فواتير المورد مع المواد
        const purchasesWithItems = supplierPurchases.map(purchase => {
          const items = purchase.purchase_invoice_items || []
          const itemsText = items.length > 0
            ? items.map((item: any) => {
                const medicineName = item.medicines?.name || item.medicine_name || 'غير محدد'
                return `${medicineName} (${item.quantity} × ${(item.unit_cost || 0).toLocaleString()} = ${(item.total_cost || 0).toLocaleString()} د.ع)`
              }).join(' | ')
            : 'لا توجد مواد'

          return {
            'رقم الفاتورة': purchase.invoice_number,
            'التاريخ': new Date(purchase.created_at).toLocaleDateString('ar-EG'),
            'المبلغ الإجمالي': (purchase.final_amount || 0).toLocaleString() + ' د.ع',
            'المبلغ المدفوع': (purchase.paid_amount || 0).toLocaleString() + ' د.ع',
            'المبلغ المتبقي': ((purchase.final_amount || 0) - (purchase.paid_amount || 0)).toLocaleString() + ' د.ع',
            'حالة الدفع': purchase.payment_status === 'paid' ? 'مدفوع' : purchase.payment_status === 'partial' ? 'جزئي' : 'معلق',
            'المواد المشتراة': itemsText
          }
        })

        const totalAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)
        const paidAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)

        return {
          title: `كشف حساب المورد: ${selectedSupplier.name}`,
          summary: {
            totalRecords: supplierPurchases.length,
            totalAmount,
            averageAmount: supplierPurchases.length > 0 ? totalAmount / supplierPurchases.length : 0,
            paidAmount,
            pendingAmount: totalAmount - paidAmount
          },
          data: purchasesWithItems
        }
      }
    }

    // عرض ملخص جميع الموردين
    const suppliersWithPurchases = suppliers.map(supplier => {
      const supplierPurchases = filteredPurchases.filter(purchase => purchase.supplier_id === supplier.id)
      const totalAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)
      const paidAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)

      return {
        supplier,
        totalAmount,
        paidAmount,
        purchasesCount: supplierPurchases.length
      }
    }).filter(item => item.purchasesCount > 0) // فقط الموردين الذين لديهم مشتريات

    return {
      title: 'كشف حساب الموردين',
      summary: {
        totalRecords: suppliersWithPurchases.length,
        totalAmount: suppliersWithPurchases.reduce((sum, item) => sum + item.totalAmount, 0),
        averageAmount: suppliersWithPurchases.length > 0 ?
          suppliersWithPurchases.reduce((sum, item) => sum + item.totalAmount, 0) / suppliersWithPurchases.length : 0
      },
      data: suppliersWithPurchases.map(item => ({
        'اسم المورد': item.supplier.name,
        'الهاتف': item.supplier.phone || 'غير محدد',
        'العنوان': item.supplier.address || 'غير محدد',
        'الشخص المسؤول': item.supplier.contact_person || 'غير محدد',
        'عدد الفواتير': item.purchasesCount,
        'إجمالي المبلغ': item.totalAmount.toLocaleString() + ' د.ع',
        'المبلغ المدفوع': item.paidAmount.toLocaleString() + ' د.ع',
        'المبلغ المتبقي': (item.totalAmount - item.paidAmount).toLocaleString() + ' د.ع'
      }))
    }
  }

  const generateProfitLossReport = async (): Promise<ReportData> => {
    const [salesResult, purchasesResult] = await Promise.all([
      getSalesInvoices(),
      getPurchaseInvoices()
    ])

    const sales = salesResult.data || []
    const purchases = purchasesResult.data || []

    const revenue = sales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
    const costs = purchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)
    const grossProfit = revenue - costs

    return {
      title: 'تقرير الأرباح والخسائر',
      summary: {
        totalRecords: sales.length + purchases.length,
        totalAmount: revenue,
        averageAmount: grossProfit
      },
      data: [
        { 'البند': 'الإيرادات', 'المبلغ': revenue.toLocaleString() + ' د.ع' },
        { 'البند': 'تكلفة البضاعة المباعة', 'المبلغ': costs.toLocaleString() + ' د.ع' },
        { 'البند': 'إجمالي الربح', 'المبلغ': grossProfit.toLocaleString() + ' د.ع' },
        { 'البند': 'هامش الربح الإجمالي', 'المبلغ': revenue > 0 ? ((grossProfit / revenue) * 100).toFixed(2) + '%' : '0%' }
      ]
    }
  }

  const generateCashFlowReport = async (): Promise<ReportData> => {
    const result = await getCashTransactions()
    const transactions = result.data || []

    // فلترة المعاملات حسب التاريخ
    const filteredTransactions = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.created_at)
      const startDate = new Date(filters.dateRange.start)
      const endDate = new Date(filters.dateRange.end + 'T23:59:59')
      return transactionDate >= startDate && transactionDate <= endDate
    })

    const income = filteredTransactions.filter(t => t.transaction_type === 'income').reduce((sum, t) => sum + t.amount, 0)
    const expenses = filteredTransactions.filter(t => t.transaction_type === 'expense').reduce((sum, t) => sum + t.amount, 0)
    const netFlow = income - expenses

    // تفاصيل المعاملات
    const transactionDetails = filteredTransactions.map(transaction => ({
      'التاريخ': new Date(transaction.created_at).toLocaleDateString('ar-EG'),
      'النوع': transaction.transaction_type === 'income' ? 'داخل' : 'خارج',
      'المبلغ': transaction.amount.toLocaleString() + ' د.ع',
      'الوصف': transaction.description || 'غير محدد',
      'المرجع': transaction.reference_type || 'غير محدد'
    }))

    return {
      title: 'تقرير التدفق النقدي',
      summary: {
        totalRecords: filteredTransactions.length,
        totalAmount: income,
        averageAmount: netFlow,
        income,
        expenses,
        netFlow
      },
      data: [
        { 'البند': 'إجمالي الداخل', 'المبلغ': income.toLocaleString() + ' د.ع', 'النوع': 'إيجابي' },
        { 'البند': 'إجمالي الخارج', 'المبلغ': expenses.toLocaleString() + ' د.ع', 'النوع': 'سلبي' },
        { 'البند': 'صافي التدفق النقدي', 'المبلغ': netFlow.toLocaleString() + ' د.ع', 'النوع': netFlow >= 0 ? 'إيجابي' : 'سلبي' },
        ...transactionDetails
      ],
      charts: [
        {
          type: 'pie' as const,
          data: [
            { name: 'الداخل', value: income },
            { name: 'الخارج', value: expenses }
          ],
          labels: ['الداخل', 'الخارج']
        }
      ]
    }
  }

  const generateTopProductsReport = async (): Promise<ReportData> => {
    const result = await getMedicines()
    const medicines = result.data || []

    return {
      title: 'أفضل المنتجات مبيعاً',
      summary: {
        totalRecords: medicines.length,
        totalAmount: 0,
        averageAmount: 0
      },
      data: medicines.slice(0, 10).map((medicine, index) => ({
        'الترتيب': index + 1,
        'اسم الدواء': medicine.name,
        'الشركة المصنعة': medicine.manufacturer || 'غير محدد',
        'الكمية المباعة': Math.floor(Math.random() * 100) + 1, // بيانات تجريبية
        'إجمالي المبيعات': (Math.floor(Math.random() * 1000000) + 100000).toLocaleString() + ' د.ع'
      }))
    }
  }

  const generateCustomerAnalysisReport = async (): Promise<ReportData> => {
    const [salesResult, customersResult] = await Promise.all([
      getSalesInvoices(),
      getCustomers()
    ])

    const sales = salesResult.data || []
    const customers = customersResult.data || []

    return {
      title: 'تحليل العملاء',
      summary: {
        totalRecords: customers.length,
        totalAmount: sales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0),
        averageAmount: 0
      },
      data: customers.slice(0, 10).map(customer => {
        const customerSales = sales.filter(sale => sale.customer_id === customer.id)
        return {
          'اسم العميل': customer.name,
          'عدد الزيارات': customerSales.length,
          'متوسط قيمة الطلب': customerSales.length > 0 ?
            (customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0) / customerSales.length).toLocaleString() + ' د.ع' :
            '0 د.ع',
          'آخر زيارة': customerSales.length > 0 ?
            new Date(Math.max(...customerSales.map(sale => new Date(sale.created_at).getTime()))).toLocaleDateString('ar-EG') :
            'لا توجد زيارات'
        }
      })
    }
  }

  // دوال الطباعة والتصدير - استخدام قالب لارين
  const handlePrintReport = () => {
    if (!reportData) return

    // استخدام قالب لارين للطباعة
    const { printReport } = require('@/hooks/usePrintSettings')
    const { usePrintSettings } = require('@/hooks/usePrintSettings')

    // إعدادات الطباعة الافتراضية
    const defaultSettings = {
      companyName: 'مكتب لارين العلمي',
      companyNameEn: 'LAREN SCIENTIFIC BUREAU',
      companyAddress: 'بغداد - شارع فلسطين',
      companyPhone: '+964 ************',
      companyEmail: '<EMAIL>',
      showLogo: true,
      showHeader: true,
      showFooter: true,
      footerText: 'شكراً لتعاملكم معنا',
      fontSize: 'medium',
      paperSize: 'A4',
      showBorders: true,
      showColors: false,
      includeBarcode: false,
      includeQRCode: false,
      showWatermark: false,
      headerColor: '#1f2937',
      accentColor: '#3b82f6',
      textColor: '#374151',
      backgroundColor: '#ffffff'
    }

    // طباعة التقرير باستخدام قالب لارين
    printReport(reportData.data, selectedReport || 'general', reportData.title, defaultSettings)

  }

  const handleExportExcel = async () => {
    if (!reportData) return

    try {
      // استيراد مكتبة xlsx ديناميكياً
      const XLSX = await import('xlsx')

      // إنشاء workbook جديد
      const workbook = XLSX.utils.book_new()

      // إنشاء worksheet للملخص مع تنسيق محسن
      const summaryData = [
        ['نظام إدارة الصيدلية الاحترافي'],
        [''],
        ['اسم التقرير:', reportData.title],
        ['تاريخ الإنشاء:', new Date().toLocaleDateString('ar-EG')],
        ['وقت الإنشاء:', new Date().toLocaleTimeString('ar-EG')],
        ['الفترة الزمنية:', `من ${filters.dateRange.start} إلى ${filters.dateRange.end}`],
        [''],
        ['الملخص التنفيذي'],
        ['المؤشر', 'القيمة', 'الوحدة'],
        ['إجمالي السجلات', reportData.summary.totalRecords, 'سجل'],
        ['إجمالي المبلغ', Math.round(reportData.summary.totalAmount), 'دينار عراقي'],
        ['متوسط المبلغ', Math.round(reportData.summary.averageAmount), 'دينار عراقي']
      ]

      // إضافة بيانات إضافية من الملخص
      if (reportData.summary.paidAmount !== undefined) {
        summaryData.push(['المبلغ المدفوع', Math.round(reportData.summary.paidAmount), 'دينار عراقي'])
      }
      if (reportData.summary.pendingAmount !== undefined) {
        summaryData.push(['المبلغ المعلق', Math.round(reportData.summary.pendingAmount), 'دينار عراقي'])
      }
      if (reportData.summary.totalRevenue !== undefined) {
        summaryData.push(['إجمالي الإيرادات', Math.round(reportData.summary.totalRevenue), 'دينار عراقي'])
      }
      if (reportData.summary.totalExpenses !== undefined) {
        summaryData.push(['إجمالي المصروفات', Math.round(reportData.summary.totalExpenses), 'دينار عراقي'])
      }
      if (reportData.summary.netProfit !== undefined) {
        summaryData.push(['صافي الربح', Math.round(reportData.summary.netProfit), 'دينار عراقي'])
      }
      if (reportData.summary.profitMargin !== undefined) {
        summaryData.push(['هامش الربح', Math.round(reportData.summary.profitMargin * 100) / 100, '%'])
      }

      // إضافة معلومات الفلاتر المطبقة
      summaryData.push([''])
      summaryData.push(['الفلاتر المطبقة'])
      summaryData.push(['الفلتر', 'القيمة'])
      summaryData.push(['حالة الدفع', filters.paymentStatus === 'all' ? 'جميع الحالات' :
        filters.paymentStatus === 'paid' ? 'مدفوع' :
        filters.paymentStatus === 'partial' ? 'جزئي' : 'معلق'])
      summaryData.push(['طريقة الدفع', filters.paymentMethod === 'all' ? 'جميع الطرق' :
        filters.paymentMethod === 'cash' ? 'نقداً' : 'آجل'])
      if (filters.customer) {
        const customer = customers.find(c => c.id === filters.customer)
        summaryData.push(['العميل المحدد', customer?.name || 'غير معروف'])
      }
      if (filters.supplier) {
        const supplier = suppliers.find(s => s.id === filters.supplier)
        summaryData.push(['المورد المحدد', supplier?.name || 'غير معروف'])
      }

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData)

      // تنسيق worksheet الملخص
      const summaryRange = XLSX.utils.decode_range(summaryWorksheet['!ref'] || 'A1')

      // تعيين عرض الأعمدة
      summaryWorksheet['!cols'] = [
        { width: 25 }, // العمود الأول
        { width: 20 }, // العمود الثاني
        { width: 15 }  // العمود الثالث
      ]

      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'الملخص التنفيذي')

      // إنشاء worksheet للبيانات التفصيلية
      if (reportData.data.length > 0) {
        // تحضير البيانات مع ترجمة العناوين
        const translatedData = reportData.data.map(row => {
          const translatedRow: any = {}
          Object.entries(row).forEach(([key, value]) => {
            const translatedKey = translateColumnName(key)
            translatedRow[translatedKey] = value
          })
          return translatedRow
        })

        const detailsWorksheet = XLSX.utils.json_to_sheet(translatedData)

        // تعيين عرض الأعمدة للبيانات التفصيلية
        const headers = Object.keys(translatedData[0] || {})
        detailsWorksheet['!cols'] = headers.map(header => {
          if (header === 'المواد' || header === 'المواد المشتراة') {
            return { width: 50 } // عرض أكبر لعمود المواد
          }
          return { width: 15 }
        })

        XLSX.utils.book_append_sheet(workbook, detailsWorksheet, 'البيانات التفصيلية')
      }

      // إضافة worksheet للإحصائيات إذا كانت متوفرة
      if (reportData.charts && reportData.charts.length > 0) {
        const statsData = [
          ['الإحصائيات والرسوم البيانية'],
          ['']
        ]

        reportData.charts.forEach((chart, index) => {
          statsData.push([`الرسم البياني ${index + 1}`, chart.type === 'pie' ? 'دائري' : chart.type === 'bar' ? 'عمودي' : 'خطي'])
          statsData.push(['التصنيف', 'القيمة'])

          chart.data.forEach((item: any) => {
            statsData.push([item.name || 'غير محدد', item.value || 0])
          })

          statsData.push(['']) // سطر فارغ بين الرسوم البيانية
        })

        const statsWorksheet = XLSX.utils.aoa_to_sheet(statsData)
        statsWorksheet['!cols'] = [{ width: 20 }, { width: 15 }]
        XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')
      }

      // تصدير الملف
      const fileName = `${reportData.title}_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, fileName)

    } catch (error) {
      console.error('Error exporting Excel:', error)
      // fallback إلى CSV
      handleExportCSV()
    }
  }

  const handleExportCSV = () => {
    if (!reportData) return

    // إنشاء محتوى CSV
    const csvContent = [
      // العنوان
      [reportData.title],
      [`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`],
      [`الفترة: ${filters.dateRange.start} إلى ${filters.dateRange.end}`],
      [], // سطر فارغ

      // الملخص
      ['الملخص'],
      ['إجمالي السجلات', reportData.summary.totalRecords.toLocaleString()],
      ['إجمالي المبلغ', reportData.summary.totalAmount.toLocaleString() + ' د.ع'],
      ['متوسط المبلغ', reportData.summary.averageAmount.toLocaleString() + ' د.ع'],
      [], // سطر فارغ

      // البيانات
      ...(reportData.data.length > 0 ? [
        Object.keys(reportData.data[0]), // العناوين
        ...reportData.data.map(row => Object.values(row))
      ] : [])
    ]

    const csvString = csvContent.map(row =>
      Array.isArray(row) ? row.join(',') : row
    ).join('\n')

    // إنشاء ملف وتحميله
    const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${reportData.title}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleExportPDF = async () => {
    if (!reportData) return

    try {
      // استيراد مكتبة jsPDF ديناميكياً
      const { jsPDF } = await import('jspdf')
      require('jspdf-autotable')

      // إنشاء مستند PDF جديد
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // إعداد الخط
      doc.setFont('helvetica')

      // إضافة خلفية ملونة للعنوان
      doc.setFillColor(102, 126, 234) // لون أزرق جميل
      doc.rect(0, 0, 210, 35, 'F')

      // العنوان الرئيسي
      doc.setTextColor(255, 255, 255)
      doc.setFontSize(18)
      doc.text(reportData.title, 105, 15, { align: 'center' })

      // العنوان الفرعي
      doc.setFontSize(12)
      doc.text('نظام إدارة الصيدلية الاحترافي', 105, 25, { align: 'center' })

      // إعادة تعيين لون النص
      doc.setTextColor(0, 0, 0)

      // معلومات التقرير في صندوق
      doc.setFillColor(248, 250, 252)
      doc.rect(15, 40, 180, 25, 'F')
      doc.setDrawColor(226, 232, 240)
      doc.rect(15, 40, 180, 25, 'S')

      doc.setFontSize(10)
      doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`, 20, 50)
      doc.text(`الفترة: ${filters.dateRange.start} إلى ${filters.dateRange.end}`, 20, 58)

      // الملخص في بطاقات ملونة
      let yPos = 75
      doc.setFontSize(14)
      doc.setTextColor(45, 55, 72)
      doc.text('ملخص التقرير:', 20, yPos)
      yPos += 10

      // بطاقات الملخص
      const summaryItems = [
        { label: 'إجمالي السجلات', value: reportData.summary.totalRecords.toLocaleString(), color: [59, 130, 246] },
        { label: 'إجمالي المبلغ', value: Math.round(reportData.summary.totalAmount).toLocaleString() + ' د.ع', color: [16, 185, 129] },
        { label: 'متوسط المبلغ', value: Math.round(reportData.summary.averageAmount).toLocaleString() + ' د.ع', color: [139, 92, 246] }
      ]

      // إضافة عناصر إضافية للملخص
      if (reportData.summary.paidAmount !== undefined) {
        summaryItems.push({
          label: 'المبلغ المدفوع',
          value: Math.round(reportData.summary.paidAmount).toLocaleString() + ' د.ع',
          color: [5, 150, 105]
        })
      }
      if (reportData.summary.pendingAmount !== undefined) {
        summaryItems.push({
          label: 'المبلغ المعلق',
          value: Math.round(reportData.summary.pendingAmount).toLocaleString() + ' د.ع',
          color: [220, 38, 38]
        })
      }

      // رسم بطاقات الملخص
      const cardWidth = 85
      const cardHeight = 20
      let xPos = 20

      summaryItems.forEach((item, index) => {
        if (index > 0 && index % 2 === 0) {
          yPos += 25
          xPos = 20
        }

        // خلفية البطاقة
        doc.setFillColor(item.color[0], item.color[1], item.color[2])
        doc.rect(xPos, yPos, cardWidth, cardHeight, 'F')

        // نص البطاقة
        doc.setTextColor(255, 255, 255)
        doc.setFontSize(9)
        doc.text(item.label, xPos + 5, yPos + 8)
        doc.setFontSize(12)
        doc.text(item.value, xPos + 5, yPos + 16)

        xPos += cardWidth + 10
      })

      yPos += 35

      // الجدول التفصيلي
      if (reportData.data.length > 0) {
        doc.setTextColor(0, 0, 0)
        doc.setFontSize(12)
        doc.text('تفاصيل التقرير:', 20, yPos)
        yPos += 10

        // تحضير بيانات الجدول
        const tableHeaders = Object.keys(reportData.data[0]).map(key => translateColumnName(key))
        const tableData = reportData.data.map(row =>
          Object.entries(row).map(([key, value]) => {
            if (key === 'المواد' || key === 'المواد المشتراة') {
              // تقصير نص المواد للـ PDF
              const text = String(value || 'لا توجد مواد')
              return text.length > 50 ? text.substring(0, 50) + '...' : text
            }
            return String(value || '-')
          })
        )

        // استخدام autotable لإنشاء الجدول
        ;(doc as any).autoTable({
          head: [tableHeaders],
          body: tableData,
          startY: yPos,
          styles: {
            fontSize: 7,
            cellPadding: 3,
            textColor: [45, 55, 72],
            fillColor: [255, 255, 255],
            lineColor: [226, 232, 240],
            lineWidth: 0.1
          },
          headStyles: {
            fillColor: [102, 126, 234],
            textColor: [255, 255, 255],
            fontStyle: 'bold',
            fontSize: 8
          },
          alternateRowStyles: {
            fillColor: [248, 250, 252]
          },
          margin: { top: 10, right: 15, bottom: 20, left: 15 },
          tableWidth: 'auto',
          columnStyles: {
            // تخصيص عرض أعمدة المواد
            [tableHeaders.indexOf('المواد')]: { cellWidth: 40 },
            [tableHeaders.indexOf('المواد المشتراة')]: { cellWidth: 40 }
          },
          didDrawPage: function (data: any) {
            // إضافة تذييل في كل صفحة
            const pageHeight = doc.internal.pageSize.height
            doc.setFontSize(8)
            doc.setTextColor(128, 128, 128)
            doc.text(
              `تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-EG')}`,
              105,
              pageHeight - 10,
              { align: 'center' }
            )
            doc.text(
              `صفحة ${data.pageNumber}`,
              195,
              pageHeight - 10,
              { align: 'right' }
            )
          }
        })
      }

      // حفظ الملف
      const fileName = `${reportData.title}_${new Date().toISOString().split('T')[0]}.pdf`
      doc.save(fileName)

    } catch (error) {
      console.error('Error exporting PDF:', error)
      // fallback إلى الطباعة
      alert('حدث خطأ في تصدير PDF. سيتم فتح نافذة الطباعة بدلاً من ذلك.')
      handlePrintReport()
    }
  }

  // دوال مساعدة
  const translateColumnName = (columnName: string): string => {
    const translations: { [key: string]: string } = {
      'رقم الفاتورة': 'رقم الفاتورة',
      'العميل': 'العميل',
      'المورد': 'المورد',
      'التاريخ': 'التاريخ',
      'المبلغ الإجمالي': 'المبلغ الإجمالي',
      'المبلغ المدفوع': 'المبلغ المدفوع',
      'المبلغ المتبقي': 'المبلغ المتبقي',
      'حالة الدفع': 'حالة الدفع',
      'طريقة الدفع': 'طريقة الدفع',
      'المواد': 'المواد',
      'المواد المشتراة': 'المواد المشتراة',
      'اسم العميل': 'اسم العميل',
      'اسم المورد': 'اسم المورد',
      'الهاتف': 'الهاتف',
      'العنوان': 'العنوان',
      'الشخص المسؤول': 'الشخص المسؤول',
      'عدد الفواتير': 'عدد الفواتير',
      'البند': 'البند',
      'المبلغ': 'المبلغ',
      'النوع': 'النوع',
      'الوصف': 'الوصف',
      'المرجع': 'المرجع',
      'اسم الدواء': 'اسم الدواء',
      'الشركة المصنعة': 'الشركة المصنعة',
      'الكمية المتاحة': 'الكمية المتاحة',
      'تاريخ الانتهاء': 'تاريخ الانتهاء',
      'الترتيب': 'الترتيب',
      'الكمية المباعة': 'الكمية المباعة',
      'إجمالي المبيعات': 'إجمالي المبيعات',
      'عدد الزيارات': 'عدد الزيارات',
      'متوسط قيمة الطلب': 'متوسط قيمة الطلب',
      'آخر زيارة': 'آخر زيارة',
      // English translations for fallback
      'invoice_number': 'رقم الفاتورة',
      'customer_name': 'اسم العميل',
      'supplier_name': 'اسم المورد',
      'created_at': 'التاريخ',
      'final_amount': 'المبلغ الإجمالي',
      'paid_amount': 'المبلغ المدفوع',
      'payment_status': 'حالة الدفع',
      'payment_method': 'طريقة الدفع',
      'name': 'الاسم',
      'phone': 'الهاتف',
      'address': 'العنوان',
      'quantity': 'الكمية',
      'unit_price': 'سعر الوحدة',
      'total_price': 'المجموع'
    }
    return translations[columnName] || columnName
  }

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) return '-'
    if (typeof value === 'number') return value.toLocaleString()
    if (typeof value === 'string' && value.includes('T')) {
      // تاريخ
      try {
        return new Date(value).toLocaleDateString('ar-EG')
      } catch {
        return value
      }
    }

    // تحسين عرض القيم الخاصة
    const stringValue = String(value)

    // حالة الدفع مع ألوان
    if (stringValue === 'مدفوع') {
      return '✅ مدفوع'
    } else if (stringValue === 'جزئي') {
      return '🟡 جزئي'
    } else if (stringValue === 'معلق') {
      return '🔴 معلق'
    }

    // طريقة الدفع مع رموز
    if (stringValue === 'نقداً') {
      return '💵 نقداً'
    } else if (stringValue === 'آجل') {
      return '📋 آجل'
    }

    // نوع السجل مع رموز
    if (stringValue === 'فاتورة مبيعات') {
      return '🛒 مبيعات'
    } else if (stringValue === 'فاتورة مشتريات') {
      return '📦 مشتريات'
    }

    return stringValue
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                نظام التقارير الاحترافي
              </h1>
              <p className="text-gray-600 mt-1">تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة</p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                  showFilters 
                    ? 'bg-blue-50 border-blue-200 text-blue-700' 
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Filter className="h-4 w-4" />
                الفلاتر المتقدمة
              </button>
              <button
                onClick={() => window.location.reload()}
                className="flex items-center gap-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                الفلاتر المتقدمة
              </h3>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الفترة الزمنية</label>
                <select
                  value={filters.dateRange.preset}
                  onChange={(e) => updateDatePreset(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {DATE_PRESETS.map(preset => (
                    <option key={preset.id} value={preset.id}>{preset.label}</option>
                  ))}
                </select>
              </div>

              {/* Payment Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">حالة الدفع</label>
                <select
                  value={filters.paymentStatus || 'all'}
                  onChange={(e) => setFilters(prev => ({ ...prev, paymentStatus: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="paid">مدفوع</option>
                  <option value="partial">جزئي</option>
                  <option value="pending">معلق</option>
                </select>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                <select
                  value={filters.paymentMethod || 'all'}
                  onChange={(e) => setFilters(prev => ({ ...prev, paymentMethod: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">جميع الطرق</option>
                  <option value="cash">نقداً</option>
                  <option value="credit">آجل</option>
                </select>
              </div>

              {/* Customer Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">العميل</label>
                <select
                  value={filters.customer || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع العملاء</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>

              {/* Supplier Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المورد</label>
                <select
                  value={filters.supplier || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, supplier: e.target.value || undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع الموردين</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                  ))}
                </select>
              </div>

              {/* Include Returns */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المرتجعات</label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.includeReturns || false}
                    onChange={(e) => setFilters(prev => ({ ...prev, includeReturns: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">تضمين المرتجعات</span>
                </label>
              </div>
            </div>

            {/* Custom Date Range */}
            {filters.dateRange.preset === 'custom' && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                  <input
                    type="date"
                    value={filters.dateRange.start}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, start: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                  <input
                    type="date"
                    value={filters.dateRange.end}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, end: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {AVAILABLE_REPORTS.map((report) => {
            const Icon = report.icon
            return (
              <div
                key={report.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${report.color} text-white`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <button
                      onClick={() => generateReport(report.id)}
                      disabled={loading}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1"
                    >
                      {loading && selectedReport === report.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                      عرض
                    </button>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.title}</h3>
                  <p className="text-sm text-gray-600">{report.description}</p>
                </div>
              </div>
            )
          })}
        </div>

        {/* Report Modal */}
        {showReportModal && reportData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900">{reportData.title}</h2>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handlePrintReport()}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <Printer className="h-4 w-4" />
                    طباعة
                  </button>
                  <button
                    onClick={() => handleExportExcel()}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    تصدير Excel
                  </button>
                  <button
                    onClick={() => handleExportPDF()}
                    className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    <Download className="h-4 w-4" />
                    تصدير PDF
                  </button>
                  <button
                    onClick={() => setShowReportModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                {/* Enhanced Report Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600">إجمالي السجلات</p>
                        <p className="text-2xl font-bold text-blue-900">{reportData.summary.totalRecords?.toLocaleString() || 0}</p>
                        <p className="text-xs text-blue-500 mt-1">📊 عدد العمليات</p>
                      </div>
                      <div className="bg-blue-200 p-2 rounded-full">
                        <FileText className="h-6 w-6 text-blue-700" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600">إجمالي المبلغ</p>
                        <p className="text-2xl font-bold text-green-900">{Math.round(reportData.summary.totalAmount || 0).toLocaleString()} د.ع</p>
                        <p className="text-xs text-green-500 mt-1">💰 القيمة الإجمالية</p>
                      </div>
                      <div className="bg-green-200 p-2 rounded-full">
                        <DollarSign className="h-6 w-6 text-green-700" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-600">متوسط المبلغ</p>
                        <p className="text-2xl font-bold text-purple-900">{Math.round(reportData.summary.averageAmount || 0).toLocaleString()} د.ع</p>
                        <p className="text-xs text-purple-500 mt-1">📈 متوسط العملية</p>
                      </div>
                      <div className="bg-purple-200 p-2 rounded-full">
                        <BarChart3 className="h-6 w-6 text-purple-700" />
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-4 border border-orange-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-600">الفترة الزمنية</p>
                        <p className="text-sm font-bold text-orange-900">
                          {new Date(filters.dateRange.start).toLocaleDateString('ar-EG')}
                        </p>
                        <p className="text-xs text-orange-500">إلى {new Date(filters.dateRange.end).toLocaleDateString('ar-EG')}</p>
                      </div>
                      <div className="bg-orange-200 p-2 rounded-full">
                        <CalendarIcon className="h-6 w-6 text-orange-700" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Summary Cards for Sales Reports */}
                {reportData.summary.paidAmount !== undefined && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-4 border border-emerald-200 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-emerald-600">المبلغ المدفوع</p>
                          <p className="text-2xl font-bold text-emerald-900">{Math.round(reportData.summary.paidAmount || 0).toLocaleString()} د.ع</p>
                          <p className="text-xs text-emerald-500 mt-1">✅ المحصل</p>
                        </div>
                        <div className="bg-emerald-200 p-2 rounded-full">
                          <CheckCircle className="h-6 w-6 text-emerald-700" />
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 border border-red-200 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-red-600">المبلغ المعلق</p>
                          <p className="text-2xl font-bold text-red-900">{Math.round(reportData.summary.pendingAmount || 0).toLocaleString()} د.ع</p>
                          <p className="text-xs text-red-500 mt-1">⏳ غير محصل</p>
                        </div>
                        <div className="bg-red-200 p-2 rounded-full">
                          <Clock className="h-6 w-6 text-red-700" />
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg p-4 border border-indigo-200 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-indigo-600">نسبة التحصيل</p>
                          <p className="text-2xl font-bold text-indigo-900">
                            {reportData.summary.totalAmount > 0
                              ? Math.round((reportData.summary.paidAmount / reportData.summary.totalAmount) * 100)
                              : 0}%
                          </p>
                          <p className="text-xs text-indigo-500 mt-1">📊 معدل الدفع</p>
                        </div>
                        <div className="bg-indigo-200 p-2 rounded-full">
                          <Target className="h-6 w-6 text-indigo-700" />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                  {reportData.summary.netProfit !== undefined && (
                    <div className="bg-cyan-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-cyan-600">صافي الربح</p>
                          <p className="text-2xl font-bold text-cyan-900">{Math.round(reportData.summary.netProfit).toLocaleString()} د.ع</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-cyan-600" />
                      </div>
                    </div>
                  )}

                  {reportData.summary.profitMargin !== undefined && (
                    <div className="bg-indigo-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-indigo-600">هامش الربح</p>
                          <p className="text-2xl font-bold text-indigo-900">{Math.round(reportData.summary.profitMargin * 100) / 100}%</p>
                        </div>
                        <Target className="h-8 w-8 text-indigo-600" />
                      </div>
                    </div>
                  )}
                </div>

                {/* Quick Stats Section */}
                {reportData.data.length > 0 && (
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 mb-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <Activity className="h-5 w-5 text-blue-600" />
                        إحصائيات سريعة
                      </h3>
                      <div className="flex gap-2">
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                          📊 تحليل البيانات
                        </span>
                        <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                          ✅ محدث
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {reportData.data.filter(row => row['حالة الدفع']?.includes('مدفوع')).length}
                        </div>
                        <div className="text-xs text-gray-600">فواتير مدفوعة</div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {reportData.data.filter(row => row['حالة الدفع']?.includes('جزئي')).length}
                        </div>
                        <div className="text-xs text-gray-600">فواتير جزئية</div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {reportData.data.filter(row => row['حالة الدفع']?.includes('معلق')).length}
                        </div>
                        <div className="text-xs text-gray-600">فواتير معلقة</div>
                      </div>

                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {reportData.data.filter(row => row['طريقة الدفع']?.includes('نقداً')).length}
                        </div>
                        <div className="text-xs text-gray-600">دفع نقدي</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Enhanced Report Data Table */}
                {reportData.data.length > 0 && (
                  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                            <Database className="h-5 w-5 text-blue-600" />
                            تفاصيل التقرير التفاعلي
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            عدد السجلات: <span className="font-semibold text-blue-600">{reportData.data.length}</span>
                            {expandedRows.size > 0 && (
                              <span className="mr-2">• موسع: <span className="font-semibold text-green-600">{expandedRows.size}</span></span>
                            )}
                          </p>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <button
                            onClick={() => setExpandedRows(new Set(Array.from({length: reportData.data.length}, (_, i) => i)))}
                            className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors flex items-center gap-1"
                          >
                            <Plus className="h-3 w-3" />
                            توسيع الكل
                          </button>
                          <button
                            onClick={() => setExpandedRows(new Set())}
                            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-1"
                          >
                            <Minus className="h-3 w-3" />
                            طي الكل
                          </button>
                          <div className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md flex items-center gap-1">
                            <Zap className="h-3 w-3" />
                            تفاعلي
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                              تفاصيل
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                              إجراءات
                            </th>
                            {Object.keys(reportData.data[0] || {})
                              .filter(key => !['معرف الفاتورة', 'معرف العميل', 'نوع السجل', 'المواد', 'ملاحظات'].includes(key))
                              .map((key) => (
                              <th key={key} className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {translateColumnName(key)}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {reportData.data.map((row, index) => (
                            <>
                              <tr key={index} className="hover:bg-gray-50">
                                {/* Expand/Collapse Button */}
                                <td className="px-4 py-3 text-center">
                                  <button
                                    onClick={() => toggleRowExpansion(index)}
                                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                                  >
                                    {expandedRows.has(index) ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                  </button>
                                </td>

                                {/* Action Buttons */}
                                <td className="px-4 py-3">
                                  <div className="flex gap-1">
                                    {row['نوع السجل'] === 'فاتورة مبيعات' && row['معرف الفاتورة'] && (
                                      <button
                                        onClick={() => navigateToInvoice(row['معرف الفاتورة'], 'sales')}
                                        className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                                        title="عرض الفاتورة"
                                      >
                                        <Receipt className="h-4 w-4" />
                                      </button>
                                    )}
                                    {row['نوع السجل'] === 'فاتورة مشتريات' && row['معرف الفاتورة'] && (
                                      <button
                                        onClick={() => navigateToInvoice(row['معرف الفاتورة'], 'purchases')}
                                        className="p-1 text-green-600 hover:text-green-800 transition-colors"
                                        title="عرض فاتورة المشتريات"
                                      >
                                        <Package2 className="h-4 w-4" />
                                      </button>
                                    )}
                                    {row['معرف العميل'] && (
                                      <button
                                        onClick={() => navigateToCustomer(row['معرف العميل'])}
                                        className="p-1 text-purple-600 hover:text-purple-800 transition-colors"
                                        title="عرض العميل"
                                      >
                                        <User className="h-4 w-4" />
                                      </button>
                                    )}
                                    <button
                                      onClick={() => toggleRowExpansion(index)}
                                      className="p-1 text-gray-600 hover:text-gray-800 transition-colors"
                                      title="عرض التفاصيل"
                                    >
                                      <Info className="h-4 w-4" />
                                    </button>
                                  </div>
                                </td>

                                {/* Main Data Columns */}
                                {Object.entries(row)
                                  .filter(([key]) => !['معرف الفاتورة', 'معرف العميل', 'نوع السجل', 'المواد', 'ملاحظات'].includes(key))
                                  .map(([key, value]: [string, any], cellIndex) => (
                                  <td
                                    key={cellIndex}
                                    className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap"
                                  >
                                    {formatCellValue(value)}
                                  </td>
                                ))}
                              </tr>

                              {/* Expanded Row Details */}
                              {expandedRows.has(index) && (
                                <tr className="bg-gray-50">
                                  <td colSpan={Object.keys(reportData.data[0] || {}).length + 2} className="px-6 py-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {/* Additional Details */}
                                      <div className="space-y-2">
                                        <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                                          <Info className="h-4 w-4" />
                                          معلومات إضافية
                                        </h4>
                                        {row['ملاحظات'] && (
                                          <div className="text-sm">
                                            <span className="font-medium text-gray-700">الملاحظات:</span>
                                            <p className="text-gray-600 mt-1">{row['ملاحظات']}</p>
                                          </div>
                                        )}
                                        <div className="text-sm">
                                          <span className="font-medium text-gray-700">معرف السجل:</span>
                                          <span className="text-gray-600 ml-2 font-mono">{row['معرف الفاتورة'] || 'غير محدد'}</span>
                                        </div>
                                      </div>

                                      {/* Items Details */}
                                      {row['المواد'] && (
                                        <div className="space-y-2">
                                          <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                                            <Package className="h-4 w-4" />
                                            تفاصيل المواد
                                          </h4>
                                          <div className="text-sm text-gray-600 bg-white p-3 rounded border max-h-32 overflow-y-auto">
                                            {row['المواد']}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              )}
                            </>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Charts Section */}
                {reportData.charts && reportData.charts.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">الرسوم البيانية</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {reportData.charts.map((chart, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4">
                          <h4 className="text-md font-medium text-gray-700 mb-2">
                            {chart.type === 'bar' ? 'رسم بياني عمودي' :
                             chart.type === 'pie' ? 'رسم بياني دائري' :
                             'رسم بياني خطي'}
                          </h4>
                          <div className="h-64 flex items-center justify-center text-gray-500">
                            <PieChart className="h-16 w-16" />
                            <span className="mr-2">الرسم البياني سيتم عرضه هنا</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
