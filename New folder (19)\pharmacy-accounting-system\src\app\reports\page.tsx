'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AppLayout from '@/components/AppLayout'
import {
  BarChart3,
  FileText,
  Calendar,
  Users,
  Package,
  TrendingUp,
  DollarSign,
  Activity,
  Eye,
  RefreshCw,
  Filter,
  Settings,
  X,
  Printer,
  Download,
  FileSpreadsheet,
  Plus,
  Minus,
  ChevronDown,
  ChevronRight,
  Receipt,
  User,
  Building,
  Package2,
  Info,
  Zap,
  Database,
  CheckCircle,
  Clock,
  Target,
  PieChart,
  ExternalLink,
  Edit,
  Phone,
  Mail,
  MapPin,
  CreditCard
} from 'lucide-react'

import {
  getSalesInvoices,
  getPurchaseInvoices,
  getCashTransactions,
  getReturns,
  getCustomers,
  getSuppliers,
  getMedicines
} from '@/lib/database'

// تعريف أنواع التقارير
type ReportType = 
  | 'sales_summary'
  | 'purchases_summary'
  | 'financial_summary'
  | 'inventory_report'
  | 'customer_statement'
  | 'supplier_statement'
  | 'profit_loss'
  | 'cash_flow'
  | 'top_products'
  | 'customer_analysis'

// تعريف فلاتر التقارير
interface ReportFilters {
  dateRange: {
    start: string
    end: string
    preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'
  }
  customer?: string
  supplier?: string
  paymentStatus?: 'all' | 'paid' | 'partial' | 'pending'
  paymentMethod?: 'all' | 'cash' | 'credit'
  includeReturns?: boolean
}

// تعريف بيانات التقرير
interface ReportData {
  title: string
  summary: {
    totalRecords: number
    totalAmount: number
    averageAmount: number
    paidAmount?: number
    pendingAmount?: number
    totalRevenue?: number
    totalExpenses?: number
    netProfit?: number
    profitMargin?: number
    cashIncome?: number
    cashExpenses?: number
  }
  data: any[]
  charts?: {
    type: 'bar' | 'pie' | 'line'
    data: any[]
    labels: string[]
  }[]
}

// قائمة التقارير المتاحة
const AVAILABLE_REPORTS = [
  {
    id: 'sales_summary' as ReportType,
    title: 'تقرير المبيعات الشامل',
    description: 'تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية',
    icon: BarChart3,
    category: 'sales',
    color: 'bg-blue-500'
  },
  {
    id: 'purchases_summary' as ReportType,
    title: 'تقرير المشتريات الشامل',
    description: 'تقرير مفصل عن جميع عمليات المشتريات والموردين',
    icon: Package,
    category: 'purchases',
    color: 'bg-green-500'
  },
  {
    id: 'financial_summary' as ReportType,
    title: 'التقرير المالي الشامل',
    description: 'تحليل مالي شامل للإيرادات والمصروفات والأرباح',
    icon: DollarSign,
    category: 'financial',
    color: 'bg-emerald-500'
  },
  {
    id: 'inventory_report' as ReportType,
    title: 'تقرير المخزون',
    description: 'حالة المخزون الحالية والأدوية المتوفرة',
    icon: Package2,
    category: 'inventory',
    color: 'bg-purple-500'
  },
  {
    id: 'customer_statement' as ReportType,
    title: 'كشف حساب العملاء',
    description: 'تفاصيل حسابات العملاء والمبالغ المستحقة',
    icon: Users,
    category: 'customers',
    color: 'bg-indigo-500'
  },
  {
    id: 'supplier_statement' as ReportType,
    title: 'كشف حساب الموردين',
    description: 'تفاصيل حسابات الموردين والمبالغ المستحقة',
    icon: Building,
    category: 'suppliers',
    color: 'bg-cyan-500'
  },
  {
    id: 'profit_loss' as ReportType,
    title: 'تقرير الأرباح والخسائر',
    description: 'تحليل الأرباح والخسائر للفترة المحددة',
    icon: TrendingUp,
    category: 'financial',
    color: 'bg-orange-500'
  },
  {
    id: 'cash_flow' as ReportType,
    title: 'تقرير التدفق النقدي',
    description: 'تتبع التدفقات النقدية الداخلة والخارجة',
    icon: Activity,
    category: 'financial',
    color: 'bg-teal-500'
  },
  {
    id: 'top_products' as ReportType,
    title: 'أفضل المنتجات مبيعاً',
    description: 'تحليل أداء المنتجات والأدوية الأكثر مبيعاً',
    icon: FileText,
    category: 'analytics',
    color: 'bg-pink-500'
  },
  {
    id: 'customer_analysis' as ReportType,
    title: 'تحليل سلوك العملاء',
    description: 'تحليل أنماط شراء العملاء وسلوكهم',
    icon: Users,
    category: 'analytics',
    color: 'bg-violet-500'
  }
]

export default function ReportsPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [showReportModal, setShowReportModal] = useState(false)
  const [selectedReport, setSelectedReport] = useState<ReportType | null>(null)
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [customers, setCustomers] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [medicines, setMedicines] = useState<any[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())

  // الفلاتر الافتراضية
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0],
      preset: 'month'
    },
    paymentStatus: 'all',
    paymentMethod: 'all',
    includeReturns: true
  })

  // تحميل البيانات الأساسية
  useEffect(() => {
    loadBasicData()
  }, [])

  const loadBasicData = async () => {
    try {
      const [customersResult, suppliersResult, medicinesResult] = await Promise.all([
        getCustomers(),
        getSuppliers(),
        getMedicines()
      ])
      
      setCustomers(customersResult.data || [])
      setSuppliers(suppliersResult.data || [])
      setMedicines(medicinesResult.data || [])
    } catch (error) {
      console.error('Error loading basic data:', error)
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                نظام التقارير الاحترافي
              </h1>
              <p className="text-gray-600 mt-1">تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة</p>
            </div>
          </div>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {AVAILABLE_REPORTS.map((report) => {
            const Icon = report.icon
            return (
              <div
                key={report.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-lg ${report.color} text-white`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <button
                      onClick={() => alert('قريباً...')}
                      disabled={loading}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1"
                    >
                      <Eye className="h-4 w-4" />
                      عرض
                    </button>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.title}</h3>
                  <p className="text-sm text-gray-600">{report.description}</p>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </AppLayout>
  )
}
