'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import NotificationDropdown from './NotificationDropdown'
import MobileMenuButton from './MobileMenuButton'
import {
  Search,
  User,
  LogOut,
  Settings,
  Shield,
  ChevronDown,
  Activity
} from 'lucide-react'

interface HeaderProps {
  onMobileMenuToggle?: () => void
  isMobileMenuOpen?: boolean
}

export default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const { user, logout } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    await logout()
    router.push('/login')
  }

  const getRoleDisplayName = (role: string) => {
    const roleNames: Record<string, string> = {
      admin: 'مدير النظام',
      manager: 'مدير',
      pharmacist: 'صيدلي',
      cashier: 'كاشير',
      viewer: 'مشاهد'
    }
    return roleNames[role] || role
  }

  const getRoleColor = (role: string) => {
    const colors: Record<string, string> = {
      admin: 'bg-red-600',
      manager: 'bg-purple-600',
      pharmacist: 'bg-blue-600',
      cashier: 'bg-green-600',
      viewer: 'bg-gray-600'
    }
    return colors[role] || 'bg-gray-600'
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-40 header-mobile">
      <div className="flex items-center justify-between h-full px-3 md:px-6">
        <div className="flex items-center gap-2 md:gap-4">
          {/* Mobile Menu Button */}
          <MobileMenuButton
            isOpen={isMobileMenuOpen}
            onClick={onMobileMenuToggle || (() => console.log('⚠️ No onMobileMenuToggle function provided!'))}
          />

          {/* Mobile Search - Hidden on very small screens */}
          <div className="relative hidden sm:hidden md:block">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="البحث..."
              className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile"
            />
          </div>

          {/* Mobile Search Icon - Visible only on small screens */}
          <button className="md:hidden p-2 hover:bg-gray-100 rounded-lg">
            <Search className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        <div className="flex items-center gap-2 md:gap-4">
          {/* Notifications */}
          <NotificationDropdown />

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]"
            >
              {/* Hide user info text on mobile */}
              <div className="text-right hidden md:block">
                <p className="text-sm font-medium text-gray-800">{user?.full_name || 'مستخدم'}</p>
                <p className="text-xs text-gray-500">{user ? getRoleDisplayName(user.role) : ''}</p>
              </div>
              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>
                <User className="h-4 w-4 text-white" />
              </div>
              <ChevronDown className="h-4 w-4 text-gray-400 hidden md:block" />
            </button>

            {/* Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile">
                {/* User Info */}
                <div className="px-4 py-3 border-b border-gray-100">
                  <div className="flex items-center gap-3">
                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>
                      <User className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{user?.full_name}</p>
                      <p className="text-xs text-gray-500">@{user?.username}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :
                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :
                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      <Shield className="h-3 w-3 ml-1" />
                      {user ? getRoleDisplayName(user.role) : ''}
                    </span>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-1">
                  <button
                    onClick={() => {
                      setShowUserMenu(false)
                      router.push('/profile')
                    }}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <User className="h-4 w-4" />
                    الملف الشخصي
                  </button>

                  <button
                    onClick={() => {
                      setShowUserMenu(false)
                      router.push('/activity-log')
                    }}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Activity className="h-4 w-4" />
                    سجل النشاطات
                  </button>

                  <button
                    onClick={() => {
                      setShowUserMenu(false)
                      router.push('/settings')
                    }}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Settings className="h-4 w-4" />
                    الإعدادات
                  </button>
                </div>

                {/* Logout */}
                <div className="border-t border-gray-100 pt-1">
                  <button
                    onClick={() => {
                      setShowUserMenu(false)
                      handleLogout()
                    }}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    <LogOut className="h-4 w-4" />
                    تسجيل الخروج
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  )
}
