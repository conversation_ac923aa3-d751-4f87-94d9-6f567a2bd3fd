{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { usePermissions } from '@/contexts/AuthContext'\nimport {\n  Home,\n  ShoppingCart,\n  Package,\n  Users,\n  UserCheck,\n  RotateCcw,\n  BarChart3,\n  Settings,\n  Pill,\n  FileText,\n  Wallet,\n  Shield,\n  Activity,\n  Bell,\n  Wrench,\n  Printer,\n  Bug\n} from 'lucide-react'\n\ninterface MenuItem {\n  title: string\n  href: string\n  icon: any\n  permission?: string\n  requireAny?: string[]\n}\n\nconst getMenuItems = (permissions: any): MenuItem[] => [\n  {\n    title: 'الرئيسية',\n    href: '/',\n    icon: Home\n  },\n  {\n    title: 'إدارة المخزون',\n    href: '/inventory',\n    icon: Package,\n    permission: 'inventory_view'\n  },\n  {\n    title: 'المبيعات',\n    href: '/sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  },\n\n  {\n    title: 'المشتريات',\n    href: '/purchases',\n    icon: Pill,\n    permission: 'purchases_view'\n  },\n\n  {\n    title: 'العملاء',\n    href: '/customers',\n    icon: Users,\n    permission: 'customers_view'\n  },\n  {\n    title: 'الموردين',\n    href: '/suppliers',\n    icon: UserCheck,\n    permission: 'suppliers_view'\n  },\n  {\n    title: 'المرتجعات',\n    href: '/returns',\n    icon: RotateCcw,\n    permission: 'returns_view'\n  },\n\n  {\n    title: 'الصندوق',\n    href: '/cashbox',\n    icon: Wallet,\n    permission: 'cashbox_view'\n  },\n  {\n    title: 'التقارير',\n    href: '/reports',\n    icon: BarChart3,\n    permission: 'reports_view'\n  },\n  {\n    title: 'إدارة المستخدمين',\n    href: '/users',\n    icon: Shield,\n    permission: 'users_view'\n  },\n  {\n    title: 'سجل النشاطات',\n    href: '/activity-log',\n    icon: Activity,\n    permission: 'users_view'\n  },\n  {\n    title: 'التنبيهات',\n    href: '/notifications',\n    icon: Bell\n  },\n  {\n    title: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    permission: 'settings_view'\n  },\n  {\n    title: 'إصلاح البيانات',\n    href: '/fix-data',\n    icon: Wrench,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار الطباعة',\n    href: '/test-print',\n    icon: Printer,\n    permission: 'sales_view'\n  },\n  {\n    title: 'تشخيص البيانات',\n    href: '/debug-data',\n    icon: Bug,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار المبيعات',\n    href: '/debug-sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  }\n]\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nexport default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {\n  const pathname = usePathname()\n  const { hasPermission, permissions } = usePermissions()\n\n\n\n  const menuItems = getMenuItems(permissions)\n\n  // إغلاق القائمة عند تغيير الصفحة\n  useEffect(() => {\n    if (onClose) {\n      onClose()\n    }\n  }, [pathname, onClose])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.getElementById('mobile-sidebar')\n      const menuButton = document.getElementById('mobile-menu-button')\n\n      if (sidebar && !sidebar.contains(event.target as Node) &&\n          menuButton && !menuButton.contains(event.target as Node)) {\n        if (onClose) {\n          onClose()\n        }\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  // تصفية العناصر بناءً على الصلاحيات\n  const visibleMenuItems = menuItems.filter(item => {\n    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)\n    return hasPermission(item.permission as any)\n  })\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Desktop Sidebar - Always visible on desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:right-0 md:z-30\">\n        <div className=\"flex flex-col flex-grow bg-gradient-to-b from-white to-gray-50 shadow-xl border-l border-gray-200\">\n          <div className=\"flex items-center gap-3 p-6 bg-gradient-to-r from-blue-600 to-indigo-700 text-white\">\n            <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n              <Pill className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-bold\">نظام الصيدلية</h1>\n              <p className=\"text-sm text-blue-100\">مكتب لارين العلمي</p>\n            </div>\n          </div>\n\n          <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n            {visibleMenuItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'\n                      : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800'\n                  }`}\n                >\n                  <Icon className={`h-5 w-5 ${isActive ? 'text-white' : ''}`} />\n                  <span className=\"font-medium\">{item.title}</span>\n                  {isActive && (\n                    <div className=\"mr-auto w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          <div className=\"p-4\">\n            <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200\">\n              <div className=\"flex items-center justify-center gap-2 mb-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <p className=\"text-xs text-gray-600 font-medium\">متصل</p>\n              </div>\n              <p className=\"text-xs text-gray-500\">© 2024 مكتب لارين العلمي</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Sidebar - Only visible on mobile when open */}\n      <div\n        id=\"mobile-sidebar\"\n        className=\"md:hidden\"\n        style={{\n          position: 'fixed',\n          top: 0,\n          right: isOpen ? '0px' : '-300px',\n          height: '100vh',\n          width: '280px',\n          backgroundColor: 'white',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          borderLeft: '1px solid #e5e7eb',\n          transition: 'right 0.3s ease-in-out',\n          zIndex: 9999,\n          overflow: 'auto'\n        }}\n      >\n        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>\n          {/* Header */}\n          <div style={{\n            padding: '16px',\n            background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n            borderRadius: '12px',\n            color: 'white',\n            marginBottom: '24px',\n            textAlign: 'center'\n          }}>\n            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>نظام الصيدلية</h1>\n            <p style={{ fontSize: '14px', margin: '8px 0 0 0', opacity: 0.9 }}>مكتب لارين العلمي</p>\n          </div>\n\n          {/* Navigation Menu */}\n          <div style={{ marginBottom: '24px' }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: '#374151',\n              borderBottom: '2px solid #e5e7eb',\n              paddingBottom: '8px'\n            }}>القائمة الرئيسية</h3>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              {visibleMenuItems.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <a\n                    key={item.href}\n                    href={item.href}\n                    onClick={onClose}\n                    style={{\n                      padding: '12px 16px',\n                      backgroundColor: isActive ? '#3b82f6' : '#f8fafc',\n                      color: isActive ? 'white' : '#374151',\n                      textDecoration: 'none',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '12px',\n                      border: isActive ? 'none' : '1px solid #e5e7eb',\n                      transition: 'all 0.2s ease',\n                      fontSize: '14px',\n                      fontWeight: isActive ? 'bold' : 'normal'\n                    }}\n                  >\n                    <Icon style={{ width: '20px', height: '20px' }} />\n                    {item.title}\n                  </a>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Close Button */}\n          <div style={{ marginTop: 'auto', paddingTop: '16px' }}>\n            <button\n              onClick={onClose}\n              style={{\n                width: '100%',\n                padding: '12px',\n                backgroundColor: '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                fontSize: '16px',\n                fontWeight: 'bold',\n                cursor: 'pointer',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}\n              onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}\n            >\n              إغلاق القائمة\n            </button>\n          </div>\n\n        </div>\n      </div>\n\n      {/* Overlay */}\n      {isOpen && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 9998\n          }}\n          onClick={onClose}\n        />\n      )}\n    </>\n  )\n}\n\n// تصدير دالة للتحكم في القائمة من مكونات أخرى\nexport const useSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAkCA,MAAM,eAAe,CAAC,cAAiC;QACrD;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sNAAA,CAAA,eAAY;YAClB,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gMAAA,CAAA,MAAG;YACT,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sNAAA,CAAA,eAAY;YAClB,YAAY;QACd;KACD;AAOc,SAAS,QAAQ,EAAE,SAAS,KAAK,EAAE,OAAO,EAAgB,GAAG,CAAC,CAAC;IAC5E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAIpD,MAAM,YAAY,aAAa;IAE/B,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,MAAM,aAAa,SAAS,cAAc,CAAC;YAE3C,IAAI,WAAW,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KACzC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5D,IAAI,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,oCAAoC;IACpC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,KAAK,6CAA6C;;QAC/E,OAAO,cAAc,KAAK,UAAU;IACtC;IAEA,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC;gCACrB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,+EAA+E,EACzF,WACI,sEACA,iGACJ;;sDAEF,8OAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,IAAI;;;;;;sDAC1D,8OAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;wCACxC,0BACC,8OAAC;4CAAI,WAAU;;;;;;;mCAXZ,KAAK,IAAI;;;;;4BAepB;;;;;;sCAGF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBACC,IAAG;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,OAAO,SAAS,QAAQ;oBACxB,QAAQ;oBACR,OAAO;oBACP,iBAAiB;oBACjB,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,UAAU;gBACZ;0BAEA,cAAA,8OAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,QAAQ;wBAAQ,WAAW;oBAAO;;sCAE/D,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,OAAO;gCACP,cAAc;gCACd,WAAW;4BACb;;8CACE,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAQ,YAAY;wCAAQ,QAAQ;oCAAE;8CAAG;;;;;;8CAChE,8OAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAQ,QAAQ;wCAAa,SAAS;oCAAI;8CAAG;;;;;;;;;;;;sCAIrE,8OAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CACjC,8OAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,cAAc;wCACd,eAAe;oCACjB;8CAAG;;;;;;8CAEH,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;8CAChE,iBAAiB,GAAG,CAAC,CAAC;wCACrB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wCAEvC,qBACE,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS;4CACT,OAAO;gDACL,SAAS;gDACT,iBAAiB,WAAW,YAAY;gDACxC,OAAO,WAAW,UAAU;gDAC5B,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,QAAQ,WAAW,SAAS;gDAC5B,YAAY;gDACZ,UAAU;gDACV,YAAY,WAAW,SAAS;4CAClC;;8DAEA,8OAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAQ,QAAQ;oDAAO;;;;;;gDAC5C,KAAK,KAAK;;2CAnBN,KAAK,IAAI;;;;;oCAsBpB;;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAQ,YAAY;4BAAO;sCAClD,cAAA,8OAAC;gCACC,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,UAAU;oCACV,YAAY;oCACZ,QAAQ;oCACR,YAAY;gCACd;gCACA,aAAa,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;gCACrD,YAAY,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;0CACrD;;;;;;;;;;;;;;;;;;;;;;YASN,wBACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;;;AAKnB;AAGO,MAAM,aAAa;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO;QAAE;QAAQ;QAAW,eAAe,IAAM,UAAU,CAAC;IAAQ;AACtE", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  X,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign\n} from 'lucide-react'\n\nexport default function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll\n  } = useNotifications()\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const filteredNotifications = activeTab === 'unread' \n    ? notifications.filter(n => !n.isRead)\n    : notifications\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-4 w-4 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-4 w-4 text-blue-500\" />\n    \n    // أيقونات حسب الفئة\n    if (category === 'inventory') return <Package className=\"h-4 w-4 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-4 w-4 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-4 w-4 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-4 w-4 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-4 w-4 text-gray-500\" />\n    \n    return <Bell className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'border-r-4 border-red-500 bg-red-50'\n      case 'high': return 'border-r-4 border-orange-500 bg-orange-50'\n      case 'medium': return 'border-r-4 border-yellow-500 bg-yellow-50'\n      case 'low': return 'border-r-4 border-blue-500 bg-blue-50'\n      default: return 'border-r-4 border-gray-500 bg-gray-50'\n    }\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n      setIsOpen(false)\n    }\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Bell className=\"h-5 w-5\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">التنبيهات</h3>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n            \n            {/* Tabs */}\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('all')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'all'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                الكل ({notifications.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('unread')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'unread'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                غير مقروءة ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Actions */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800\"\n                    >\n                      <CheckCheck className=\"h-3 w-3\" />\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n                <button\n                  onClick={clearAll}\n                  className=\"flex items-center gap-1 text-xs text-red-600 hover:text-red-800\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  مسح الكل\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500\">\n                  {activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                      !notification.isRead ? getPriorityColor(notification.priority) : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                              {notification.message}\n                            </p>\n                            \n                            {/* Action Button */}\n                            {notification.actionUrl && (\n                              <button className=\"inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2\">\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض التفاصيل'}\n                              </button>\n                            )}\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-1 mr-2\">\n                            {!notification.isRead && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation()\n                                  markAsRead(notification.id)\n                                }}\n                                className=\"p-1 text-gray-400 hover:text-blue-600\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                removeNotification(notification.id)\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-600\"\n                              title=\"حذف\"\n                            >\n                              <X className=\"h-3 w-3\" />\n                            </button>\n                          </div>\n                        </div>\n                        \n                        {/* Time */}\n                        <div className=\"flex items-center gap-1 mt-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(notification.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  router.push('/notifications')\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع التنبيهات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,wBAAwB,cAAc,WACxC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IACnC;IAEJ,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,oBAAoB;QACpB,IAAI,aAAa,aAAa,qBAAO,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC;QAE3D,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC;QAEtD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC;IAChC;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;YAClC,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,mBACb,8OAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,6DAA6D,EACvE,cAAc,QACV,8BACA,qCACJ;;4CACH;4CACQ,cAAc,MAAM;4CAAC;;;;;;;kDAE9B,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,6DAA6D,EACvE,cAAc,WACV,8BACA,qCACJ;;4CACH;4CACc;4CAAY;;;;;;;;;;;;;;;;;;;oBAM9B,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,cAAc,mBACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAQtC,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CACV,cAAc,WAAW,+BAA+B;;;;;;;;;;;iDAI7D,8OAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC;oCAEC,WAAW,CAAC,sDAAsD,EAChE,CAAC,aAAa,MAAM,GAAG,iBAAiB,aAAa,QAAQ,IAAI,IACjE;oCACF,SAAS,IAAM,wBAAwB;8CAEvC,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,MAAM,GAAG,kBAAkB,iBACzC;kFACC,aAAa,KAAK;;;;;;kFAErB,8OAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;oEAItB,aAAa,SAAS,kBACrB,8OAAC;wEAAO,WAAU;;0FAChB,8OAAC,sNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,aAAa,WAAW,IAAI;;;;;;;;;;;;;0EAMnC,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,WAAW,aAAa,EAAE;wEAC5B;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAGrB,8OAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;;;;;;;mCAhEtC,aAAa,EAAE;;;;;;;;;;;;;;;oBA2E7B,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,OAAO,IAAI,CAAC;gCACZ,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileMenuButton.tsx"], "sourcesContent": ["'use client'\n\nimport { Menu, X } from 'lucide-react'\n\ninterface MobileMenuButtonProps {\n  isOpen: boolean\n  onClick: () => void\n}\n\nexport default function MobileMenuButton({ isOpen, onClick }: MobileMenuButtonProps) {\n  const handleClick = () => {\n    onClick()\n  }\n\n  return (\n    <button\n      id=\"mobile-menu-button\"\n      onClick={handleClick}\n      className=\"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center\"\n      aria-label={isOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n    >\n      {isOpen ? (\n        <X className=\"h-6 w-6\" />\n      ) : (\n        <Menu className=\"h-6 w-6\" />\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AASe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAyB;IACjF,MAAM,cAAc;QAClB;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,SAAS;QACT,WAAU;QACV,cAAY,SAAS,kBAAkB;kBAEtC,uBACC,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;iCAEb,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport NotificationDropdown from './NotificationDropdown'\nimport MobileMenuButton from './MobileMenuButton'\nimport {\n  Search,\n  User,\n  LogOut,\n  Settings,\n  Shield,\n  ChevronDown,\n  Activity\n} from 'lucide-react'\n\ninterface HeaderProps {\n  onMobileMenuToggle?: () => void\n  isMobileMenuOpen?: boolean\n}\n\nexport default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, logout } = useAuth()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    const roleNames: Record<string, string> = {\n      admin: 'مدير النظام',\n      manager: 'مدير',\n      pharmacist: 'صيدلي',\n      cashier: 'كاشير',\n      viewer: 'مشاهد'\n    }\n    return roleNames[role] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors: Record<string, string> = {\n      admin: 'bg-red-600',\n      manager: 'bg-purple-600',\n      pharmacist: 'bg-blue-600',\n      cashier: 'bg-green-600',\n      viewer: 'bg-gray-600'\n    }\n    return colors[role] || 'bg-gray-600'\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 md:right-64 z-40 header-mobile\">\n      <div className=\"flex items-center justify-between h-full px-3 md:px-6\">\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Mobile Menu Button - Only visible on mobile */}\n          <div className=\"md:hidden\">\n            <MobileMenuButton\n              isOpen={isMobileMenuOpen}\n              onClick={onMobileMenuToggle || (() => console.log('⚠️ No onMobileMenuToggle function provided!'))}\n            />\n          </div>\n\n          {/* Mobile Search - Hidden on very small screens */}\n          <div className=\"relative hidden sm:hidden md:block\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث...\"\n              className=\"pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile\"\n            />\n          </div>\n\n          {/* Mobile Search Icon - Visible only on small screens */}\n          <button className=\"md:hidden p-2 hover:bg-gray-100 rounded-lg\">\n            <Search className=\"h-5 w-5 text-gray-600\" />\n          </button>\n        </div>\n\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Notifications */}\n          <NotificationDropdown />\n\n          {/* User Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]\"\n            >\n              {/* Hide user info text on mobile */}\n              <div className=\"text-right hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-800\">{user?.full_name || 'مستخدم'}</p>\n                <p className=\"text-xs text-gray-500\">{user ? getRoleDisplayName(user.role) : ''}</p>\n              </div>\n              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <ChevronDown className=\"h-4 w-4 text-gray-400 hidden md:block\" />\n            </button>\n\n            {/* Dropdown Menu */}\n            {showUserMenu && (\n              <div className=\"absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile\">\n                {/* User Info */}\n                <div className=\"px-4 py-3 border-b border-gray-100\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                      <p className=\"text-xs text-gray-500\">@{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :\n                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :\n                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :\n                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      <Shield className=\"h-3 w-3 ml-1\" />\n                      {user ? getRoleDisplayName(user.role) : ''}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/profile')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    الملف الشخصي\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/activity-log')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Activity className=\"h-4 w-4\" />\n                    سجل النشاطات\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/settings')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    الإعدادات\n                  </button>\n                </div>\n\n                {/* Logout */}\n                <div className=\"border-t border-gray-100 pt-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      handleLogout()\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAsBe,SAAS,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,KAAK,EAAe,GAAG,CAAC,CAAC;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAoC;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,UAAgB;oCACf,QAAQ;oCACR,SAAS,sBAAsB,CAAC,IAAM,QAAQ,GAAG,CAAC,8CAA8C;;;;;;;;;;;0CAKpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0IAAA,CAAA,UAAoB;;;;;0CAGrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,MAAM,aAAa;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;0DAE/E,8OAAC;gDAAI,WAAW,GAAG,OAAO,aAAa,KAAK,IAAI,IAAI,cAAc,iBAAiB,CAAC;0DAClF,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,GAAG,OAAO,aAAa,KAAK,IAAI,IAAI,cAAc,iBAAiB,CAAC;0EAClF,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAqC,MAAM;;;;;;kFACxD,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAE,MAAM;;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;kFAAyB,MAAM;;;;;;;;;;;;;;;;;;kEAGhD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAW,CAAC,oEAAoE,EACpF,MAAM,SAAS,UAAU,4BACzB,MAAM,SAAS,YAAY,kCAC3B,MAAM,SAAS,eAAe,8BAC9B,MAAM,SAAS,YAAY,gCAC3B,6BACA;;8EACA,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;0DAM9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAI9B,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIlC,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB;oDACF;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,8BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <AlertTriangle className=\"h-12 w-12 text-red-500\" />\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 mb-2\">حدث خطأ غير متوقع</h1>\n            \n            <p className=\"text-gray-600 mb-4\">\n              عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.\n            </p>\n            \n            {this.state.error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left\">\n                <p className=\"text-sm text-red-800 font-mono\">\n                  {this.state.error.message}\n                </p>\n              </div>\n            )}\n            \n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={this.retry}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                إعادة المحاولة\n              </button>\n              \n              <button\n                onClick={() => window.location.reload()}\n                className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\n              >\n                إعادة تحميل الصفحة\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  تفاصيل الخطأ (للمطورين)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                  {this.state.error?.stack}\n                  {'\\n\\n'}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for functional components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null)\n\n  const resetError = React.useCallback(() => {\n    setError(null)\n  }, [])\n\n  const handleError = React.useCallback((error: Error) => {\n    console.error('Error caught by useErrorHandler:', error)\n    setError(error)\n  }, [])\n\n  React.useEffect(() => {\n    if (error) {\n      throw error\n    }\n  }, [error])\n\n  return { handleError, resetError }\n}\n\n// Safe component wrapper\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAHA;;;;AAgBA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAEA,QAAQ;QACN,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAIjC,IAAI,CAAC,KAAK,CAAC,KAAK,kBACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAI,CAAC,KAAK;oCACnB,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,8OAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC7D,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;;wCACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;wCAClB;wCACA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QACnC,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACrC,QAAQ,KAAK,CAAC,oCAAoC;QAClD,SAAS;IACX,GAAG,EAAE;IAEL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,OAAO;YACT,MAAM;QACR;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC;AAGO,SAAS,kBACd,SAAiC,EACjC,QAAmE;IAEnE,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAc,UAAU;sBACvB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ToastNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  X,\n  CheckCircle,\n  AlertTriangle,\n  Info,\n  XCircle,\n  ExternalLink\n} from 'lucide-react'\n\ninterface ToastNotification {\n  id: string\n  type: 'success' | 'warning' | 'error' | 'info'\n  title: string\n  message: string\n  actionUrl?: string\n  actionLabel?: string\n  duration?: number\n}\n\nexport default function ToastNotifications() {\n  const [toasts, setToasts] = useState<ToastNotification[]>([])\n  const { notifications } = useNotifications()\n\n  // مراقبة التنبيهات الجديدة وعرضها كـ Toast\n  useEffect(() => {\n    const latestNotification = notifications[0]\n    if (latestNotification && !latestNotification.isRead) {\n      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)\n      const notificationTime = new Date(latestNotification.createdAt).getTime()\n      const now = new Date().getTime()\n      const diffInMinutes = (now - notificationTime) / (1000 * 60)\n      \n      if (diffInMinutes < 1) {\n        showToast({\n          id: latestNotification.id,\n          type: latestNotification.type,\n          title: latestNotification.title,\n          message: latestNotification.message,\n          actionUrl: latestNotification.actionUrl,\n          actionLabel: latestNotification.actionLabel,\n          duration: getDurationByPriority(latestNotification.priority)\n        })\n      }\n    }\n  }, [notifications])\n\n  const getDurationByPriority = (priority: string): number => {\n    switch (priority) {\n      case 'critical': return 10000 // 10 ثواني\n      case 'high': return 7000     // 7 ثواني\n      case 'medium': return 5000   // 5 ثواني\n      case 'low': return 3000      // 3 ثواني\n      default: return 5000\n    }\n  }\n\n  const showToast = (toast: ToastNotification) => {\n    // تجنب التكرار\n    if (toasts.find(t => t.id === toast.id)) return\n\n    setToasts(prev => [...prev, toast])\n\n    // إزالة التنبيه تلقائياً بعد المدة المحددة\n    setTimeout(() => {\n      removeToast(toast.id)\n    }, toast.duration || 5000)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const getToastIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getToastStyles = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-20 left-4 md:left-72 z-[60] space-y-3 max-w-sm\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className={`w-full shadow-lg rounded-lg border p-3 md:p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}\n        >\n          <div className=\"flex items-start gap-2 md:gap-3\">\n            {/* Icon */}\n            <div className=\"flex-shrink-0 mt-0.5\">\n              {getToastIcon(toast.type)}\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 min-w-0\">\n              <h4 className=\"text-xs md:text-sm font-medium mb-1\">\n                {toast.title}\n              </h4>\n              <p className=\"text-xs md:text-sm opacity-90 line-clamp-2\">\n                {toast.message}\n              </p>\n\n              {/* Action Button */}\n              {toast.actionUrl && (\n                <a\n                  href={toast.actionUrl}\n                  className=\"inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline min-h-[32px]\"\n                >\n                  <ExternalLink className=\"h-3 w-3\" />\n                  {toast.actionLabel || 'عرض التفاصيل'}\n                </a>\n              )}\n            </div>\n\n            {/* Close Button */}\n            <button\n              onClick={() => removeToast(toast.id)}\n              className=\"flex-shrink-0 p-2 hover:bg-black hover:bg-opacity-10 rounded min-h-[44px] min-w-[44px] flex items-center justify-center\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// إضافة الأنيميشن إلى CSS\nconst toastStyles = `\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`\n\n// إضافة الأنيميشن إلى الصفحة\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style')\n  styleElement.textContent = toastStyles\n  document.head.appendChild(styleElement)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAuBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,aAAa,CAAC,EAAE;QAC3C,IAAI,sBAAsB,CAAC,mBAAmB,MAAM,EAAE;YACpD,uDAAuD;YACvD,MAAM,mBAAmB,IAAI,KAAK,mBAAmB,SAAS,EAAE,OAAO;YACvE,MAAM,MAAM,IAAI,OAAO,OAAO;YAC9B,MAAM,gBAAgB,CAAC,MAAM,gBAAgB,IAAI,CAAC,OAAO,EAAE;YAE3D,IAAI,gBAAgB,GAAG;gBACrB,UAAU;oBACR,IAAI,mBAAmB,EAAE;oBACzB,MAAM,mBAAmB,IAAI;oBAC7B,OAAO,mBAAmB,KAAK;oBAC/B,SAAS,mBAAmB,OAAO;oBACnC,WAAW,mBAAmB,SAAS;oBACvC,aAAa,mBAAmB,WAAW;oBAC3C,UAAU,sBAAsB,mBAAmB,QAAQ;gBAC7D;YACF;QACF;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAY,OAAO,MAAM,WAAW;;YACzC,KAAK;gBAAQ,OAAO,KAAS,UAAU;;YACvC,KAAK;gBAAU,OAAO,KAAO,UAAU;;YACvC,KAAK;gBAAO,OAAO,KAAU,UAAU;;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,eAAe;QACf,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;QAEzC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,2CAA2C;QAC3C,WAAW;YACT,YAAY,MAAM,EAAE;QACtB,GAAG,MAAM,QAAQ,IAAI;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAEC,WAAW,CAAC,0GAA0G,EAAE,eAAe,MAAM,IAAI,GAAG;0BAEpJ,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,IAAI;;;;;;sCAI1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;gCAIf,MAAM,SAAS,kBACd,8OAAC;oCACC,MAAM,MAAM,SAAS;oCACrB,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,MAAM,WAAW,IAAI;;;;;;;;;;;;;sCAM5B,8OAAC;4BACC,SAAS,IAAM,YAAY,MAAM,EAAE;4BACnC,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;eAnCZ,MAAM,EAAE;;;;;;;;;;AA0CvB;AAEA,0BAA0B;AAC1B,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;AAsBrB,CAAC;AAED,6BAA6B;AAC7B,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileOptimizer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\nexport default function MobileOptimizer() {\n  useEffect(() => {\n    // Detect mobile device\n    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    if (isMobile || isTouch) {\n      // Add mobile-specific classes to body\n      document.body.classList.add('mobile-device', 'touch-device')\n      \n      // Prevent zoom on input focus (iOS)\n      const viewport = document.querySelector('meta[name=viewport]')\n      if (viewport) {\n        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover')\n      }\n\n      // Add touch-friendly classes to interactive elements\n      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')\n      interactiveElements.forEach(element => {\n        element.classList.add('touch-friendly')\n      })\n\n      // Optimize scrolling\n      document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')\n      document.body.style.setProperty('-webkit-overflow-scrolling', 'touch')\n\n      // Prevent bounce scrolling on iOS\n      document.addEventListener('touchmove', (e) => {\n        if (e.target === document.body) {\n          e.preventDefault()\n        }\n      }, { passive: false })\n\n      // Add safe area support\n      if (CSS.supports('padding: max(0px)')) {\n        document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')\n        document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')\n        document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')\n        document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')\n      }\n\n      // Optimize performance for mobile\n      const optimizeElement = (element: Element) => {\n        if (element instanceof HTMLElement) {\n          element.style.setProperty('transform', 'translateZ(0)')\n          element.style.setProperty('will-change', 'transform')\n        }\n      }\n\n      // Apply optimizations to animated elements\n      const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift')\n      animatedElements.forEach(optimizeElement)\n\n      // Handle orientation change\n      const handleOrientationChange = () => {\n        // Force repaint after orientation change\n        setTimeout(() => {\n          window.scrollTo(0, window.scrollY)\n        }, 100)\n      }\n\n      window.addEventListener('orientationchange', handleOrientationChange)\n      \n      // Cleanup\n      return () => {\n        window.removeEventListener('orientationchange', handleOrientationChange)\n      }\n    }\n  }, [])\n\n  useEffect(() => {\n    // Add PWA install prompt handling\n    let deferredPrompt: any\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      deferredPrompt = e\n      \n      // Show custom install button or banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'block'\n      }\n    }\n\n    const handleAppInstalled = () => {\n      console.log('PWA was installed')\n      deferredPrompt = null\n      \n      // Hide install banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'none'\n      }\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  return null // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,MAAM,WAAW,iEAAiE,IAAI,CAAC,UAAU,SAAS;QAC1G,MAAM,UAAU,kBAAkB,UAAU,UAAU,cAAc,GAAG;QAEvE,IAAI,YAAY,SAAS;YACvB,sCAAsC;YACtC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB;YAE7C,oCAAoC;YACpC,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,IAAI,UAAU;gBACZ,SAAS,YAAY,CAAC,WAAW;YACnC;YAEA,qDAAqD;YACrD,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;YACtD,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;YAEA,qBAAqB;YACrB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;YACzE,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;YAE9D,kCAAkC;YAClC,SAAS,gBAAgB,CAAC,aAAa,CAAC;gBACtC,IAAI,EAAE,MAAM,KAAK,SAAS,IAAI,EAAE;oBAC9B,EAAE,cAAc;gBAClB;YACF,GAAG;gBAAE,SAAS;YAAM;YAEpB,wBAAwB;YACxB,IAAI,IAAI,QAAQ,CAAC,sBAAsB;gBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB;gBACpE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,4BAA4B;gBACvE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B;gBACrE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,2BAA2B;YACxE;YAEA,kCAAkC;YAClC,MAAM,kBAAkB,CAAC;gBACvB,IAAI,mBAAmB,aAAa;oBAClC,QAAQ,KAAK,CAAC,WAAW,CAAC,aAAa;oBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,eAAe;gBAC3C;YACF;YAEA,2CAA2C;YAC3C,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC;YAEzB,4BAA4B;YAC5B,MAAM,0BAA0B;gBAC9B,yCAAyC;gBACzC,WAAW;oBACT,OAAO,QAAQ,CAAC,GAAG,OAAO,OAAO;gBACnC,GAAG;YACL;YAEA,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C,UAAU;YACV,OAAO;gBACL,OAAO,mBAAmB,CAAC,qBAAqB;YAClD;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI;QAEJ,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,iBAAiB;YAEjB,uCAAuC;YACvC,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,IAAI,eAAe;gBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;YAChC;QACF;QAEA,MAAM,qBAAqB;YACzB,QAAQ,GAAG,CAAC;YACZ,iBAAiB;YAEjB,sBAAsB;YACtB,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,IAAI,eAAe;gBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,OAAO,KAAK,yCAAyC;;AACvD", "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/PWAInstallBanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Smartphone } from 'lucide-react'\n\nexport default function PWAInstallBanner() {\n  const [showBanner, setShowBanner] = useState(false)\n  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)\n\n  useEffect(() => {\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e)\n      setShowBanner(true)\n    }\n\n    const handleAppInstalled = () => {\n      setShowBanner(false)\n      setDeferredPrompt(null)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    // Check if app is already installed\n    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {\n      setShowBanner(false)\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  const handleInstallClick = async () => {\n    if (deferredPrompt) {\n      deferredPrompt.prompt()\n      const { outcome } = await deferredPrompt.userChoice\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt')\n      } else {\n        console.log('User dismissed the install prompt')\n      }\n      \n      setDeferredPrompt(null)\n      setShowBanner(false)\n    }\n  }\n\n  const handleDismiss = () => {\n    setShowBanner(false)\n    // Remember user dismissed the banner\n    localStorage.setItem('pwa-install-dismissed', 'true')\n  }\n\n  // Don't show if user previously dismissed\n  useEffect(() => {\n    const dismissed = localStorage.getItem('pwa-install-dismissed')\n    if (dismissed === 'true') {\n      setShowBanner(false)\n    }\n  }, [])\n\n  if (!showBanner) return null\n\n  return (\n    <div \n      id=\"pwa-install-banner\"\n      className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card\"\n    >\n      <div className=\"flex items-start gap-3\">\n        <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n          <Smartphone className=\"h-5 w-5\" />\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-sm md:text-base mb-1\">\n            تثبيت التطبيق\n          </h3>\n          <p className=\"text-blue-100 text-xs md:text-sm mb-3\">\n            ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت\n          </p>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleInstallClick}\n              className=\"bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1\"\n            >\n              <Download className=\"h-3 w-3 md:h-4 md:w-4\" />\n              تثبيت\n            </button>\n            <button\n              onClick={handleDismiss}\n              className=\"text-blue-100 hover:text-white transition-colors\"\n            >\n              <X className=\"h-4 w-4 md:h-5 md:w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,kBAAkB;YAClB,cAAc;QAChB;QAEA,MAAM,qBAAqB;YACzB,cAAc;YACd,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,oCAAoC;QACpC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;YAChF,cAAc;QAChB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,eAAe,MAAM;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,qCAAqC;QACrC,aAAa,OAAO,CAAC,yBAAyB;IAChD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,cAAc,QAAQ;YACxB,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;8BAExB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGhD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ScreenSizeIndicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Monitor, Tablet, Smartphone, Laptop } from 'lucide-react'\n\nexport default function ScreenSizeIndicator() {\n  const [screenInfo, setScreenInfo] = useState({\n    width: 0,\n    height: 0,\n    breakpoint: '',\n    device: '',\n    orientation: ''\n  })\n\n  useEffect(() => {\n    const updateScreenInfo = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n      \n      let breakpoint = ''\n      let device = ''\n      \n      if (width < 640) {\n        breakpoint = 'sm (< 640px)'\n        device = 'هاتف صغير'\n      } else if (width < 768) {\n        breakpoint = 'md (640px - 768px)'\n        device = 'هاتف كبير'\n      } else if (width < 1024) {\n        breakpoint = 'lg (768px - 1024px)'\n        device = 'تابلت'\n      } else if (width < 1280) {\n        breakpoint = 'xl (1024px - 1280px)'\n        device = 'لابتوب'\n      } else {\n        breakpoint = '2xl (> 1280px)'\n        device = 'كمبيوتر مكتبي'\n      }\n\n      const orientation = width > height ? 'أفقي' : 'عمودي'\n\n      setScreenInfo({\n        width,\n        height,\n        breakpoint,\n        device,\n        orientation\n      })\n    }\n\n    updateScreenInfo()\n    window.addEventListener('resize', updateScreenInfo)\n    window.addEventListener('orientationchange', updateScreenInfo)\n\n    return () => {\n      window.removeEventListener('resize', updateScreenInfo)\n      window.removeEventListener('orientationchange', updateScreenInfo)\n    }\n  }, [])\n\n  const getIcon = () => {\n    if (screenInfo.width < 768) return Smartphone\n    if (screenInfo.width < 1024) return Tablet\n    if (screenInfo.width < 1280) return Laptop\n    return Monitor\n  }\n\n  const Icon = getIcon()\n\n  return (\n    <div className=\"fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block\">\n      <div className=\"flex items-center gap-2 mb-1\">\n        <Icon className=\"h-4 w-4\" />\n        <span className=\"font-medium\">{screenInfo.device}</span>\n      </div>\n      <div className=\"space-y-1\">\n        <div>{screenInfo.width} × {screenInfo.height}</div>\n        <div>{screenInfo.breakpoint}</div>\n        <div>{screenInfo.orientation}</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,IAAI,aAAa;YACjB,IAAI,SAAS;YAEb,IAAI,QAAQ,KAAK;gBACf,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,KAAK;gBACtB,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,MAAM;gBACvB,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,MAAM;gBACvB,aAAa;gBACb,SAAS;YACX,OAAO;gBACL,aAAa;gBACb,SAAS;YACX;YAEA,MAAM,cAAc,QAAQ,SAAS,SAAS;YAE9C,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,qBAAqB;QAE7C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI,WAAW,KAAK,GAAG,KAAK,OAAO,8MAAA,CAAA,aAAU;QAC7C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,sMAAA,CAAA,SAAM;QAC1C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,sMAAA,CAAA,SAAM;QAC1C,OAAO,wMAAA,CAAA,UAAO;IAChB;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAe,WAAW,MAAM;;;;;;;;;;;;0BAElD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAK,WAAW,KAAK;4BAAC;4BAAI,WAAW,MAAM;;;;;;;kCAC5C,8OAAC;kCAAK,WAAW,UAAU;;;;;;kCAC3B,8OAAC;kCAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport Header from './Header'\nimport ErrorBoundary from './ErrorBoundary'\nimport ToastNotifications from './ToastNotifications'\nimport MobileOptimizer from './MobileOptimizer'\nimport PWAInstallBanner from './PWAInstallBanner'\nimport ScreenSizeIndicator from './ScreenSizeIndicator'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <MobileOptimizer />\n      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n      <Header\n        onMobileMenuToggle={toggleMobileMenu}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n      <main className={`\n        mr-0 md:mr-64\n        mt-16\n        p-3 md:p-6\n        main-content-mobile\n        min-h-screen\n        w-full\n        max-w-full\n        overflow-x-hidden\n        safe-area-inset-bottom\n      `}>\n        <ErrorBoundary>\n          <div className=\"max-w-full overflow-x-auto container\">\n            {children}\n          </div>\n        </ErrorBoundary>\n      </main>\n      <ToastNotifications />\n      <PWAInstallBanner />\n      <ScreenSizeIndicator />\n\n      {/* Mobile overlay when sidebar is open */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAee,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,6HAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;0BACtE,8OAAC,4HAAA,CAAA,UAAM;gBACL,oBAAoB;gBACpB,kBAAkB;;;;;;0BAEpB,8OAAC;gBAAK,WAAW,CAAC;;;;;;;;;;MAUlB,CAAC;0BACC,cAAA,8OAAC,mIAAA,CAAA,UAAa;8BACZ,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;0BAIP,8OAAC,wIAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,sIAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,yIAAA,CAAA,UAAmB;;;;;YAGnB,kCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 2599, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/PrintTemplate.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport {\n  Printer, Download, X, FileText, Calendar, User, Phone, MapPin, Hash, CreditCard,\n  CheckCircle, Clock, AlertCircle, TrendingUp, ShoppingCart, Target, Layers,\n  BarChart3\n} from 'lucide-react'\nimport '../styles/classic-print.css'\n\n// Classic Print Styles - Similar to traditional invoice format\nconst printStyles = `\n  @media print {\n    body {\n      margin: 0;\n      padding: 0;\n      font-family: 'Arial', sans-serif;\n      font-size: 12px;\n      line-height: 1.2;\n      color: #000;\n      background: white;\n    }\n\n    .print-content {\n      max-width: none;\n      margin: 0;\n      padding: 10mm;\n      box-shadow: none;\n      border: none;\n      background: white;\n    }\n\n    .no-print { display: none !important; }\n\n    /* Classic table styles */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin-bottom: 5px;\n      border: 2px solid #000;\n    }\n\n    th, td {\n      border: 1px solid #000;\n      padding: 4px 6px;\n      text-align: center;\n      vertical-align: middle;\n      font-size: 11px;\n      height: 25px;\n    }\n\n    th {\n      background-color: #f0f0f0 !important;\n      font-weight: bold;\n      text-align: center;\n    }\n\n    /* Header border styling */\n    .border-2 {\n      border: 2px solid #000 !important;\n    }\n\n    .border {\n      border: 1px solid #000 !important;\n    }\n\n    .border-black {\n      border-color: #000 !important;\n    }\n\n    .border-r-2 {\n      border-right: 2px solid #000 !important;\n    }\n\n    .border-b-2 {\n      border-bottom: 2px solid #000 !important;\n    }\n\n    .border-t-2 {\n      border-top: 2px solid #000 !important;\n    }\n\n    /* Grid layout for print */\n    .grid {\n      display: table;\n      width: 100%;\n      table-layout: fixed;\n    }\n\n    .grid-cols-3 > div {\n      display: table-cell;\n      width: 33.333%;\n      vertical-align: top;\n    }\n\n    .grid-cols-4 > div {\n      display: table-cell;\n      width: 25%;\n      vertical-align: top;\n    }\n\n    .grid-cols-2 > div {\n      display: table-cell;\n      width: 50%;\n      vertical-align: top;\n    }\n\n    /* Text alignment */\n    .text-center { text-align: center !important; }\n    .text-right { text-align: right !important; }\n    .text-left { text-align: left !important; }\n\n    /* Font weights and sizes */\n    .font-bold { font-weight: bold !important; }\n    .text-xl { font-size: 18px !important; }\n    .text-lg { font-size: 16px !important; }\n    .text-md { font-size: 14px !important; }\n    .text-sm { font-size: 12px !important; }\n    .text-xs { font-size: 10px !important; }\n\n    /* Hide modern styling elements */\n    .rounded, .rounded-lg, .rounded-full, .shadow, .shadow-sm {\n      border-radius: 0 !important;\n      box-shadow: none !important;\n    }\n\n    /* Color adjustments for print */\n    .text-blue-600, .bg-blue-100 { color: #000 !important; background: transparent !important; }\n    .text-green-600, .bg-green-100 { color: #000 !important; background: transparent !important; }\n    .text-red-600, .bg-red-100 { color: #000 !important; background: transparent !important; }\n    .text-yellow-600, .bg-yellow-100 { color: #000 !important; background: transparent !important; }\n    .text-gray-600 { color: #333 !important; }\n\n    /* Background colors */\n    .bg-gray-100 { background-color: #f5f5f5 !important; }\n\n    /* Hide interactive elements */\n    button, .btn, .no-print, .print-button {\n      display: none !important;\n    }\n\n    /* Signature circle */\n    .rounded-full {\n      border: 2px solid #000 !important;\n      border-radius: 50% !important;\n    }\n\n    /* Font family for numbers */\n    .font-mono { font-family: 'Courier New', monospace !important; }\n  }\n`\n\ninterface PrintSettings {\n  companyName: string\n  companyAddress: string\n  companyPhone: string\n  companyEmail: string\n  showHeader: boolean\n  showFooter: boolean\n  showLogo: boolean\n  showWatermark: boolean\n  watermark: string\n  footerText: string\n  fontSize: 'small' | 'medium' | 'large'\n  includeDate: boolean\n  includePageNumbers: boolean\n  includeQRCode: boolean\n  headerColor: string\n  accentColor: string\n  textColor: string\n  backgroundColor: string\n}\n\ninterface PrintTemplateProps {\n  children: React.ReactNode\n  title: string\n  settings?: Partial<PrintSettings>\n  onClose?: () => void\n  onPrint?: () => void\n  onDownload?: () => void\n}\n\nconst defaultSettings: PrintSettings = {\n  companyName: 'صيدلية الشفاء',\n  companyAddress: 'بغداد - العراق',\n  companyPhone: '+964 ************',\n  companyEmail: '<EMAIL>',\n  showHeader: true,\n  showFooter: true,\n  showLogo: true,\n  showWatermark: false,\n  watermark: 'نسخة أصلية',\n  footerText: 'شكراً لثقتكم بنا - نتمنى لكم الشفاء العاجل',\n  fontSize: 'medium',\n  includeDate: true,\n  includePageNumbers: true,\n  includeQRCode: false,\n  headerColor: '#1f2937',\n  accentColor: '#3b82f6',\n  textColor: '#374151',\n  backgroundColor: '#ffffff'\n}\n\nexport default function PrintTemplate({ \n  children, \n  title, \n  settings, \n  onClose, \n  onPrint, \n  onDownload \n}: PrintTemplateProps) {\n  const printSettings = { ...defaultSettings, ...settings }\n\n  const handlePrint = () => {\n    window.print()\n    onPrint?.()\n  }\n\n  const handleDownload = () => {\n    // تنفيذ تحميل PDF\n    onDownload?.()\n  }\n\n  return (\n    <>\n      <style dangerouslySetInnerHTML={{ __html: printStyles }} />\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n          {/* Header Controls */}\n          <div className=\"flex items-center justify-between p-4 border-b bg-gray-50\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">معاينة الطباعة</h2>\n            <div className=\"flex items-center gap-2\">\n              <button\n                onClick={handlePrint}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <Printer className=\"h-4 w-4\" />\n                طباعة\n              </button>\n              <button\n                onClick={handleDownload}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2\"\n              >\n                <Download className=\"h-4 w-4\" />\n                تحميل\n              </button>\n              <button\n                onClick={onClose}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center gap-2\"\n              >\n                <X className=\"h-4 w-4\" />\n                إغلاق\n              </button>\n            </div>\n          </div>\n\n          {/* Print Content */}\n          <div className=\"overflow-auto max-h-[calc(90vh-80px)]\">\n            <div \n              className={`print-content bg-white p-8 ${\n                printSettings.fontSize === 'small' ? 'text-sm' : \n                printSettings.fontSize === 'large' ? 'text-lg' : 'text-base'\n              }`}\n              style={{ \n                color: printSettings.textColor,\n                backgroundColor: printSettings.backgroundColor \n              }}\n            >\n              {/* Enhanced Header */}\n              {printSettings.showHeader && (\n                <div className=\"mb-8 relative\">\n                  {/* Watermark */}\n                  {printSettings.showWatermark && printSettings.watermark && (\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none\">\n                      <div className=\"text-6xl font-bold transform rotate-45 text-gray-400\">\n                        {printSettings.watermark}\n                      </div>\n                    </div>\n                  )}\n                  \n                  <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center\">\n                    {printSettings.showLogo && (\n                      <div className=\"mb-4\">\n                        <div \n                          className=\"w-24 h-24 rounded-full mx-auto flex items-center justify-center text-white text-3xl font-bold shadow-lg\"\n                          style={{ backgroundColor: printSettings.accentColor }}\n                        >\n                          {printSettings.companyName.charAt(0)}\n                        </div>\n                      </div>\n                    )}\n                    \n                    <h1 \n                      className=\"text-3xl font-bold mb-3\"\n                      style={{ color: printSettings.headerColor }}\n                    >\n                      {printSettings.companyName}\n                    </h1>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm\">\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <MapPin className=\"h-4 w-4\" />\n                        <span>{printSettings.companyAddress}</span>\n                      </div>\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <Phone className=\"h-4 w-4\" />\n                        <span>{printSettings.companyPhone}</span>\n                      </div>\n                      <div className=\"flex items-center justify-center gap-2\">\n                        <User className=\"h-4 w-4\" />\n                        <span>{printSettings.companyEmail}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Enhanced Document Title */}\n              <div className=\"text-center mb-8\">\n                <div className=\"bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 shadow-sm\">\n                  <div className=\"flex items-center justify-center gap-3 mb-3\">\n                    <FileText className=\"h-6 w-6\" style={{ color: printSettings.accentColor }} />\n                    <h2 \n                      className=\"text-2xl font-bold\"\n                      style={{ color: printSettings.headerColor }}\n                    >\n                      {title}\n                    </h2>\n                  </div>\n                  \n                  {printSettings.includeDate && (\n                    <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600\">\n                      <Calendar className=\"h-4 w-4\" />\n                      <span>تاريخ الإنشاء: {new Date().toLocaleDateString('ar-EG')}</span>\n                    </div>\n                  )}\n                  \n                  {/* Document ID */}\n                  <div className=\"mt-3 text-xs text-gray-500\">\n                    رقم المستند: DOC-{Date.now().toString().slice(-8)}\n                  </div>\n                </div>\n              </div>\n\n              {/* Content */}\n              {children}\n\n              {/* Enhanced Footer */}\n              {printSettings.showFooter && (\n                <div className=\"mt-8 border-t-2 border-gray-200 pt-6\">\n                  <div className=\"bg-gray-50 rounded-lg p-6 text-center\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-lg font-semibold text-gray-800 mb-2\">\n                        {printSettings.footerText}\n                      </p>\n                      <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                        <span>مستند معتمد ومطبوع إلكترونياً</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"border-t border-gray-200 pt-4\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500\">\n                        <div>\n                          <strong>نظام إدارة الصيدلية</strong><br />\n                          الإصدار 2.0 - 2024\n                        </div>\n                        <div>\n                          <strong>تاريخ الإنشاء:</strong><br />\n                          {new Date().toLocaleDateString('ar-EG')} - {new Date().toLocaleTimeString('ar-EG')}\n                        </div>\n                        <div>\n                          <strong>حالة المستند:</strong><br />\n                          <span className=\"text-green-600 font-semibold\">✓ صالح ومعتمد</span>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {/* QR Code placeholder */}\n                    {printSettings.includeQRCode && (\n                      <div className=\"mt-4 flex justify-center\">\n                        <div className=\"w-16 h-16 bg-gray-200 border-2 border-dashed border-gray-400 rounded flex items-center justify-center text-xs text-gray-500\">\n                          QR Code\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n\n// Classic Invoice Print Component - Similar to the provided image\nexport function InvoicePrint({ invoice, type = 'sales', settings }: {\n  invoice: any,\n  type: 'sales' | 'purchase' | 'return',\n  settings?: Partial<PrintSettings>\n}) {\n  const printSettings = { ...defaultSettings, ...settings }\n\n  return (\n    <div className=\"print-content bg-white\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Classic Header - Similar to LAREN SCIENTIFIC BUREAU */}\n      <div className=\"invoice-header\">\n        <div className=\"header-row border-b-2\">\n          {/* Left: Company Name in English */}\n          <div className=\"header-cell english-text\">\n            <h1 className=\"text-xl font-bold mb-2\" style={{ letterSpacing: '2px' }}>\n              {printSettings.companyName.toUpperCase()}\n            </h1>\n            <h2 className=\"text-lg font-bold\">\n              PHARMACY\n            </h2>\n            <h3 className=\"text-sm\">\n              MANAGEMENT SYSTEM\n            </h3>\n          </div>\n\n          {/* Center: Logo */}\n          <div className=\"header-cell\">\n            <div className=\"logo-container\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold\">\n                  {printSettings.companyName.charAt(0)}\n                </div>\n                <div className=\"text-xs font-bold\">\n                  LOGO\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right: Company Name in Arabic */}\n          <div className=\"header-cell arabic-text\">\n            <h1 className=\"text-xl font-bold mb-2\">\n              {printSettings.companyName}\n            </h1>\n            <p className=\"text-sm\">\n              {printSettings.companyAddress}\n            </p>\n            <p className=\"text-sm\">\n              {printSettings.companyPhone}\n            </p>\n          </div>\n        </div>\n\n        {/* Invoice Details Row */}\n        <div className=\"header-row text-sm\">\n          {/* Customer Name */}\n          <div className=\"header-cell arabic-text\">\n            <div className=\"font-bold\">اسم الزبون رقم المريض</div>\n            <div className=\"mt-1\">\n              {type === 'sales'\n                ? (invoice.customers?.name || invoice.customer_name || 'عميل نقدي')\n                : (invoice.suppliers?.name || 'غير محدد')\n              }\n            </div>\n          </div>\n\n          {/* Date */}\n          <div className=\"header-cell\">\n            <div className=\"font-bold\">التاريخ: بين</div>\n            <div className=\"mt-1\">\n              {new Date(invoice.created_at).toLocaleDateString('ar-EG')}\n            </div>\n          </div>\n\n          {/* Serial */}\n          <div className=\"header-cell\">\n            <div className=\"font-bold\">فاتورة رقم:</div>\n            <div className=\"mt-1 font-mono\">\n              {invoice.invoice_number}\n            </div>\n          </div>\n\n          {/* Type */}\n          <div className=\"header-cell\">\n            <div className=\"font-bold\">رقم الفاتورة:</div>\n            <div className=\"mt-1\">\n              {type === 'sales' ? 'مبيعات' : 'مشتريات'}\n            </div>\n          </div>\n        </div>\n\n        {/* Additional Info Row */}\n        <div className=\"header-row text-sm border-t\">\n          <div className=\"header-cell arabic-text\" style={{ width: '50%' }}>\n            <span className=\"font-bold\">المنطقة: الجمهورية</span>\n          </div>\n          <div className=\"header-cell\" style={{ width: '50%' }}>\n            <span className=\"font-bold\">العنوان: {printSettings.companyAddress}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Classic Invoice Table */}\n      <table className=\"invoice-table\">\n        <thead>\n          <tr>\n            <th className=\"number-cell\">ت</th>\n            <th className=\"medicine-cell\">اسم الدواء</th>\n            <th className=\"number-cell\">الكمية</th>\n            <th className=\"number-cell\">سعر الوحدة</th>\n            <th className=\"number-cell\">المجموع</th>\n            {type === 'return' && <th className=\"number-cell\">سبب المرتجع</th>}\n            <th className=\"number-cell\">رقم الدفعة</th>\n            <th className=\"number-cell\">تاريخ الانتهاء</th>\n          </tr>\n        </thead>\n        <tbody>\n          {(type === 'return'\n            ? (invoice.return_invoice_items || invoice.sales_return_items || invoice.purchase_return_items || [])\n            : (type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items)\n          )?.map((item: any, index: number) => (\n            <tr key={`item_${index}_${item.id || index}`}>\n              <td className=\"number-cell\">\n                {index + 1}\n              </td>\n              <td className=\"medicine-cell arabic-text\">\n                <div className=\"font-bold\">\n                  {type === 'return'\n                    ? (item.medicine_name ||\n                       item.medicineName ||\n                       item.medicine_batches?.medicines?.name ||\n                       item.medicines?.name ||\n                       'غير محدد')\n                    : type === 'sales'\n                      ? (item.medicine_name ||\n                         item.medicineName ||\n                         item.medicine_batches?.medicines?.name ||\n                         'غير محدد')\n                      : (item.medicine_name ||\n                         item.medicineName ||\n                         item.medicines?.name ||\n                         'غير محدد')\n                  }\n                </div>\n                {((type === 'sales' && item.medicine_batches?.medicines?.category) ||\n                  (type === 'purchase' && item.medicines?.category) ||\n                  (type === 'return' && (item.medicine_batches?.medicines?.category || item.medicines?.category))) && (\n                  <div className=\"text-xs text-gray-600\">\n                    {item.medicine_batches?.medicines?.category || item.medicines?.category}\n                  </div>\n                )}\n                {/* Additional medicine info for better identification */}\n                {type === 'sales' && item.medicine_batches?.medicines && (\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    {item.medicine_batches.medicines.strength && (\n                      <span>{item.medicine_batches.medicines.strength} </span>\n                    )}\n                    {item.medicine_batches.medicines.form && (\n                      <span>({item.medicine_batches.medicines.form})</span>\n                    )}\n                  </div>\n                )}\n              </td>\n              <td className=\"number-cell\">\n                {item.quantity}\n              </td>\n              <td className=\"number-cell\">\n                {type === 'sales'\n                  ? (item.unit_price || 0).toLocaleString()\n                  : (item.unit_cost || item.unitCost || 0).toLocaleString()\n                }\n              </td>\n              <td className=\"number-cell\">\n                {type === 'sales'\n                  ? (item.total_price || 0).toLocaleString()\n                  : (item.total_cost || item.totalCost || 0).toLocaleString()\n                }\n              </td>\n              {type === 'return' && (\n                <td className=\"number-cell text-xs\">\n                  {item.return_reason || invoice.reason || 'غير محدد'}\n                </td>\n              )}\n              <td className=\"number-cell text-xs\">\n                {type === 'return'\n                  ? (item.batch_code || item.batchCode || item.medicine_batches?.batch_number || '---')\n                  : type === 'sales'\n                    ? (item.medicine_batches?.batch_number ? item.medicine_batches.batch_number.slice(-6) : '---')\n                    : (item.batch_code || item.batchCode || '---')\n                }\n              </td>\n              <td className=\"number-cell text-xs\">\n                {type === 'return'\n                  ? (item.expiry_date || item.expiryDate || item.medicine_batches?.expiry_date\n                      ? new Date(item.expiry_date || item.expiryDate || item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/')\n                      : '---')\n                  : type === 'sales'\n                    ? (item.medicine_batches?.expiry_date ? new Date(item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/') : '---')\n                    : (item.expiry_date || item.expiryDate ? new Date(item.expiry_date || item.expiryDate).toLocaleDateString('en-GB').replace(/\\//g, '/') : '---')\n                }\n              </td>\n            </tr>\n          ))}\n\n          {/* Empty rows to fill space */}\n          {Array.from({ length: Math.max(0, 8 - ((type === 'return'\n            ? (invoice.return_invoice_items || invoice.sales_return_items || invoice.purchase_return_items || [])\n            : (type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items)\n          )?.length || 0)) }).map((_, index) => (\n            <tr key={`empty_${index}`}>\n              <td>&nbsp;</td>\n              <td>&nbsp;</td>\n              <td>&nbsp;</td>\n              <td>&nbsp;</td>\n              <td>&nbsp;</td>\n              {type === 'return' && <td>&nbsp;</td>}\n              <td>&nbsp;</td>\n              <td>&nbsp;</td>\n            </tr>\n          ))}\n\n          {/* Total Row */}\n          <tr className=\"bg-gray-100\">\n            <td className=\"text-center font-bold arabic-text\" colSpan={4}>\n              المجموع الكلي\n            </td>\n            <td className=\"number-cell\">\n              {invoice.final_amount?.toLocaleString() || invoice.total_amount?.toLocaleString() || 0}\n            </td>\n            <td colSpan={type === 'return' ? 3 : 2}>\n              &nbsp;\n            </td>\n          </tr>\n        </tbody>\n      </table>\n\n      {/* Notes Section */}\n      <div className=\"notes-section\">\n        <div className=\"font-bold mb-2 text-sm\">ملاحظات: تاريخ صرف سنة البداية</div>\n        <div className=\"min-h-16 text-sm arabic-text\">\n          {invoice.notes || ''}\n        </div>\n      </div>\n\n      {/* Signature and Footer Section */}\n      <div className=\"signature-area\">\n        <div className=\"grid grid-cols-2\">\n          {/* Left: Signature */}\n          <div className=\"text-center\">\n            <div className=\"signature-circle\">\n              <div>\n                <div className=\"text-xs\">توقيع</div>\n                <div className=\"text-xs\">الصيدلي</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right: Payment Info */}\n          <div className=\"text-sm arabic-text\">\n            <div className=\"mb-2\">\n              <span className=\"font-bold\">طريقة الدفع: </span>\n              <span className=\"font-bold\">\n                {invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}\n              </span>\n            </div>\n\n            <div className=\"mb-2\">\n              <span className=\"font-bold\">حالة الدفع: </span>\n              <span className=\"font-bold\">\n                {invoice.payment_status === 'paid' ? 'مدفوع بالكامل' :\n                 invoice.payment_status === 'partial' ? 'مدفوع جزئياً' : 'معلق'}\n              </span>\n            </div>\n\n            {invoice.payment_status !== 'paid' && (\n              <div className=\"font-bold\">\n                المبلغ المستحق: {(invoice.final_amount - (invoice.paid_amount || 0)).toLocaleString()}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"print-footer\">\n        <div className=\"grid grid-cols-2\">\n          <div>صفحة 1 من 1</div>\n          <div className=\"text-right\">{new Date().toLocaleDateString('ar-EG')}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Enhanced Report Print Component\nexport function ReportPrint({ reportData, reportType, title, settings, onInvoiceClick }: {\n  reportData: any,\n  reportType: string,\n  title: string,\n  settings?: Partial<PrintSettings>,\n  onInvoiceClick?: (invoice: any) => void\n}) {\n  const printSettings = { ...defaultSettings, ...settings }\n\n  return (\n    <div className=\"print-content\">\n      {/* Enhanced Report Header */}\n      <div className=\"mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6\">\n          <div className=\"flex items-center gap-4 mb-4\">\n            <div\n              className=\"w-16 h-16 rounded-lg flex items-center justify-center text-white\"\n              style={{ backgroundColor: printSettings.accentColor }}\n            >\n              <FileText className=\"h-8 w-8\" />\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{title}</h1>\n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                <div className=\"flex items-center gap-1\">\n                  <Calendar className=\"h-4 w-4\" />\n                  <span>تاريخ التقرير: {new Date().toLocaleDateString('ar-EG')}</span>\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <Clock className=\"h-4 w-4\" />\n                  <span>وقت الإنشاء: {new Date().toLocaleTimeString('ar-EG')}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n            <h2 className=\"font-bold text-gray-800 mb-2\">{printSettings.companyName}</h2>\n            <p className=\"text-sm text-gray-600\">{printSettings.companyAddress}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Report Summary */}\n      {Array.isArray(reportData) && reportData.length > 0 && (\n        <div className=\"mb-8\">\n          <h4 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2\">\n            <BarChart3 className=\"h-5 w-5\" />\n            ملخص التقرير\n          </h4>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            {/* Total Records */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 text-center shadow-sm\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n                  <Layers className=\"h-5 w-5 text-white\" />\n                </div>\n              </div>\n              <p className=\"text-xs text-blue-600 mb-1\">إجمالي السجلات</p>\n              <p className=\"text-2xl font-bold text-blue-800\">{reportData.length}</p>\n            </div>\n\n            {/* Sales Total */}\n            {reportType.includes('sales') && (\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 text-center shadow-sm\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <div className=\"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center\">\n                    <TrendingUp className=\"h-5 w-5 text-white\" />\n                  </div>\n                </div>\n                <p className=\"text-xs text-green-600 mb-1\">إجمالي المبيعات</p>\n                <p className=\"text-lg font-bold text-green-800\">\n                  {reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع\n                </p>\n              </div>\n            )}\n\n            {/* Purchases Total */}\n            {reportType.includes('purchases') && (\n              <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 text-center shadow-sm\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <div className=\"w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <ShoppingCart className=\"h-5 w-5 text-white\" />\n                  </div>\n                </div>\n                <p className=\"text-xs text-orange-600 mb-1\">إجمالي المشتريات</p>\n                <p className=\"text-lg font-bold text-orange-800\">\n                  {reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع\n                </p>\n              </div>\n            )}\n\n            {/* Average Value */}\n            {(reportType.includes('sales') || reportType.includes('purchases')) && (\n              <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 text-center shadow-sm\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <div className=\"w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <Target className=\"h-5 w-5 text-white\" />\n                  </div>\n                </div>\n                <p className=\"text-xs text-purple-600 mb-1\">متوسط القيمة</p>\n                <p className=\"text-lg font-bold text-purple-800\">\n                  {Math.round(reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0) / reportData.length).toLocaleString()} د.ع\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Enhanced Report Table */}\n      {Array.isArray(reportData) && reportData.length > 0 && (\n        <div className=\"mb-6\">\n          <h4 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            تفاصيل البيانات\n          </h4>\n\n          <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr\n                    className=\"text-white font-semibold\"\n                    style={{ backgroundColor: printSettings.accentColor }}\n                  >\n                    <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">\n                      #\n                    </th>\n                    {reportType.includes('sales') && (\n                      <>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">رقم الفاتورة</th>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">اسم العميل</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">المبلغ الإجمالي</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">حالة الدفع</th>\n                        <th className=\"px-3 py-3 text-center\">التاريخ</th>\n                      </>\n                    )}\n                    {reportType.includes('purchases') && (\n                      <>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">رقم الفاتورة</th>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">اسم المورد</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">المبلغ الإجمالي</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">حالة الدفع</th>\n                        <th className=\"px-3 py-3 text-center\">التاريخ</th>\n                      </>\n                    )}\n                    {reportType === 'inventory' && (\n                      <>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">اسم الدواء</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">الفئة</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">الكمية المتاحة</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">تاريخ الانتهاء</th>\n                        <th className=\"px-3 py-3 text-center\">الحالة</th>\n                      </>\n                    )}\n                    {reportType === 'cashbox' && (\n                      <>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">النوع</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">الفئة</th>\n                        <th className=\"px-3 py-3 text-right border-r border-white border-opacity-20\">الوصف</th>\n                        <th className=\"px-3 py-3 text-center border-r border-white border-opacity-20\">المبلغ</th>\n                        <th className=\"px-3 py-3 text-center\">التاريخ</th>\n                      </>\n                    )}\n                  </tr>\n                </thead>\n                <tbody>\n                  {reportData.slice(0, 100).map((item: any, index: number) => (\n                    <tr key={`report_item_${index}_${item.id || index}`} className=\"border-b border-gray-100 hover:bg-gray-50\">\n                      <td className=\"px-3 py-2 text-center font-mono text-sm text-gray-500 border-r border-gray-200\">\n                        {index + 1}\n                      </td>\n\n                      {reportType.includes('sales') && (\n                        <>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <button\n                              onClick={() => onInvoiceClick?.(item)}\n                              className=\"font-mono text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200 cursor-pointer transition-colors\"\n                              title=\"انقر لعرض تفاصيل الفاتورة\"\n                            >\n                              {item.invoice_number}\n                            </button>\n                          </td>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <div>\n                              <p className=\"font-semibold text-gray-900\">\n                                {item.customers?.name || item.customer_name || 'عميل نقدي'}\n                              </p>\n                              {item.customers?.phone && (\n                                <p className=\"text-xs text-gray-500\">{item.customers.phone}</p>\n                              )}\n                            </div>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className=\"font-bold text-gray-900\">\n                              {item.final_amount?.toLocaleString() || 0} د.ع\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                              item.payment_status === 'paid'\n                                ? 'bg-green-100 text-green-800'\n                                : item.payment_status === 'partial'\n                                ? 'bg-yellow-100 text-yellow-800'\n                                : 'bg-red-100 text-red-800'\n                            }`}>\n                              {item.payment_status === 'paid' ? 'مدفوع' :\n                               item.payment_status === 'partial' ? 'جزئي' : 'معلق'}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center\">\n                            <span className=\"text-sm text-gray-600\">\n                              {new Date(item.created_at).toLocaleDateString('ar-EG')}\n                            </span>\n                          </td>\n                        </>\n                      )}\n\n                      {reportType.includes('purchases') && (\n                        <>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <button\n                              onClick={() => onInvoiceClick?.(item)}\n                              className=\"font-mono text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded hover:bg-orange-200 cursor-pointer transition-colors\"\n                              title=\"انقر لعرض تفاصيل الفاتورة\"\n                            >\n                              {item.invoice_number}\n                            </button>\n                          </td>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <div>\n                              <p className=\"font-semibold text-gray-900\">\n                                {item.suppliers?.name || 'غير محدد'}\n                              </p>\n                              {item.suppliers?.phone && (\n                                <p className=\"text-xs text-gray-500\">{item.suppliers.phone}</p>\n                              )}\n                            </div>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className=\"font-bold text-gray-900\">\n                              {item.final_amount?.toLocaleString() || 0} د.ع\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                              item.payment_status === 'paid'\n                                ? 'bg-green-100 text-green-800'\n                                : item.payment_status === 'partial'\n                                ? 'bg-yellow-100 text-yellow-800'\n                                : 'bg-red-100 text-red-800'\n                            }`}>\n                              {item.payment_status === 'paid' ? 'مدفوع' :\n                               item.payment_status === 'partial' ? 'جزئي' : 'معلق'}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center\">\n                            <span className=\"text-sm text-gray-600\">\n                              {new Date(item.created_at).toLocaleDateString('ar-EG')}\n                            </span>\n                          </td>\n                        </>\n                      )}\n\n                      {reportType === 'inventory' && (\n                        <>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <div>\n                              <p className=\"font-semibold text-gray-900\">\n                                {item.medicines?.name || 'غير محدد'}\n                              </p>\n                              {item.medicine_batches?.batch_number && (\n                                <p className=\"text-xs text-gray-500\">\n                                  دفعة: {item.medicine_batches.batch_number}\n                                </p>\n                              )}\n                            </div>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className=\"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs\">\n                              {item.medicines?.category || 'غير محدد'}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className={`font-bold px-2 py-1 rounded ${\n                              item.quantity < 10\n                                ? 'bg-red-100 text-red-800'\n                                : item.quantity < 50\n                                ? 'bg-yellow-100 text-yellow-800'\n                                : 'bg-green-100 text-green-800'\n                            }`}>\n                              {item.quantity}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className=\"text-sm text-gray-600\">\n                              {new Date(item.expiry_date).toLocaleDateString('ar-EG')}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                              item.quantity < 10\n                                ? 'bg-red-100 text-red-800'\n                                : item.quantity < 50\n                                ? 'bg-yellow-100 text-yellow-800'\n                                : 'bg-green-100 text-green-800'\n                            }`}>\n                              {item.quantity < 10 ? 'كمية قليلة' :\n                               item.quantity < 50 ? 'كمية متوسطة' : 'كمية جيدة'}\n                            </span>\n                          </td>\n                        </>\n                      )}\n\n                      {reportType === 'cashbox' && (\n                        <>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                              item.transaction_type === 'income'\n                                ? 'bg-green-100 text-green-800'\n                                : 'bg-red-100 text-red-800'\n                            }`}>\n                              {item.transaction_type === 'income' ? 'وارد' : 'مصروف'}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <span className=\"bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs\">\n                              {item.category}\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 border-r border-gray-200\">\n                            <p className=\"text-sm text-gray-900\">{item.description}</p>\n                          </td>\n                          <td className=\"px-3 py-2 text-center border-r border-gray-200\">\n                            <span className={`font-mono font-bold ${\n                              item.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'\n                            }`}>\n                              {item.amount?.toLocaleString() || 0} د.ع\n                            </span>\n                          </td>\n                          <td className=\"px-3 py-2 text-center\">\n                            <span className=\"text-sm text-gray-600\">\n                              {new Date(item.created_at).toLocaleDateString('ar-EG')}\n                            </span>\n                          </td>\n                        </>\n                      )}\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination Info */}\n            {reportData.length > 100 && (\n              <div className=\"bg-gray-50 px-4 py-3 border-t border-gray-200\">\n                <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>\n                    يتم عرض أول 100 سجل من إجمالي {reportData.length} سجل\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Empty State */}\n      {(!Array.isArray(reportData) || reportData.length === 0) && (\n        <div className=\"text-center py-12\">\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-8\">\n            <AlertCircle className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">لا توجد بيانات</h3>\n            <p className=\"text-gray-600\">لا توجد بيانات متاحة لعرضها في هذا التقرير</p>\n          </div>\n        </div>\n      )}\n\n      {/* Report Footer */}\n      <div className=\"mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Report Statistics */}\n          <div>\n            <h5 className=\"font-bold text-gray-800 mb-3\">إحصائيات التقرير:</h5>\n            <div className=\"space-y-2 text-sm text-gray-600\">\n              <div className=\"flex justify-between\">\n                <span>عدد السجلات المعروضة:</span>\n                <span className=\"font-semibold\">\n                  {Array.isArray(reportData) ? Math.min(reportData.length, 100) : 0}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>إجمالي السجلات:</span>\n                <span className=\"font-semibold\">\n                  {Array.isArray(reportData) ? reportData.length : 0}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>تاريخ آخر تحديث:</span>\n                <span className=\"font-semibold\">\n                  {new Date().toLocaleDateString('ar-EG')}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Report Notes */}\n          <div>\n            <h5 className=\"font-bold text-gray-800 mb-3\">ملاحظات مهمة:</h5>\n            <ul className=\"text-xs text-gray-600 space-y-1\">\n              <li>• هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة الصيدلية</li>\n              <li>• جميع البيانات محدثة حتى تاريخ إنشاء التقرير</li>\n              <li>• للاستفسارات يرجى التواصل مع إدارة النظام</li>\n              <li>• يُنصح بحفظ نسخة من هذا التقرير للمراجعة المستقبلية</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAUA,+DAA+D;AAC/D,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IrB,CAAC;AAgCD,MAAM,kBAAiC;IACrC,aAAa;IACb,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,eAAe;IACf,WAAW;IACX,YAAY;IACZ,UAAU;IACV,aAAa;IACb,oBAAoB;IACpB,eAAe;IACf,aAAa;IACb,aAAa;IACb,WAAW;IACX,iBAAiB;AACnB;AAEe,SAAS,cAAc,EACpC,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACS;IACnB,MAAM,gBAAgB;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IAExD,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,kBAAkB;QAClB;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAY;;;;;;0BACtD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGjC,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;sCAO/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,2BAA2B,EACrC,cAAc,QAAQ,KAAK,UAAU,YACrC,cAAc,QAAQ,KAAK,UAAU,YAAY,aACjD;gCACF,OAAO;oCACL,OAAO,cAAc,SAAS;oCAC9B,iBAAiB,cAAc,eAAe;gCAChD;;oCAGC,cAAc,UAAU,kBACvB,8OAAC;wCAAI,WAAU;;4CAEZ,cAAc,aAAa,IAAI,cAAc,SAAS,kBACrD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,cAAc,SAAS;;;;;;;;;;;0DAK9B,8OAAC;gDAAI,WAAU;;oDACZ,cAAc,QAAQ,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,cAAc,WAAW;4DAAC;sEAEnD,cAAc,WAAW,CAAC,MAAM,CAAC;;;;;;;;;;;kEAKxC,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,cAAc,WAAW;wDAAC;kEAEzC,cAAc,WAAW;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;kFAAM,cAAc,cAAc;;;;;;;;;;;;0EAErC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAM,cAAc,YAAY;;;;;;;;;;;;0EAEnC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAM,cAAc,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAU,OAAO;gEAAE,OAAO,cAAc,WAAW;4DAAC;;;;;;sEACxE,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,cAAc,WAAW;4DAAC;sEAEzC;;;;;;;;;;;;gDAIJ,cAAc,WAAW,kBACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;;gEAAK;gEAAgB,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;8DAKxD,8OAAC;oDAAI,WAAU;;wDAA6B;wDACxB,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;oCAMpD;oCAGA,cAAc,UAAU,kBACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,cAAc,UAAU;;;;;;sEAE3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAIV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;kFAAO;;;;;;kFAA4B,8OAAC;;;;;oEAAK;;;;;;;0EAG5C,8OAAC;;kFACC,8OAAC;kFAAO;;;;;;kFAAuB,8OAAC;;;;;oEAC/B,IAAI,OAAO,kBAAkB,CAAC;oEAAS;oEAAI,IAAI,OAAO,kBAAkB,CAAC;;;;;;;0EAE5E,8OAAC;;kFACC,8OAAC;kFAAO;;;;;;kFAAsB,8OAAC;;;;;kFAC/B,8OAAC;wEAAK,WAAU;kFAA+B;;;;;;;;;;;;;;;;;;;;;;;gDAMpD,cAAc,aAAa,kBAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAA8H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrK;AAGO,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,OAAO,EAAE,QAAQ,EAI/D;IACC,MAAM,gBAAgB;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IAExD,qBACE,8OAAC;QAAI,WAAU;QAAyB,OAAO;YAAE,YAAY;QAAoB;;0BAE/E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;wCAAyB,OAAO;4CAAE,eAAe;wCAAM;kDAClE,cAAc,WAAW,CAAC,WAAW;;;;;;kDAExC,8OAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAGlC,8OAAC;wCAAG,WAAU;kDAAU;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,cAAc,WAAW,CAAC,MAAM,CAAC;;;;;;0DAEpC,8OAAC;gDAAI,WAAU;0DAAoB;;;;;;;;;;;;;;;;;;;;;;0CAQzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,cAAc,WAAW;;;;;;kDAE5B,8OAAC;wCAAE,WAAU;kDACV,cAAc,cAAc;;;;;;kDAE/B,8OAAC;wCAAE,WAAU;kDACV,cAAc,YAAY;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY;;;;;;kDAC3B,8OAAC;wCAAI,WAAU;kDACZ,SAAS,UACL,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,cACpD,QAAQ,SAAS,EAAE,QAAQ;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY;;;;;;kDAC3B,8OAAC;wCAAI,WAAU;kDACZ,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0CAKrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY;;;;;;kDAC3B,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,cAAc;;;;;;;;;;;;0CAK3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY;;;;;;kDAC3B,8OAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,WAAW;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0B,OAAO;oCAAE,OAAO;gCAAM;0CAC7D,cAAA,8OAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;gCAAc,OAAO;oCAAE,OAAO;gCAAM;0CACjD,cAAA,8OAAC;oCAAK,WAAU;;wCAAY;wCAAU,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMxE,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;kCACC,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;gCAC3B,SAAS,0BAAY,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAG,WAAU;8CAAc;;;;;;;;;;;;;;;;;kCAGhC,8OAAC;;4BACE,CAAC,SAAS,WACN,QAAQ,oBAAoB,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,IAAI,EAAE,GACjG,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB,AACpF,GAAG,IAAI,CAAC,MAAW,sBACjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,QAAQ;;;;;;sDAEX,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,SAAS,WACL,KAAK,aAAa,IAClB,KAAK,YAAY,IACjB,KAAK,gBAAgB,EAAE,WAAW,QAClC,KAAK,SAAS,EAAE,QAChB,aACD,SAAS,UACN,KAAK,aAAa,IAClB,KAAK,YAAY,IACjB,KAAK,gBAAgB,EAAE,WAAW,QAClC,aACA,KAAK,aAAa,IAClB,KAAK,YAAY,IACjB,KAAK,SAAS,EAAE,QAChB;;;;;;gDAGR,CAAC,AAAC,SAAS,WAAW,KAAK,gBAAgB,EAAE,WAAW,YACtD,SAAS,cAAc,KAAK,SAAS,EAAE,YACvC,SAAS,YAAY,CAAC,KAAK,gBAAgB,EAAE,WAAW,YAAY,KAAK,SAAS,EAAE,QAAQ,CAAE,mBAC/F,8OAAC;oDAAI,WAAU;8DACZ,KAAK,gBAAgB,EAAE,WAAW,YAAY,KAAK,SAAS,EAAE;;;;;;gDAIlE,SAAS,WAAW,KAAK,gBAAgB,EAAE,2BAC1C,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,gBAAgB,CAAC,SAAS,CAAC,QAAQ,kBACvC,8OAAC;;gEAAM,KAAK,gBAAgB,CAAC,SAAS,CAAC,QAAQ;gEAAC;;;;;;;wDAEjD,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI,kBACnC,8OAAC;;gEAAK;gEAAE,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAKrD,8OAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ;;;;;;sDAEhB,8OAAC;4CAAG,WAAU;sDACX,SAAS,UACN,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KACrC,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc;;;;;;sDAG3D,8OAAC;4CAAG,WAAU;sDACX,SAAS,UACN,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KACtC,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc;;;;;;wCAG5D,SAAS,0BACR,8OAAC;4CAAG,WAAU;sDACX,KAAK,aAAa,IAAI,QAAQ,MAAM,IAAI;;;;;;sDAG7C,8OAAC;4CAAG,WAAU;sDACX,SAAS,WACL,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,gBAAgB,EAAE,gBAAgB,QAC7E,SAAS,UACN,KAAK,gBAAgB,EAAE,eAAe,KAAK,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,QACrF,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;;;;;;sDAG9C,8OAAC;4CAAG,WAAU;sDACX,SAAS,WACL,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,gBAAgB,EAAE,cAC3D,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAC9H,QACJ,SAAS,UACN,KAAK,gBAAgB,EAAE,cAAc,IAAI,KAAK,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,QACnI,KAAK,WAAW,IAAI,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO;;;;;;;mCA7ExI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,OAAO;;;;;4BAoF7C,MAAM,IAAI,CAAC;gCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,SAAS,WAC5C,QAAQ,oBAAoB,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,IAAI,EAAE,GACjG,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB,AACpF,GAAG,UAAU,CAAC;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1B,8OAAC;;sDACC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;wCACH,SAAS,0BAAY,8OAAC;sDAAG;;;;;;sDAC1B,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;mCARG,CAAC,MAAM,EAAE,OAAO;;;;;0CAa3B,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;wCAAoC,SAAS;kDAAG;;;;;;kDAG9D,8OAAC;wCAAG,WAAU;kDACX,QAAQ,YAAY,EAAE,oBAAoB,QAAQ,YAAY,EAAE,oBAAoB;;;;;;kDAEvF,8OAAC;wCAAG,SAAS,SAAS,WAAW,IAAI;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAyB;;;;;;kCACxC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK,IAAI;;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;sCAM/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDACb,QAAQ,cAAc,KAAK,SAAS,UAAU;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDACb,QAAQ,cAAc,KAAK,SAAS,kBACpC,QAAQ,cAAc,KAAK,YAAY,iBAAiB;;;;;;;;;;;;gCAI5D,QAAQ,cAAc,KAAK,wBAC1B,8OAAC;oCAAI,WAAU;;wCAAY;wCACR,CAAC,QAAQ,YAAY,GAAG,CAAC,QAAQ,WAAW,IAAI,CAAC,CAAC,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAI;;;;;;sCACL,8OAAC;4BAAI,WAAU;sCAAc,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAKrE;AAGO,SAAS,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAMpF;IACC,MAAM,gBAAgB;QAAE,GAAG,eAAe;QAAE,GAAG,QAAQ;IAAC;IAExD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,cAAc,WAAW;oCAAC;8CAEpD,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;;gEAAK;gEAAgB,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;8DAEtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAK;gEAAc,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgC,cAAc,WAAW;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAAyB,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;YAMvE,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,mBAChD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGtB,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAoC,WAAW,MAAM;;;;;;;;;;;;4BAInE,WAAW,QAAQ,CAAC,0BACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;;4CACV,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc;4CAAG;;;;;;;;;;;;;4BAMxG,WAAW,QAAQ,CAAC,8BACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAC5C,8OAAC;wCAAE,WAAU;;4CACV,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc;4CAAG;;;;;;;;;;;;;4BAMxG,CAAC,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,YAAY,mBAChE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGtB,8OAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAC5C,8OAAC;wCAAE,WAAU;;4CACV,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,WAAW,MAAM,EAAE,cAAc;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAS9I,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,mBAChD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,cAAc,WAAW;gDAAC;;kEAEpD,8OAAC;wDAAG,WAAU;kEAAgE;;;;;;oDAG7E,WAAW,QAAQ,CAAC,0BACnB;;0EACE,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;;;oDAGzC,WAAW,QAAQ,CAAC,8BACnB;;0EACE,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;;;oDAGzC,eAAe,6BACd;;0EACE,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;;;oDAGzC,eAAe,2BACd;;0EACE,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAA+D;;;;;;0EAC7E,8OAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;sDAK9C,8OAAC;sDACE,WAAW,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,MAAW,sBACxC,8OAAC;oDAAoD,WAAU;;sEAC7D,8OAAC;4DAAG,WAAU;sEACX,QAAQ;;;;;;wDAGV,WAAW,QAAQ,CAAC,0BACnB;;8EACE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,SAAS,IAAM,iBAAiB;wEAChC,WAAU;wEACV,OAAM;kFAEL,KAAK,cAAc;;;;;;;;;;;8EAGxB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,KAAK,SAAS,EAAE,QAAQ,KAAK,aAAa,IAAI;;;;;;4EAEhD,KAAK,SAAS,EAAE,uBACf,8OAAC;gFAAE,WAAU;0FAAyB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;8EAIhE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;;4EACb,KAAK,YAAY,EAAE,oBAAoB;4EAAE;;;;;;;;;;;;8EAG9C,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,6CAA6C,EAC7D,KAAK,cAAc,KAAK,SACpB,gCACA,KAAK,cAAc,KAAK,YACxB,kCACA,2BACJ;kFACC,KAAK,cAAc,KAAK,SAAS,UACjC,KAAK,cAAc,KAAK,YAAY,SAAS;;;;;;;;;;;8EAGlD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wDAMrD,WAAW,QAAQ,CAAC,8BACnB;;8EACE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,SAAS,IAAM,iBAAiB;wEAChC,WAAU;wEACV,OAAM;kFAEL,KAAK,cAAc;;;;;;;;;;;8EAGxB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,KAAK,SAAS,EAAE,QAAQ;;;;;;4EAE1B,KAAK,SAAS,EAAE,uBACf,8OAAC;gFAAE,WAAU;0FAAyB,KAAK,SAAS,CAAC,KAAK;;;;;;;;;;;;;;;;;8EAIhE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;;4EACb,KAAK,YAAY,EAAE,oBAAoB;4EAAE;;;;;;;;;;;;8EAG9C,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,6CAA6C,EAC7D,KAAK,cAAc,KAAK,SACpB,gCACA,KAAK,cAAc,KAAK,YACxB,kCACA,2BACJ;kFACC,KAAK,cAAc,KAAK,SAAS,UACjC,KAAK,cAAc,KAAK,YAAY,SAAS;;;;;;;;;;;8EAGlD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wDAMrD,eAAe,6BACd;;8EACE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,KAAK,SAAS,EAAE,QAAQ;;;;;;4EAE1B,KAAK,gBAAgB,EAAE,8BACtB,8OAAC;gFAAE,WAAU;;oFAAwB;oFAC5B,KAAK,gBAAgB,CAAC,YAAY;;;;;;;;;;;;;;;;;;8EAKjD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,KAAK,SAAS,EAAE,YAAY;;;;;;;;;;;8EAGjC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,4BAA4B,EAC5C,KAAK,QAAQ,GAAG,KACZ,4BACA,KAAK,QAAQ,GAAG,KAChB,kCACA,+BACJ;kFACC,KAAK,QAAQ;;;;;;;;;;;8EAGlB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;8EAGnD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,6CAA6C,EAC7D,KAAK,QAAQ,GAAG,KACZ,4BACA,KAAK,QAAQ,GAAG,KAChB,kCACA,+BACJ;kFACC,KAAK,QAAQ,GAAG,KAAK,eACrB,KAAK,QAAQ,GAAG,KAAK,gBAAgB;;;;;;;;;;;;;wDAM7C,eAAe,2BACd;;8EACE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,6CAA6C,EAC7D,KAAK,gBAAgB,KAAK,WACtB,gCACA,2BACJ;kFACC,KAAK,gBAAgB,KAAK,WAAW,SAAS;;;;;;;;;;;8EAGnD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,KAAK,QAAQ;;;;;;;;;;;8EAGlB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAE,WAAU;kFAAyB,KAAK,WAAW;;;;;;;;;;;8EAExD,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,gBAAgB,KAAK,WAAW,mBAAmB,gBACxD;;4EACC,KAAK,MAAM,EAAE,oBAAoB;4EAAE;;;;;;;;;;;;8EAGxC,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;mDA/K/C,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;4BA2L1D,WAAW,MAAM,GAAG,qBACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;;gDAAK;gDAC2B,WAAW,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU9D,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,KAAK,CAAC,mBACrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,MAAM,OAAO,CAAC,cAAc,KAAK,GAAG,CAAC,WAAW,MAAM,EAAE,OAAO;;;;;;;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,MAAM,OAAO,CAAC,cAAc,WAAW,MAAM,GAAG;;;;;;;;;;;;sDAGrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 5481, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface Medicine {\n  id: string\n  name: string\n  category: string\n  manufacturer: string\n  active_ingredient: string\n  strength: string\n  form: string // tablet, capsule, syrup, etc.\n  unit_price: number\n  selling_price: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MedicineBatch {\n  id: string\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id: string\n  received_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Supplier {\n  id: string\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoice {\n  id: string\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  created_at: string\n}\n\nexport interface PurchaseInvoice {\n  id: string\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PurchaseInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  created_at: string\n}\n\nexport interface SalesReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface PurchaseReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface InventoryMovement {\n  id: string\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 5493, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\n\n// Medicine operations\nexport const addMedicine = async (medicineData: {\n  name: string\n  category: string\n  manufacturer?: string\n  active_ingredient?: string\n  strength?: string\n  form: string\n  unit_price: number\n  selling_price: number\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .insert([{\n        name: medicineData.name,\n        category: medicineData.category,\n        manufacturer: medicineData.manufacturer || '',\n        active_ingredient: medicineData.active_ingredient || '',\n        strength: medicineData.strength || '',\n        form: medicineData.form,\n        unit_price: medicineData.unit_price,\n        selling_price: medicineData.selling_price\n      }])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicines = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .select(`\n        *,\n        medicine_batches (\n          id,\n          batch_code,\n          expiry_date,\n          quantity,\n          cost_price,\n          selling_price,\n          supplier_id,\n          received_date\n        )\n      `)\n      .order('name')\n\n    if (error) {\n      console.warn('Supabase error fetching medicines, using localStorage:', error)\n      // Fallback to localStorage\n      return getMedicinesFromLocalStorage()\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching medicines:', error)\n    // Final fallback to localStorage\n    return getMedicinesFromLocalStorage()\n  }\n}\n\n// Helper function to get medicines from localStorage\nconst getMedicinesFromLocalStorage = () => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    // If no medicines in localStorage, create sample data\n    if (medicines.length === 0) {\n      console.log('🔄 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية...')\n      return createSampleMedicinesData()\n    }\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = medicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: batches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: batches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم تحميل ${medicinesWithBatches.length} دواء من localStorage`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error loading medicines from localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to create sample medicines data\nconst createSampleMedicinesData = () => {\n  try {\n    const sampleMedicines = [\n      {\n        id: 'med_1',\n        name: 'باراسيتامول 500 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الأدوية العراقية',\n        strength: '500mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_2',\n        name: 'أموكسيسيلين 250 مجم',\n        category: 'مضادات حيوية',\n        manufacturer: 'شركة بغداد للأدوية',\n        strength: '250mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_3',\n        name: 'أسبرين 100 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة النهرين',\n        strength: '100mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_4',\n        name: 'إيبوبروفين 400 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الرافدين',\n        strength: '400mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_5',\n        name: 'أوميبرازول 20 مجم',\n        category: 'أدوية المعدة',\n        manufacturer: 'شركة دجلة',\n        strength: '20mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    const sampleBatches = [\n      {\n        id: 'batch_1',\n        medicine_id: 'med_1',\n        batch_code: 'PAR001',\n        expiry_date: '2025-12-31',\n        quantity: 100,\n        cost_price: 500,\n        selling_price: 750,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_2',\n        medicine_id: 'med_2',\n        batch_code: 'AMX001',\n        expiry_date: '2025-06-30',\n        quantity: 50,\n        cost_price: 1000,\n        selling_price: 1500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_3',\n        medicine_id: 'med_3',\n        batch_code: 'ASP001',\n        expiry_date: '2026-03-31',\n        quantity: 200,\n        cost_price: 300,\n        selling_price: 500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_4',\n        medicine_id: 'med_4',\n        batch_code: 'IBU001',\n        expiry_date: '2025-09-30',\n        quantity: 75,\n        cost_price: 800,\n        selling_price: 1200,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_5',\n        medicine_id: 'med_5',\n        batch_code: 'OME001',\n        expiry_date: '2025-11-30',\n        quantity: 30,\n        cost_price: 1500,\n        selling_price: 2000,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Create sample customers\n    const sampleCustomers = [\n      {\n        id: 'cust_1',\n        name: 'أحمد محمد علي',\n        phone: '07701234567',\n        address: 'بغداد - الكرادة',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'cust_2',\n        name: 'فاطمة حسن',\n        phone: '07809876543',\n        address: 'بغداد - الجادرية',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Save to localStorage\n    localStorage.setItem('medicines', JSON.stringify(sampleMedicines))\n    localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))\n    localStorage.setItem('customers', JSON.stringify(sampleCustomers))\n\n    // Initialize empty arrays for invoices\n    localStorage.setItem('sales_invoices', JSON.stringify([]))\n    localStorage.setItem('sales_invoice_items', JSON.stringify([]))\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = sampleMedicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم إنشاء ${medicinesWithBatches.length} دواء تجريبي`)\n    console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)\n    console.log(`✅ تم إنشاء ${sampleCustomers.length} عميل تجريبي`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error creating sample medicines:', error)\n    return { success: false, error }\n  }\n}\n\n// Function to initialize system data\nexport const initializeSystemData = async () => {\n  try {\n    console.log('🔄 تهيئة بيانات النظام...')\n\n    // Check if we have basic data\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    if (medicines.length === 0 || batches.length === 0) {\n      console.log('📦 إنشاء البيانات الأساسية...')\n      return createSampleMedicinesData()\n    }\n\n    console.log(`✅ البيانات الأساسية موجودة: ${medicines.length} دواء، ${batches.length} دفعة`)\n    return { success: true, data: medicines }\n  } catch (error) {\n    console.error('Error initializing system data:', error)\n    return { success: false, error }\n  }\n}\n\n// Medicine batch operations\nexport const addMedicineBatch = async (batchData: {\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .insert([batchData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine batch:', error)\n    return { success: false, error }\n  }\n}\n\nexport const updateBatchQuantity = async (batchId: string, newQuantity: number) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .update({ quantity: newQuantity })\n      .eq('id', batchId)\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error updating batch quantity, using localStorage:', error)\n      // Fallback to localStorage\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found in localStorage' }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error updating batch quantity:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found' }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for batch update:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Sales operations\nexport const createSalesInvoice = async (invoiceData: {\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating sales invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addSalesInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for invoice items, using localStorage:', error)\n      // Fallback to localStorage - preserve existing medicine names\n      console.log('📦 العناصر الواردة للحفظ:', items)\n\n      const enhancedItems = items.map(item => {\n        // Use existing medicine name if available, otherwise enhance\n        const medicineName = item.medicine_name || item.medicineName\n\n        if (medicineName && medicineName !== 'غير محدد') {\n          console.log(`✅ استخدام اسم الدواء الموجود: ${medicineName}`)\n          return {\n            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicine_batches: {\n              batch_code: '',\n              expiry_date: '',\n              medicines: {\n                name: medicineName,\n                category: '',\n                manufacturer: '',\n                strength: '',\n                form: ''\n              }\n            },\n            created_at: new Date().toISOString()\n          }\n        } else {\n          console.log(`⚠️ لا يوجد اسم دواء، سيتم البحث عنه...`)\n          // Only enhance if no medicine name is available\n          return item\n        }\n      })\n\n      // Enhance items that still need medicine names\n      const itemsNeedingEnhancement = enhancedItems.filter(item =>\n        !item.medicine_name || item.medicine_name === 'غير محدد'\n      )\n\n      let finalItems = enhancedItems\n      if (itemsNeedingEnhancement.length > 0) {\n        console.log(`🔍 تحسين ${itemsNeedingEnhancement.length} عنصر يحتاج أسماء أدوية`)\n        const enhancedNeeded = await enhanceItemsWithMedicineNames(itemsNeedingEnhancement)\n\n        // Replace items that needed enhancement\n        finalItems = enhancedItems.map(item => {\n          if (!item.medicine_name || item.medicine_name === 'غير محدد') {\n            const enhanced = enhancedNeeded.find(e => e.medicine_batch_id === item.medicine_batch_id)\n            return enhanced || item\n          }\n          return item\n        })\n      }\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...finalItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ تم حفظ العناصر في localStorage:', finalItems)\n      return { success: true, data: finalItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding sales invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      console.log('🔄 Final fallback - حفظ العناصر مع الأسماء الموجودة')\n\n      const enhancedItems = items.map(item => {\n        const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        return {\n          id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          ...item,\n          medicine_name: medicineName,\n          medicineName: medicineName,\n          medicine_batches: {\n            batch_code: '',\n            expiry_date: '',\n            medicines: {\n              name: medicineName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          },\n          created_at: new Date().toISOString()\n        }\n      })\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...enhancedItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ Final fallback - تم حفظ العناصر:', enhancedItems)\n      return { success: true, data: enhancedItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Helper function to enhance items with medicine names\nconst enhanceItemsWithMedicineNames = async (items: any[]) => {\n  try {\n    // Get medicine data for names\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    return items.map(item => {\n      // Use existing medicine name if available, otherwise find from batch\n      let medicineName = item.medicine_name || item.medicineName\n\n      if (!medicineName || medicineName === 'غير محدد') {\n        // Find medicine name from batch\n        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n        const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n        medicineName = medicine?.name || 'غير محدد'\n      }\n\n      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      return {\n        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: medicineName,\n        medicineName: medicineName, // Add both for compatibility\n        medicine_batches: {\n          batch_code: batch?.batch_code || '',\n          expiry_date: batch?.expiry_date || '',\n          medicines: {\n            name: medicineName,\n            category: medicine?.category || '',\n            manufacturer: medicine?.manufacturer || '',\n            strength: medicine?.strength || '',\n            form: medicine?.form || ''\n          }\n        },\n        created_at: new Date().toISOString()\n      }\n    })\n  } catch (error) {\n    console.error('خطأ في تحسين العناصر بأسماء الأدوية:', error)\n    // Return items with basic structure if enhancement fails\n    return items.map(item => ({\n      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      ...item,\n      medicine_name: 'غير محدد',\n      created_at: new Date().toISOString()\n    }))\n  }\n}\n\n// Function to fix existing localStorage data with medicine names\nexport const fixLocalStorageInvoiceItems = () => {\n  try {\n    console.log('🔧 بدء إصلاح بيانات الفواتير في localStorage...')\n\n    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    console.log(`📦 عدد عناصر الفواتير: ${salesItems.length}`)\n    console.log(`💊 عدد الأدوية: ${medicines.length}`)\n    console.log(`📋 عدد الدفعات: ${batches.length}`)\n\n    let fixedCount = 0\n    let notFoundCount = 0\n    let createdBatchesCount = 0\n\n    const fixedItems = salesItems.map((item: any) => {\n      // Skip if already has proper medicine name structure\n      if (item.medicine_batches?.medicines?.name && item.medicine_batches.medicines.name !== 'غير محدد') {\n        return item\n      }\n\n      // Find medicine name from batch\n      let batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      let medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      // If batch not found, try to create a missing batch\n      if (!batch && item.medicine_batch_id) {\n        console.log(`🔍 محاولة إنشاء دفعة مفقودة: ${item.medicine_batch_id}`)\n\n        // Try to find medicine by name if available in item\n        if (item.medicine_name && item.medicine_name !== 'غير محدد') {\n          medicine = medicines.find((m: any) => m.name === item.medicine_name)\n        }\n\n        // If still no medicine found, use first available medicine as fallback\n        if (!medicine && medicines.length > 0) {\n          medicine = medicines[0]\n          console.log(`🔄 استخدام دواء افتراضي: ${medicine.name}`)\n        }\n\n        // Create missing batch if we have a medicine\n        if (medicine) {\n          batch = {\n            id: item.medicine_batch_id,\n            medicine_id: medicine.id,\n            batch_code: item.batch_code || `BATCH_${Date.now()}`,\n            expiry_date: item.expiry_date || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n            quantity: item.quantity || 0,\n            cost_price: item.unit_price || medicine.unit_price || 0,\n            selling_price: item.unit_price || medicine.selling_price || 0,\n            supplier_id: null,\n            received_date: new Date().toISOString().split('T')[0],\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n\n          batches.push(batch)\n          createdBatchesCount++\n          console.log(`✅ تم إنشاء دفعة جديدة: ${batch.batch_code} للدواء: ${medicine.name}`)\n        }\n      }\n\n      if (medicine?.name) {\n        fixedCount++\n        console.log(`✅ إصلاح العنصر: ${medicine.name} (Batch: ${batch?.batch_code})`)\n\n        return {\n          ...item,\n          medicine_name: medicine.name,\n          medicineName: medicine.name, // Add both for compatibility\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: medicine.name,\n              category: medicine.category || '',\n              manufacturer: medicine.manufacturer || '',\n              strength: medicine.strength || '',\n              form: medicine.form || ''\n            }\n          }\n        }\n      } else {\n        notFoundCount++\n        console.log(`⚠️ لم يتم العثور على الدواء للعنصر: ${item.medicine_batch_id}`)\n\n        // Try to preserve any existing name\n        const existingName = item.medicine_name || item.medicineName || 'غير محدد'\n        return {\n          ...item,\n          medicine_name: existingName,\n          medicineName: existingName,\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: existingName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          }\n        }\n      }\n    })\n\n    // Save fixed data back to localStorage\n    localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems))\n\n    // Save updated batches if any were created\n    if (createdBatchesCount > 0) {\n      localStorage.setItem('medicine_batches', JSON.stringify(batches))\n      console.log(`📋 تم حفظ ${createdBatchesCount} دفعة جديدة`)\n    }\n\n    console.log(`✅ تم إصلاح ${fixedCount} عنصر من أصل ${salesItems.length}`)\n    console.log(`⚠️ لم يتم العثور على ${notFoundCount} عنصر`)\n\n    return {\n      success: true,\n      fixedCount,\n      notFoundCount,\n      createdBatchesCount,\n      totalCount: salesItems.length\n    }\n\n  } catch (error) {\n    console.error('❌ خطأ في إصلاح بيانات localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Enhanced function to clean and fix all localStorage data\nexport const cleanAndFixAllLocalStorageData = () => {\n  try {\n    console.log('🧹 بدء تنظيف وإصلاح جميع بيانات localStorage...')\n\n    // Step 1: Remove invalid or orphaned items\n    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n    const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n\n    console.log(`📊 البيانات الحالية:`)\n    console.log(`📦 عناصر الفواتير: ${salesItems.length}`)\n    console.log(`💊 الأدوية: ${medicines.length}`)\n    console.log(`📋 الدفعات: ${batches.length}`)\n    console.log(`🧾 الفواتير: ${invoices.length}`)\n\n    // Step 2: Remove duplicate items\n    const uniqueItems = salesItems.filter((item: any, index: number, self: any[]) =>\n      index === self.findIndex((i: any) => i.id === item.id)\n    )\n\n    // Step 3: Remove items with invalid invoice_id\n    const validInvoiceIds = new Set(invoices.map((inv: any) => inv.id))\n    const itemsWithValidInvoices = uniqueItems.filter((item: any) =>\n      validInvoiceIds.has(item.invoice_id)\n    )\n\n    // Step 4: Fix medicine batch references\n    const result = fixLocalStorageInvoiceItems()\n\n    // Step 5: Clean up orphaned batches (batches without medicines)\n    const validMedicineIds = new Set(medicines.map((med: any) => med.id))\n    const cleanBatches = batches.filter((batch: any) =>\n      validMedicineIds.has(batch.medicine_id)\n    )\n\n    // Save cleaned data\n    localStorage.setItem('sales_invoice_items', JSON.stringify(itemsWithValidInvoices))\n    localStorage.setItem('medicine_batches', JSON.stringify(cleanBatches))\n\n    const removedItems = salesItems.length - itemsWithValidInvoices.length\n    const removedBatches = batches.length - cleanBatches.length\n\n    console.log(`🧹 تم تنظيف البيانات:`)\n    console.log(`❌ تم حذف ${removedItems} عنصر مكرر أو غير صالح`)\n    console.log(`❌ تم حذف ${removedBatches} دفعة يتيمة`)\n    console.log(`✅ تم الاحتفاظ بـ ${itemsWithValidInvoices.length} عنصر صالح`)\n\n    return {\n      success: true,\n      removedItems,\n      removedBatches,\n      remainingItems: itemsWithValidInvoices.length,\n      fixResult: result\n    }\n\n  } catch (error) {\n    console.error('❌ خطأ في تنظيف البيانات:', error)\n    return { success: false, error }\n  }\n}\n\n// Function to create sample medicines if none exist\nexport const createSampleMedicinesIfNeeded = () => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n\n    if (medicines.length === 0) {\n      console.log('🏥 إنشاء أدوية تجريبية...')\n\n      const sampleMedicines = [\n        {\n          id: `medicine_${Date.now()}_1`,\n          name: 'باراسيتامول 500 مجم',\n          category: 'مسكنات',\n          manufacturer: 'شركة الأدوية المصرية',\n          active_ingredient: 'باراسيتامول',\n          strength: '500 مجم',\n          form: 'أقراص',\n          unit_price: 5.00,\n          selling_price: 8.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: `medicine_${Date.now()}_2`,\n          name: 'أموكسيسيلين 250 مجم',\n          category: 'مضادات حيوية',\n          manufacturer: 'شركة الأدوية العربية',\n          active_ingredient: 'أموكسيسيلين',\n          strength: '250 مجم',\n          form: 'كبسولات',\n          unit_price: 12.00,\n          selling_price: 18.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        },\n        {\n          id: `medicine_${Date.now()}_3`,\n          name: 'فيتامين سي 1000 مجم',\n          category: 'فيتامينات',\n          manufacturer: 'شركة الفيتامينات الطبيعية',\n          active_ingredient: 'حمض الأسكوربيك',\n          strength: '1000 مجم',\n          form: 'أقراص فوارة',\n          unit_price: 8.00,\n          selling_price: 12.00,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n      ]\n\n      localStorage.setItem('medicines', JSON.stringify(sampleMedicines))\n      console.log(`✅ تم إنشاء ${sampleMedicines.length} دواء تجريبي`)\n\n      // Create sample batches for these medicines\n      const sampleBatches = sampleMedicines.map((medicine, index) => ({\n        id: `batch_${Date.now()}_${index + 1}`,\n        medicine_id: medicine.id,\n        batch_code: `BATCH${String(index + 1).padStart(3, '0')}`,\n        expiry_date: new Date(Date.now() + (365 + index * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        quantity: 100 + index * 50,\n        cost_price: medicine.unit_price,\n        selling_price: medicine.selling_price,\n        supplier_id: null,\n        received_date: new Date().toISOString().split('T')[0],\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }))\n\n      localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))\n      console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)\n\n      return {\n        success: true,\n        medicinesCreated: sampleMedicines.length,\n        batchesCreated: sampleBatches.length\n      }\n    }\n\n    return { success: true, medicinesCreated: 0, batchesCreated: 0 }\n\n  } catch (error) {\n    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to get medicine name from batch ID\nexport const getMedicineNameFromBatch = (batchId: string): string => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    const batch = batches.find((b: any) => b.id === batchId)\n    const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n    return medicine?.name || 'غير محدد'\n  } catch (error) {\n    console.error('خطأ في الحصول على اسم الدواء:', error)\n    return 'غير محدد'\n  }\n}\n\n// Purchase operations\nexport const createPurchaseInvoice = async (invoiceData: {\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating purchase invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase invoice:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addPurchaseInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice items, using localStorage:', error)\n      // Fallback to localStorage\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding purchase invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Inventory movement operations\nexport const addInventoryMovement = async (movementData: {\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('inventory_movements')\n      .insert([movementData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for inventory movement, using localStorage:', error)\n      // Fallback to localStorage\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding inventory movement:', error)\n\n    // Final fallback to localStorage\n    try {\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for inventory movement:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Customer operations\nexport const getCustomers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching customers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addCustomer = async (customerData: {\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .insert([customerData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding customer:', error)\n    return { success: false, error }\n  }\n}\n\n// Supplier operations\nexport const getSuppliers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching suppliers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addSupplier = async (supplierData: {\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .insert([supplierData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding supplier:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete sales transaction with inventory update\nexport const completeSalesTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    console.log('🔄 بدء معاملة المبيعات الكاملة...')\n    console.log('📄 بيانات الفاتورة:', invoiceData)\n    console.log('📦 العناصر:', items)\n\n    // Start transaction by creating invoice\n    console.log('📝 إنشاء الفاتورة...')\n    const invoiceResult = await createSalesInvoice(invoiceData)\n    console.log('📝 نتيجة إنشاء الفاتورة:', invoiceResult)\n\n    if (!invoiceResult.success) {\n      console.error('❌ فشل في إنشاء الفاتورة:', invoiceResult.error)\n      throw new Error(`فشل في إنشاء الفاتورة: ${invoiceResult.error?.message || 'خطأ غير معروف'}`)\n    }\n\n    const invoiceId = invoiceResult.data.id\n    console.log('✅ تم إنشاء الفاتورة بنجاح، ID:', invoiceId)\n\n    // Process each item\n    console.log('📦 معالجة عناصر الفاتورة...')\n    const itemsToAdd = []\n\n    for (const item of items) {\n      console.log('📦 معالجة العنصر:', item)\n      const batchId = item.medicine_batch_id || item.batchId\n\n      // Prepare item for batch insert with medicine name\n      itemsToAdd.push({\n        invoice_id: invoiceId,\n        medicine_batch_id: batchId,\n        quantity: item.quantity,\n        unit_price: item.unit_price || item.unitPrice,\n        total_price: item.total_price || item.totalPrice,\n        is_gift: item.is_gift || item.isGift || false,\n        medicine_name: item.medicine_name || item.medicineName || 'غير محدد'\n      })\n\n      // Update batch quantity (only for non-gift items)\n      if (!(item.is_gift || item.isGift)) {\n        try {\n          const currentBatch = await supabase\n            .from('medicine_batches')\n            .select('quantity')\n            .eq('id', batchId)\n            .single()\n\n          if (currentBatch.data) {\n            const newQuantity = Math.max(0, currentBatch.data.quantity - item.quantity)\n            await updateBatchQuantity(batchId, newQuantity)\n            console.log(`✅ تم تحديث كمية الدفعة ${batchId} إلى ${newQuantity}`)\n          }\n        } catch (batchError) {\n          console.warn('تحذير: فشل في تحديث كمية الدفعة:', batchError)\n        }\n      }\n\n      // Add inventory movement\n      try {\n        await addInventoryMovement({\n          medicine_batch_id: batchId,\n          movement_type: 'out',\n          quantity: item.quantity,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          notes: (item.is_gift || item.isGift) ? 'هدية' : undefined\n        })\n        console.log(`✅ تم إضافة حركة المخزون للدفعة ${batchId}`)\n      } catch (movementError) {\n        console.warn('تحذير: فشل في إضافة حركة المخزون:', movementError)\n      }\n    }\n\n    // Add all invoice items in batch\n    console.log('📝 إضافة عناصر الفاتورة...')\n    const itemsResult = await addSalesInvoiceItems(itemsToAdd)\n    if (!itemsResult.success) {\n      console.warn('تحذير: فشل في إضافة عناصر الفاتورة:', itemsResult.error)\n    } else {\n      console.log('✅ تم إضافة جميع عناصر الفاتورة بنجاح')\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      try {\n        await addCashTransaction({\n          transaction_type: 'income',\n          category: 'مبيعات',\n          amount: invoiceData.final_amount,\n          description: `فاتورة مبيعات رقم ${invoiceData.invoice_number}`,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          payment_method: 'cash',\n          notes: invoiceData.notes\n        })\n        console.log('✅ تم إضافة معاملة الصندوق')\n      } catch (cashError) {\n        console.warn('تحذير: فشل في إضافة معاملة الصندوق:', cashError)\n      }\n    }\n\n    console.log('🎉 تمت معاملة المبيعات بنجاح!')\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('❌ خطأ في إتمام معاملة المبيعات:', error)\n    return { success: false, error }\n  }\n}\n\n// Returns operations\nexport const getSalesInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for sales invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names are available\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\n// Get single sales invoice with full details for printing\nexport const getSalesInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names - FORCE REFRESH\n        console.log('🔧 بدء تحسين عناصر الفاتورة للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n        console.log('💊 عدد الأدوية المتاحة:', medicines.length)\n        console.log('📋 عدد الدفعات المتاحة:', batches.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Find medicine name from batch - ALWAYS recalculate\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          console.log('الدفعة الموجودة:', batch)\n\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n          console.log('الدواء الموجود:', medicine)\n\n          // Get the best available medicine name\n          const medicineName = medicine?.name || 'غير محدد'\n          console.log('اسم الدواء المحسوب:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName, // Add both for compatibility\n            medicine_batches: {\n              id: batch?.id,\n              batch_code: batch?.batch_code || item.batch_code || '',\n              expiry_date: batch?.expiry_date || item.expiry_date || '',\n              medicine_id: batch?.medicine_id,\n              medicines: {\n                id: medicine?.id,\n                name: medicineName,\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع العناصر')\n        console.log('النتيجة النهائية:', itemsWithNames)\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            sales_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Get single purchase invoice with full details for printing\nexport const getPurchaseInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single purchase invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names\n        console.log('🔧 بدء تحسين عناصر فاتورة المشتريات للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Use existing medicine name if available\n          const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n          console.log('اسم الدواء:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicines: {\n              name: medicineName,\n              category: item.category || '',\n              manufacturer: item.manufacturer || '',\n              strength: item.strength || '',\n              form: item.form || ''\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع عناصر المشتريات')\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            purchase_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchaseInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for purchase invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\nexport const createSalesReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase sales return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `sr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('sales_returns', JSON.stringify(existingReturns))\n\n      console.log('Sales return saved to localStorage:', returnWithId)\n      console.log('Total sales returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating sales return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const createPurchaseReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  supplier_id: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase purchase return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `pr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('purchase_returns', JSON.stringify(existingReturns))\n\n      console.log('Purchase return saved to localStorage:', returnWithId)\n      console.log('Total purchase returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating purchase return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const addReturnItems = async (items: Array<{\n  return_id: string\n  return_type: 'sales' | 'purchase'\n  medicine_batch_id?: string\n  medicine_id?: string\n  quantity: number\n  unit_price: number\n  total_price: number\n}>) => {\n  try {\n    // Try Supabase first\n    const tableName = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n    const { data, error } = await supabase\n      .from(tableName)\n      .insert(items.map(item => ({\n        return_id: item.return_id,\n        medicine_batch_id: item.medicine_batch_id,\n        medicine_id: item.medicine_id,\n        quantity: item.quantity,\n        unit_price: item.unit_price,\n        total_price: item.total_price\n      })))\n      .select()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase return items failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n      const itemsWithIds = items.map(item => ({\n        ...item,\n        id: `ri_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }))\n\n      const existingItems = JSON.parse(localStorage.getItem(storageKey) || '[]')\n      existingItems.push(...itemsWithIds)\n      localStorage.setItem(storageKey, JSON.stringify(existingItems))\n\n      return { success: true, data: itemsWithIds }\n    } catch (fallbackError) {\n      console.error('Error adding return items (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const processReturn = async (\n  returnType: 'sales' | 'purchase',\n  returnData: any,\n  items: any[]\n) => {\n  try {\n    // Create return record\n    const returnResult = returnType === 'sales'\n      ? await createSalesReturn(returnData)\n      : await createPurchaseReturn(returnData)\n\n    if (!returnResult.success) throw new Error('Failed to create return')\n\n    const returnId = returnResult.data.id\n\n    // Add return items\n    const returnItems = items.map(item => ({\n      return_id: returnId,\n      return_type: returnType,\n      medicine_batch_id: item.batchId,\n      medicine_id: item.medicineId,\n      quantity: item.quantity,\n      unit_price: item.unitPrice,\n      total_price: item.totalPrice\n    }))\n\n    await addReturnItems(returnItems)\n\n    // Try to update inventory (skip if Supabase is not available)\n    try {\n      // Update inventory for sales returns (add back to stock)\n      if (returnType === 'sales') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Add returned quantity back to stock\n                await updateBatchQuantity(item.batchId, batch.quantity + item.quantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'in',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مبيعات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n\n      // Update inventory for purchase returns (remove from stock)\n      if (returnType === 'purchase') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Remove returned quantity from stock\n                const newQuantity = Math.max(0, batch.quantity - item.quantity)\n                await updateBatchQuantity(item.batchId, newQuantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'out',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مشتريات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n    } catch (inventoryError) {\n      console.warn('Inventory update failed, but return was created successfully:', inventoryError)\n    }\n\n    return { success: true, data: { returnId } }\n  } catch (error) {\n    console.error('Error processing return:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getReturns = async () => {\n  // Always try localStorage first for faster response\n  try {\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    console.log('Loading returns from localStorage:', {\n      salesReturns: salesReturns.length,\n      purchaseReturns: purchaseReturns.length,\n      customers: customers.length,\n      suppliers: suppliers.length\n    })\n\n    // Enrich sales returns with customer data\n    const enrichedSalesReturns = salesReturns.map((returnItem: any) => {\n      const customer = customers.find((c: any) => c.id === returnItem.customer_id)\n      console.log(`Enriching sales return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'sales',\n        customers: customer ? {\n          name: customer.name,\n          phone: customer.phone,\n          address: customer.address\n        } : null,\n        customer_name: customer?.name || returnItem.customer_name || 'عميل غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // Enrich purchase returns with supplier data\n    const enrichedPurchaseReturns = purchaseReturns.map((returnItem: any) => {\n      const supplier = suppliers.find((s: any) => s.id === returnItem.supplier_id)\n      console.log(`Enriching purchase return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'purchase',\n        suppliers: supplier ? {\n          name: supplier.name,\n          phone: supplier.phone,\n          address: supplier.address\n        } : null,\n        supplier_name: supplier?.name || returnItem.supplier_name || 'مورد غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // If we have local data, return it immediately\n    if (enrichedSalesReturns.length > 0 || enrichedPurchaseReturns.length > 0) {\n      const allReturns = [\n        ...enrichedSalesReturns,\n        ...enrichedPurchaseReturns\n      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      console.log('Returning enriched returns from localStorage:', allReturns.slice(0, 2))\n      return { success: true, data: allReturns }\n    }\n  } catch (localError) {\n    console.warn('Error reading from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for returns...')\n    const [salesReturns, purchaseReturns] = await Promise.all([\n      supabase\n        .from('sales_returns')\n        .select(`\n          *,\n          customers (name, phone),\n          sales_return_items (\n            *,\n            medicine_batches (\n              batch_code,\n              medicines (name)\n            )\n          )\n        `)\n        .order('created_at', { ascending: false }),\n\n      supabase\n        .from('purchase_returns')\n        .select(`\n          *,\n          suppliers (name, contact_person),\n          purchase_return_items (\n            *,\n            medicines (name)\n          )\n        `)\n        .order('created_at', { ascending: false })\n    ])\n\n    const allReturns = [\n      ...(salesReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'sales',\n        customer_name: item.customers?.name || 'عميل غير محدد'\n      })),\n      ...(purchaseReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'purchase',\n        supplier_name: item.suppliers?.name || 'مورد غير محدد'\n      }))\n    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    console.log('Returning returns from Supabase:', allReturns.slice(0, 2))\n    return { success: true, data: allReturns }\n  } catch (error) {\n    console.warn('Supabase returns failed, returning empty array:', error)\n\n    // Return empty array if both localStorage and Supabase fail\n    return { success: true, data: [] }\n  }\n}\n\n// Get return by ID with full details\nexport const getReturnById = async (returnId: string) => {\n  try {\n    // Try localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    let foundReturn = salesReturns.find((r: any) => r.id === returnId)\n    let returnType = 'sales'\n\n    if (!foundReturn) {\n      foundReturn = purchaseReturns.find((r: any) => r.id === returnId)\n      returnType = 'purchase'\n    }\n\n    if (foundReturn) {\n      // Enrich with customer/supplier data\n      if (returnType === 'sales') {\n        const customer = customers.find((c: any) => c.id === foundReturn.customer_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'sales',\n          customers: customer ? {\n            name: customer.name,\n            phone: customer.phone,\n            address: customer.address\n          } : null,\n          customer_name: customer?.name || foundReturn.customer_name || 'عميل غير محدد'\n        }\n      } else {\n        const supplier = suppliers.find((s: any) => s.id === foundReturn.supplier_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'purchase',\n          suppliers: supplier ? {\n            name: supplier.name,\n            phone: supplier.phone,\n            address: supplier.address\n          } : null,\n          supplier_name: supplier?.name || foundReturn.supplier_name || 'مورد غير محدد'\n        }\n      }\n\n      console.log('Found enriched return in localStorage:', foundReturn)\n      return { success: true, data: foundReturn }\n    }\n\n    // If not in localStorage, try Supabase\n    if (!supabase) {\n      console.warn('Supabase not available, return not found')\n      return { success: false, error: 'Return not found' }\n    }\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            *,\n            medicines (name)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        return_type: 'sales',\n        return_items: salesReturn.sales_return_items || []\n      }\n      console.log('Found sales return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        return_type: 'purchase',\n        return_items: purchaseReturn.purchase_return_items || []\n      }\n      console.log('Found purchase return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.warn('Return not found:', returnId)\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error getting return by ID:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete purchase transaction with inventory update\nexport const completePurchaseTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    // Create purchase invoice\n    const invoiceResult = await createPurchaseInvoice(invoiceData)\n    if (!invoiceResult.success) throw new Error('Failed to create purchase invoice')\n\n    const invoiceId = invoiceResult.data.id\n\n    // Process each item\n    for (const item of items) {\n      let medicineId = item.medicineId\n\n      // If medicine doesn't exist, create it\n      if (!medicineId) {\n        const newMedicineResult = await addMedicine({\n          name: item.medicineName,\n          category: item.category || 'أخرى',\n          manufacturer: item.manufacturer || '',\n          active_ingredient: item.activeIngredient || '',\n          strength: item.strength || '',\n          form: item.form || 'tablet',\n          unit_price: item.unitCost,\n          selling_price: item.sellingPrice || item.unitCost * 1.5\n        })\n\n        if (newMedicineResult.success) {\n          medicineId = newMedicineResult.data.id\n        } else {\n          console.error('Failed to create medicine:', newMedicineResult.error)\n          continue\n        }\n      }\n\n      // Add purchase invoice item\n      await addPurchaseInvoiceItems([{\n        invoice_id: invoiceId,\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        quantity: item.quantity,\n        unit_cost: item.unitCost,\n        total_cost: item.totalCost,\n        expiry_date: item.expiryDate,\n        medicine_name: item.medicineName || 'غير محدد'\n      }])\n\n      // Create or update medicine batch\n      const batchResult = await addMedicineBatch({\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        expiry_date: item.expiryDate,\n        quantity: item.quantity,\n        cost_price: item.unitCost,\n        selling_price: item.sellingPrice || item.unitCost * 1.5, // Default markup\n        supplier_id: invoiceData.supplier_id\n      })\n\n      if (batchResult.success) {\n        // Add inventory movement\n        await addInventoryMovement({\n          medicine_batch_id: batchResult.data.id,\n          movement_type: 'in',\n          quantity: item.quantity,\n          reference_type: 'purchase',\n          reference_id: invoiceId\n        })\n      }\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      await addCashTransaction({\n        transaction_type: 'expense',\n        category: 'مشتريات',\n        amount: invoiceData.final_amount,\n        description: `فاتورة مشتريات رقم ${invoiceData.invoice_number}`,\n        reference_type: 'purchase',\n        reference_id: invoiceId,\n        payment_method: 'cash',\n        notes: invoiceData.notes\n      })\n    }\n\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('Error completing purchase transaction:', error)\n    return { success: false, error }\n  }\n}\n\n// Cash Box Operations\nexport const addCashTransaction = async (transactionData: {\n  transaction_type: 'income' | 'expense'\n  category: string\n  amount: number\n  description: string\n  reference_type?: string\n  reference_id?: string\n  payment_method: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .insert([transactionData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transaction failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const transactionWithId = {\n        ...transactionData,\n        id: `ct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingTransactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n      existingTransactions.push(transactionWithId)\n      localStorage.setItem('cash_transactions', JSON.stringify(existingTransactions))\n\n      console.log('Cash transaction saved to localStorage:', transactionWithId)\n      console.log('Total cash transactions in localStorage:', existingTransactions.length)\n\n      return { success: true, data: transactionWithId }\n    } catch (fallbackError) {\n      console.error('Error adding cash transaction (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const getCashTransactions = async (filters?: {\n  start_date?: string\n  end_date?: string\n  transaction_type?: string\n  category?: string\n}) => {\n  // Always try localStorage first for faster response\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    // If we have local data, filter and return it\n    if (transactions.length > 0) {\n      console.log('Loading cash transactions from localStorage:', transactions.length)\n\n      let filteredTransactions = transactions\n\n      if (filters?.start_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at >= filters.start_date)\n      }\n      if (filters?.end_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at <= filters.end_date)\n      }\n      if (filters?.transaction_type) {\n        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === filters.transaction_type)\n      }\n      if (filters?.category) {\n        filteredTransactions = filteredTransactions.filter(t => t.category === filters.category)\n      }\n\n      // Sort by created_at descending\n      filteredTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      return { success: true, data: filteredTransactions }\n    }\n  } catch (localError) {\n    console.warn('Error reading cash transactions from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash transactions...')\n    let query = supabase\n      .from('cash_transactions')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (filters?.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters?.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters?.transaction_type) {\n      query = query.eq('transaction_type', filters.transaction_type)\n    }\n    if (filters?.category) {\n      query = query.eq('category', filters.category)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transactions failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getCashBalance = async () => {\n  // Try localStorage first\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    if (transactions.length > 0) {\n      console.log('Calculating cash balance from localStorage:', transactions.length, 'transactions')\n\n      const balance = transactions.reduce((total: number, transaction: any) => {\n        return transaction.transaction_type === 'income'\n          ? total + transaction.amount\n          : total - transaction.amount\n      }, 0)\n\n      return { success: true, data: balance }\n    }\n  } catch (localError) {\n    console.warn('Error calculating balance from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash balance...')\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .select('transaction_type, amount')\n\n    if (error) throw error\n\n    const balance = data.reduce((total, transaction) => {\n      return transaction.transaction_type === 'income'\n        ? total + transaction.amount\n        : total - transaction.amount\n    }, 0)\n\n    return { success: true, data: balance }\n  } catch (error) {\n    console.warn('Supabase cash balance failed, returning 0:', error)\n    return { success: true, data: 0 }\n  }\n}\n\n// Customer and Supplier Debts\nexport const getCustomerDebts = async () => {\n  // Try localStorage first\n  try {\n    const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n\n    if (salesInvoices.length > 0) {\n      console.log('Loading customer debts from localStorage:', salesInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = salesInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found customer debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No sales invoices found, creating sample customer debts')\n      const sampleDebts = [\n        {\n          id: 'debt_1',\n          invoice_number: 'INV-001',\n          customer_id: 'cust_1',\n          customer_name: 'أحمد محمد علي',\n          final_amount: 150000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'أحمد محمد علي', phone: '07901111111' }\n        },\n        {\n          id: 'debt_2',\n          invoice_number: 'INV-003',\n          customer_id: 'cust_2',\n          customer_name: 'فاطمة حسن محمد',\n          final_amount: 85000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'فاطمة حسن محمد', phone: '07802222222' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading customer debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for customer debts...')\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        customer_id,\n        customer_name,\n        final_amount,\n        payment_status,\n        created_at,\n        customers (name, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase customer debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getSupplierDebts = async () => {\n  // Try localStorage first\n  try {\n    const purchaseInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n\n    if (purchaseInvoices.length > 0) {\n      console.log('Loading supplier debts from localStorage:', purchaseInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = purchaseInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found supplier debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No purchase invoices found, creating sample supplier debts')\n      const sampleDebts = [\n        {\n          id: 'debt_3',\n          invoice_number: 'PUR-001',\n          supplier_id: 'sup_1',\n          final_amount: 2500000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' }\n        },\n        {\n          id: 'debt_4',\n          invoice_number: 'PUR-004',\n          supplier_id: 'sup_2',\n          final_amount: 1800000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading supplier debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for supplier debts...')\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        supplier_id,\n        final_amount,\n        payment_status,\n        created_at,\n        suppliers (name, contact_person, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase supplier debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const updatePaymentStatus = async (\n  invoiceType: 'sales' | 'purchase',\n  invoiceId: string,\n  paymentStatus: string,\n  paidAmount?: number\n) => {\n  try {\n    // Try Supabase first\n    const tableName = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n\n    const { data, error } = await supabase\n      .from(tableName)\n      .update({\n        payment_status: paymentStatus,\n        ...(paidAmount && { paid_amount: paidAmount })\n      })\n      .eq('id', invoiceId)\n      .select()\n      .single()\n\n    if (error) throw error\n\n    // Add cash transaction if payment is completed\n    if (paymentStatus === 'paid' && paidAmount) {\n      const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n      const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n      await addCashTransaction({\n        transaction_type: transactionType,\n        category,\n        amount: paidAmount,\n        description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${data.invoice_number}`,\n        reference_type: invoiceType,\n        reference_id: invoiceId,\n        payment_method: 'cash'\n      })\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase payment update failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n      const invoices = JSON.parse(localStorage.getItem(storageKey) || '[]')\n\n      const invoiceIndex = invoices.findIndex((inv: any) => inv.id === invoiceId)\n      if (invoiceIndex !== -1) {\n        invoices[invoiceIndex].payment_status = paymentStatus\n        if (paidAmount) {\n          invoices[invoiceIndex].paid_amount = paidAmount\n        }\n\n        localStorage.setItem(storageKey, JSON.stringify(invoices))\n\n        // Add cash transaction if payment is completed\n        if (paymentStatus === 'paid' && paidAmount) {\n          const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n          const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n          await addCashTransaction({\n            transaction_type: transactionType,\n            category,\n            amount: paidAmount,\n            description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${invoices[invoiceIndex].invoice_number}`,\n            reference_type: invoiceType,\n            reference_id: invoiceId,\n            payment_method: 'cash'\n          })\n        }\n\n        console.log('Payment status updated in localStorage:', invoices[invoiceIndex])\n        return { success: true, data: invoices[invoiceIndex] }\n      } else {\n        throw new Error('Invoice not found in localStorage')\n      }\n    } catch (fallbackError) {\n      console.error('Error updating payment status (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\n// Advanced Reports Functions\nexport const getSalesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  customer_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            medicines (name, category)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.customer_id) {\n      query = query.eq('customer_id', filters.customer_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.sales_invoice_items.some((item: any) =>\n          item.medicine_batches?.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching sales report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchasesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  supplier_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone),\n        purchase_invoice_items (\n          *,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.supplier_id) {\n      query = query.eq('supplier_id', filters.supplier_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.purchase_invoice_items.some((item: any) =>\n          item.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching purchases report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getCustomerStatement = async (customerId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let salesQuery = supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('sales_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      salesQuery = salesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      salesQuery = salesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [salesResult, returnsResult] = await Promise.all([\n      salesQuery,\n      returnsQuery\n    ])\n\n    if (salesResult.error) throw salesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        sales: salesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching customer statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getSupplierStatement = async (supplierId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let purchasesQuery = supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('purchase_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      purchasesQuery = purchasesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      purchasesQuery = purchasesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [purchasesResult, returnsResult] = await Promise.all([\n      purchasesQuery,\n      returnsQuery\n    ])\n\n    if (purchasesResult.error) throw purchasesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        purchases: purchasesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching supplier statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicineMovementReport = async (medicineId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let query = supabase\n      .from('inventory_movements')\n      .select(`\n        *,\n        medicine_batches (\n          batch_code,\n          expiry_date,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine\n    const filteredData = data.filter(movement =>\n      movement.medicine_batches?.medicines?.id === medicineId\n    )\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching medicine movement report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getInventoryReport = async (filters: {\n  category?: string\n  low_stock?: boolean\n  expired?: boolean\n  expiring_soon?: boolean\n}) => {\n  try {\n    let query = supabase\n      .from('medicine_batches')\n      .select(`\n        *,\n        medicines (name, category, manufacturer)\n      `)\n      .order('expiry_date', { ascending: true })\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    let filteredData = data\n\n    // Filter by category\n    if (filters.category) {\n      filteredData = filteredData.filter(batch =>\n        batch.medicines?.category === filters.category\n      )\n    }\n\n    // Filter by low stock (less than 10 units)\n    if (filters.low_stock) {\n      filteredData = filteredData.filter(batch => batch.quantity < 10)\n    }\n\n    // Filter by expired\n    if (filters.expired) {\n      const today = new Date().toISOString().split('T')[0]\n      filteredData = filteredData.filter(batch => batch.expiry_date < today)\n    }\n\n    // Filter by expiring soon (within 30 days)\n    if (filters.expiring_soon) {\n      const thirtyDaysFromNow = new Date()\n      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)\n      const futureDate = thirtyDaysFromNow.toISOString().split('T')[0]\n      const today = new Date().toISOString().split('T')[0]\n\n      filteredData = filteredData.filter(batch =>\n        batch.expiry_date >= today && batch.expiry_date <= futureDate\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching inventory report:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status (simplified function)\nexport const updateReturnStatus = async (returnId: string, status: string, rejectionReason?: string) => {\n  const updates: any = { status }\n  if (rejectionReason) {\n    updates.rejection_reason = rejectionReason\n  }\n  return updateReturn(returnId, updates)\n}\n\n// Get return for printing with full details\nexport const getReturnForPrint = async (returnId: string) => {\n  try {\n    console.log('🔍 البحث عن المرتجع للطباعة:', returnId)\n\n    // Check localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const salesReturnItems = JSON.parse(localStorage.getItem('sales_return_items') || '[]')\n    const purchaseReturnItems = JSON.parse(localStorage.getItem('purchase_return_items') || '[]')\n\n    // Find in sales returns\n    let foundReturn = salesReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = salesReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const medicineBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n        let batchCode = item.batch_code || item.batchCode || ''\n        let expiryDate = item.expiry_date || item.expiryDate || ''\n\n        // Try to get medicine name from batch if not available\n        if (medicineName === 'غير محدد' && item.medicine_batch_id) {\n          const batch = medicineBatches.find((b: any) => b.id === item.medicine_batch_id)\n          if (batch) {\n            batchCode = batch.batch_number || batchCode\n            expiryDate = batch.expiry_date || expiryDate\n\n            const medicine = medicines.find((m: any) => m.id === batch.medicine_id)\n            if (medicine) {\n              medicineName = medicine.name || medicineName\n            }\n          }\n        }\n\n        // Try to get medicine name directly if still not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: batchCode,\n          expiry_date: expiryDate,\n          unit_price: item.unit_price || item.unitPrice || 0,\n          total_price: item.total_price || item.totalPrice || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Find in purchase returns\n    foundReturn = purchaseReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = purchaseReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        // Try to get medicine name directly if not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: item.batch_code || item.batchCode || '',\n          expiry_date: item.expiry_date || item.expiryDate || '',\n          unit_cost: item.unit_cost || item.unitCost || 0,\n          total_cost: item.total_cost || item.totalCost || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // If not found in localStorage, try Supabase\n    console.log('⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...')\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: (salesReturn.sales_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_price: item.unit_price || 0,\n          total_price: item.total_price || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: (purchaseReturn.purchase_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_cost: item.unit_cost || 0,\n          total_cost: item.total_cost || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.log('❌ لم يتم العثور على المرتجع')\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error fetching return for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status\nexport const updateReturn = async (returnId: string, updates: { status?: string, rejection_reason?: string }) => {\n  try {\n    // Update in localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n\n    // Find and update in sales returns\n    const salesIndex = salesReturns.findIndex((ret: any) => ret.id === returnId)\n    if (salesIndex !== -1) {\n      salesReturns[salesIndex] = { ...salesReturns[salesIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('sales_returns', JSON.stringify(salesReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('sales_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: salesReturns[salesIndex] }\n    }\n\n    // Find and update in purchase returns\n    const purchaseIndex = purchaseReturns.findIndex((ret: any) => ret.id === returnId)\n    if (purchaseIndex !== -1) {\n      purchaseReturns[purchaseIndex] = { ...purchaseReturns[purchaseIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('purchase_returns', JSON.stringify(purchaseReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('purchase_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: purchaseReturns[purchaseIndex] }\n    }\n\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error updating return:', error)\n    return { success: false, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,cAAc,OAAO;IAUhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;gBACP,MAAM,aAAa,IAAI;gBACvB,UAAU,aAAa,QAAQ;gBAC/B,cAAc,aAAa,YAAY,IAAI;gBAC3C,mBAAmB,aAAa,iBAAiB,IAAI;gBACrD,UAAU,aAAa,QAAQ,IAAI;gBACnC,MAAM,aAAa,IAAI;gBACvB,YAAY,aAAa,UAAU;gBACnC,eAAe,aAAa,aAAa;YAC3C;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;;;MAYT,CAAC,EACA,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,0DAA0D;YACvE,2BAA2B;YAC3B,OAAO;QACT;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,iCAAiC;QACjC,OAAO;IACT;AACF;AAEA,qDAAqD;AACrD,MAAM,+BAA+B;IACnC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,sDAAsD;QACtD,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,uCAAuC;QACvC,MAAM,uBAAuB,UAAU,GAAG,CAAC,CAAC,WAAkB,CAAC;gBAC7D,GAAG,QAAQ;gBACX,kBAAkB,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBAClF,SAAS,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YAC3E,CAAC;QAED,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,qBAAqB,MAAM,CAAC,qBAAqB,CAAC;QAC5E,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEA,kDAAkD;AAClD,MAAM,4BAA4B;IAChC,IAAI;QACF,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,MAAM,gBAAgB;YACpB;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,0BAA0B;QAC1B,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,uBAAuB;QACvB,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QACxD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QAEjD,uCAAuC;QACvC,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,EAAE;QACxD,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC,EAAE;QAE7D,uCAAuC;QACvC,MAAM,uBAAuB,gBAAgB,GAAG,CAAC,CAAC,WAAkB,CAAC;gBACnE,GAAG,QAAQ;gBACX,kBAAkB,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBACxF,SAAS,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YACjF,CAAC;QAED,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,qBAAqB,MAAM,CAAC,YAAY,CAAC;QACnE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC,aAAa,CAAC;QAC7D,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;QAC9D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,uBAAuB;IAClC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,IAAI,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,UAAU,MAAM,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,KAAK,CAAC;QAC1F,OAAO;YAAE,SAAS;YAAM,MAAM;QAAU;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB,OAAO;IASrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAU,EAClB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB,OAAO,SAAiB;IACzD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,UAAU;QAAY,GAC/B,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,+DAA+D;YAC5E,2BAA2B;YAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,qBAAqB,OAAO;IAYvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,uCAAuC;YACpD,2BAA2B;YAC3B,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACpF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACpF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,yDAAyD;YACtE,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,6DAA6D;gBAC7D,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;gBAE5D,IAAI,gBAAgB,iBAAiB,YAAY;oBAC/C,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,cAAc;oBAC3D,OAAO;wBACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;wBACnE,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,YAAY;4BACZ,aAAa;4BACb,WAAW;gCACT,MAAM;gCACN,UAAU;gCACV,cAAc;gCACd,UAAU;gCACV,MAAM;4BACR;wBACF;wBACA,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;oBACpD,gDAAgD;oBAChD,OAAO;gBACT;YACF;YAEA,+CAA+C;YAC/C,MAAM,0BAA0B,cAAc,MAAM,CAAC,CAAA,OACnD,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK;YAGhD,IAAI,aAAa;YACjB,IAAI,wBAAwB,MAAM,GAAG,GAAG;gBACtC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,wBAAwB,MAAM,CAAC,uBAAuB,CAAC;gBAC/E,MAAM,iBAAiB,MAAM,8BAA8B;gBAE3D,wCAAwC;gBACxC,aAAa,cAAc,GAAG,CAAC,CAAA;oBAC7B,IAAI,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,YAAY;wBAC5D,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,KAAK,iBAAiB;wBACxF,OAAO,YAAY;oBACrB;oBACA,OAAO;gBACT;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAEhE,OAAO;oBACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACnE,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY;wBACZ,aAAa;wBACb,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;oBACA,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAc;QAC9C,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEA,uDAAuD;AACvD,MAAM,gCAAgC,OAAO;IAC3C,IAAI;QACF,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,qEAAqE;YACrE,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;YAE1D,IAAI,CAAC,gBAAgB,iBAAiB,YAAY;gBAChD,gCAAgC;gBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;gBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;gBAC5D,eAAe,UAAU,QAAQ;YACnC;YAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;YAE5D,OAAO;gBACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACnE,GAAG,IAAI;gBACP,eAAe;gBACf,cAAc;gBACd,kBAAkB;oBAChB,YAAY,OAAO,cAAc;oBACjC,aAAa,OAAO,eAAe;oBACnC,WAAW;wBACT,MAAM;wBACN,UAAU,UAAU,YAAY;wBAChC,cAAc,UAAU,gBAAgB;wBACxC,UAAU,UAAU,YAAY;wBAChC,MAAM,UAAU,QAAQ;oBAC1B;gBACF;gBACA,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,yDAAyD;QACzD,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACnE,GAAG,IAAI;gBACP,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC,CAAC;IACH;AACF;AAGO,MAAM,8BAA8B;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,MAAM,EAAE;QACzD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU,MAAM,EAAE;QACjD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ,MAAM,EAAE;QAE/C,IAAI,aAAa;QACjB,IAAI,gBAAgB;QACpB,IAAI,sBAAsB;QAE1B,MAAM,aAAa,WAAW,GAAG,CAAC,CAAC;YACjC,qDAAqD;YACrD,IAAI,KAAK,gBAAgB,EAAE,WAAW,QAAQ,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY;gBACjG,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAI,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACpE,IAAI,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;YAE1D,oDAAoD;YACpD,IAAI,CAAC,SAAS,KAAK,iBAAiB,EAAE;gBACpC,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,iBAAiB,EAAE;gBAEpE,oDAAoD;gBACpD,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,YAAY;oBAC3D,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK,KAAK,aAAa;gBACrE;gBAEA,uEAAuE;gBACvE,IAAI,CAAC,YAAY,UAAU,MAAM,GAAG,GAAG;oBACrC,WAAW,SAAS,CAAC,EAAE;oBACvB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,SAAS,IAAI,EAAE;gBACzD;gBAEA,6CAA6C;gBAC7C,IAAI,UAAU;oBACZ,QAAQ;wBACN,IAAI,KAAK,iBAAiB;wBAC1B,aAAa,SAAS,EAAE;wBACxB,YAAY,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;wBACpD,aAAa,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC7G,UAAU,KAAK,QAAQ,IAAI;wBAC3B,YAAY,KAAK,UAAU,IAAI,SAAS,UAAU,IAAI;wBACtD,eAAe,KAAK,UAAU,IAAI,SAAS,aAAa,IAAI;wBAC5D,aAAa;wBACb,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrD,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEA,QAAQ,IAAI,CAAC;oBACb;oBACA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,UAAU,CAAC,SAAS,EAAE,SAAS,IAAI,EAAE;gBACnF;YACF;YAEA,IAAI,UAAU,MAAM;gBAClB;gBACA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,WAAW,CAAC,CAAC;gBAE5E,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe,SAAS,IAAI;oBAC5B,cAAc,SAAS,IAAI;oBAC3B,kBAAkB;wBAChB,YAAY,OAAO,cAAc,KAAK,UAAU,IAAI;wBACpD,aAAa,OAAO,eAAe,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM,SAAS,IAAI;4BACnB,UAAU,SAAS,QAAQ,IAAI;4BAC/B,cAAc,SAAS,YAAY,IAAI;4BACvC,UAAU,SAAS,QAAQ,IAAI;4BAC/B,MAAM,SAAS,IAAI,IAAI;wBACzB;oBACF;gBACF;YACF,OAAO;gBACL;gBACA,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,KAAK,iBAAiB,EAAE;gBAE3E,oCAAoC;gBACpC,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAChE,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY,OAAO,cAAc,KAAK,UAAU,IAAI;wBACpD,aAAa,OAAO,eAAe,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;QACF;QAEA,uCAAuC;QACvC,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAE3D,2CAA2C;QAC3C,IAAI,sBAAsB,GAAG;YAC3B,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,oBAAoB,WAAW,CAAC;QAC3D;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,aAAa,EAAE,WAAW,MAAM,EAAE;QACvE,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,cAAc,KAAK,CAAC;QAExD,OAAO;YACL,SAAS;YACT;YACA;YACA;YACA,YAAY,WAAW,MAAM;QAC/B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,iCAAiC;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,2CAA2C;QAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QACvE,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QAEtE,QAAQ,GAAG,CAAC,CAAC,oBAAoB,CAAC;QAClC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,MAAM,EAAE;QACrD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,MAAM,EAAE;QAC7C,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,EAAE;QAC3C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,MAAM,EAAE;QAE7C,iCAAiC;QACjC,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,MAAW,OAAe,OAC/D,UAAU,KAAK,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,EAAE;QAGvD,+CAA+C;QAC/C,MAAM,kBAAkB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,MAAa,IAAI,EAAE;QACjE,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAC,OACjD,gBAAgB,GAAG,CAAC,KAAK,UAAU;QAGrC,wCAAwC;QACxC,MAAM,SAAS;QAEf,gEAAgE;QAChE,MAAM,mBAAmB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC,MAAa,IAAI,EAAE;QACnE,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,QACnC,iBAAiB,GAAG,CAAC,MAAM,WAAW;QAGxC,oBAAoB;QACpB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC3D,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAExD,MAAM,eAAe,WAAW,MAAM,GAAG,uBAAuB,MAAM;QACtE,MAAM,iBAAiB,QAAQ,MAAM,GAAG,aAAa,MAAM;QAE3D,QAAQ,GAAG,CAAC,CAAC,qBAAqB,CAAC;QACnC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,sBAAsB,CAAC;QAC5D,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,eAAe,WAAW,CAAC;QACnD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,uBAAuB,MAAM,CAAC,UAAU,CAAC;QAEzE,OAAO;YACL,SAAS;YACT;YACA;YACA,gBAAgB,uBAAuB,MAAM;YAC7C,WAAW;QACb;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,gCAAgC;IAC3C,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YAEZ,MAAM,kBAAkB;gBACtB;oBACE,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;oBAC9B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA;oBACE,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;oBAC9B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA;oBACE,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,EAAE,CAAC;oBAC9B,MAAM;oBACN,UAAU;oBACV,cAAc;oBACd,mBAAmB;oBACnB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,eAAe;oBACf,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;aACD;YAED,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC;YAE9D,4CAA4C;YAC5C,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;oBAC9D,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,QAAQ,GAAG;oBACtC,aAAa,SAAS,EAAE;oBACxB,YAAY,CAAC,KAAK,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;oBACxD,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,MAAM,QAAQ,EAAE,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACxG,UAAU,MAAM,QAAQ;oBACxB,YAAY,SAAS,UAAU;oBAC/B,eAAe,SAAS,aAAa;oBACrC,aAAa;oBACb,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrD,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC,aAAa,CAAC;YAE7D,OAAO;gBACL,SAAS;gBACT,kBAAkB,gBAAgB,MAAM;gBACxC,gBAAgB,cAAc,MAAM;YACtC;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,kBAAkB;YAAG,gBAAgB;QAAE;IAEjE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QAChD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;QAE5D,OAAO,UAAU,QAAQ;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAGO,MAAM,wBAAwB,OAAO;IAW1C,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4DAA4D;YACzE,2BAA2B;YAC3B,MAAM,YAAY,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAC7F,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAC7F,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAU5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,kEAAkE;YAC/E,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,CAAC,cAAc,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBAC5E,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QAEtD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,CAAC,cAAc,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBAC5E,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,oDAAoD;YAClE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,uBAAuB,OAAO;IAQzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,8DAA8D;YAC3E,2BAA2B;YAC3B,MAAM,WAAW;gBACf,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACvE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,WAAW;gBACf,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACvE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,wDAAwD;YACtE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAOhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAQhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,OACtC,aACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,eAAe;QAE3B,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,mBAAmB;QAC/C,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,QAAQ,KAAK,CAAC,4BAA4B,cAAc,KAAK;YAC7D,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,cAAc,KAAK,EAAE,WAAW,iBAAiB;QAC7F;QAEA,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QACvC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,oBAAoB;QACpB,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,EAAE;QAErB,KAAK,MAAM,QAAQ,MAAO;YACxB,QAAQ,GAAG,CAAC,qBAAqB;YACjC,MAAM,UAAU,KAAK,iBAAiB,IAAI,KAAK,OAAO;YAEtD,mDAAmD;YACnD,WAAW,IAAI,CAAC;gBACd,YAAY;gBACZ,mBAAmB;gBACnB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS;gBAC7C,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU;gBAChD,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI;gBACxC,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;YAC5D;YAEA,kDAAkD;YAClD,IAAI,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;gBAClC,IAAI;oBACF,MAAM,eAAe,MAAM,sHAAA,CAAA,WAAQ,CAChC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SACT,MAAM;oBAET,IAAI,aAAa,IAAI,EAAE;wBACrB,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,aAAa,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;wBAC1E,MAAM,oBAAoB,SAAS;wBACnC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,KAAK,EAAE,aAAa;oBACpE;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,oCAAoC;gBACnD;YACF;YAEA,yBAAyB;YACzB,IAAI;gBACF,MAAM,qBAAqB;oBACzB,mBAAmB;oBACnB,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;oBACd,OAAO,AAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAI,SAAS;gBAClD;gBACA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS;YACzD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,qCAAqC;YACpD;QACF;QAEA,iCAAiC;QACjC,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,qBAAqB;QAC/C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,QAAQ,IAAI,CAAC,uCAAuC,YAAY,KAAK;QACvE,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,IAAI;gBACF,MAAM,mBAAmB;oBACvB,kBAAkB;oBAClB,UAAU;oBACV,QAAQ,YAAY,YAAY;oBAChC,aAAa,CAAC,kBAAkB,EAAE,YAAY,cAAc,EAAE;oBAC9D,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,OAAO,YAAY,KAAK;gBAC1B;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,WAAW;gBAClB,QAAQ,IAAI,CAAC,uCAAuC;YACtD;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,4EAA4E;YAC5E,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;oBAC/B,IAAI,KAAK,gBAAgB,EAAE,WAAW,MAAM;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;oBAE5D,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,UAAU,QAAQ,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,OAAO,cAAc;4BACjC,aAAa,OAAO,eAAe;4BACnC,WAAW;gCACT,MAAM,UAAU,QAAQ,KAAK,aAAa,IAAI;gCAC9C,UAAU,UAAU,YAAY;gCAChC,cAAc,UAAU,gBAAgB;gCACxC,UAAU,UAAU,YAAY;gCAChC,MAAM,UAAU,QAAQ;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,8DAA8D;YAC9D,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;oBAC/B,IAAI,KAAK,gBAAgB,EAAE,WAAW,MAAM;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;oBAE5D,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,UAAU,QAAQ,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,OAAO,cAAc;4BACjC,aAAa,OAAO,eAAe;4BACnC,WAAW;gCACT,MAAM,UAAU,QAAQ,KAAK,aAAa,IAAI;gCAC9C,UAAU,UAAU,YAAY;gCAChC,cAAc,UAAU,gBAAgB;gCACxC,UAAU,UAAU,YAAY;gCAChC,MAAM,UAAU,QAAQ;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oDAAoD;gBACpD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAC3C,QAAQ,GAAG,CAAC,2BAA2B,UAAU,MAAM;gBACvD,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,MAAM;gBAErD,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC;oBAC3C,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,qDAAqD;oBACrD,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,OAAO;oBAC5D,QAAQ,GAAG,CAAC,mBAAmB;oBAE/B,uCAAuC;oBACvC,MAAM,eAAe,UAAU,QAAQ;oBACvC,QAAQ,GAAG,CAAC,uBAAuB;oBAEnC,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,IAAI,OAAO;4BACX,YAAY,OAAO,cAAc,KAAK,UAAU,IAAI;4BACpD,aAAa,OAAO,eAAe,KAAK,WAAW,IAAI;4BACvD,aAAa,OAAO;4BACpB,WAAW;gCACT,IAAI,UAAU;gCACd,MAAM;gCACN,UAAU,UAAU,YAAY;gCAChC,cAAc,UAAU,gBAAgB;gCACxC,UAAU,UAAU,YAAY;gCAChC,MAAM,UAAU,QAAQ;4BAC1B;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,qBAAqB;gBAEjC,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,qBAAqB;oBACvB;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4EAA4E;YACzF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAE3C,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC;oBAC3C,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,0CAA0C;oBAC1C,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;oBAChE,QAAQ,GAAG,CAAC,eAAe;oBAE3B,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,WAAW;4BACT,MAAM;4BACN,UAAU,KAAK,QAAQ,IAAI;4BAC3B,cAAc,KAAK,YAAY,IAAI;4BACnC,UAAU,KAAK,QAAQ,IAAI;4BAC3B,MAAM,KAAK,IAAI,IAAI;wBACrB;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,wBAAwB;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,sEAAsE;YACnF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IAUtC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACjE,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC5E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,QAAQ,GAAG,CAAC,uCAAuC;YACnD,QAAQ,GAAG,CAAC,wCAAwC,gBAAgB,MAAM;YAE1E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iEAAiE;QAE9E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACjE,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,QAAQ,GAAG,CAAC,0CAA0C;YACtD,QAAQ,GAAG,CAAC,2CAA2C,gBAAgB,MAAM;YAE7E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO;IASnC,IAAI;QACF,qBAAqB;QACrB,MAAM,YAAY,KAAK,CAAC,EAAE,EAAE,gBAAgB,UAAU,uBAAuB;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzB,WAAW,KAAK,SAAS;gBACzB,mBAAmB,KAAK,iBAAiB;gBACzC,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B,CAAC,IACA,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;YACF,MAAM,aAAa,KAAK,CAAC,EAAE,EAAE,gBAAgB,UAAU,uBAAuB;YAC9E,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtC,GAAG,IAAI;oBACP,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACjE,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YACrE,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,gBAAgB,OAC3B,YACA,YACA;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,eAAe,eAAe,UAChC,MAAM,kBAAkB,cACxB,MAAM,qBAAqB;QAE/B,IAAI,CAAC,aAAa,OAAO,EAAE,MAAM,IAAI,MAAM;QAE3C,MAAM,WAAW,aAAa,IAAI,CAAC,EAAE;QAErC,mBAAmB;QACnB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACrC,WAAW;gBACX,aAAa;gBACb,mBAAmB,KAAK,OAAO;gBAC/B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;YAC9B,CAAC;QAED,MAAM,eAAe;QAErB,8DAA8D;QAC9D,IAAI;YACF,yDAAyD;YACzD,IAAI,eAAe,SAAS;gBAC1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,oBAAoB,KAAK,OAAO,EAAE,MAAM,QAAQ,GAAG,KAAK,QAAQ;4BACxE;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,CAAC,eAAe,EAAE,WAAW,MAAM,EAAE;4BAC9C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;YAEA,4DAA4D;YAC5D,IAAI,eAAe,YAAY;gBAC7B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,GAAG,KAAK,QAAQ;gCAC9D,MAAM,oBAAoB,KAAK,OAAO,EAAE;4BAC1C;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,CAAC,gBAAgB,EAAE,WAAW,MAAM,EAAE;4BAC/C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,IAAI,CAAC,iEAAiE;QAChF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAS;QAAE;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,aAAa;IACxB,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,QAAQ,GAAG,CAAC,sCAAsC;YAChD,cAAc,aAAa,MAAM;YACjC,iBAAiB,gBAAgB,MAAM;YACvC,WAAW,UAAU,MAAM;YAC3B,WAAW,UAAU,MAAM;QAC7B;QAEA,0CAA0C;QAC1C,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAC;YAC7C,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;gBACtD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,WAAW,YAAY,EAAE,UAAU;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,UAAU,QAAQ,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,6CAA6C;QAC7C,MAAM,0BAA0B,gBAAgB,GAAG,CAAC,CAAC;YACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;gBACzD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,WAAW,YAAY,EAAE,UAAU;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,UAAU,QAAQ,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,+CAA+C;QAC/C,IAAI,qBAAqB,MAAM,GAAG,KAAK,wBAAwB,MAAM,GAAG,GAAG;YACzE,MAAM,aAAa;mBACd;mBACA;aACJ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAElF,QAAQ,GAAG,CAAC,iDAAiD,WAAW,KAAK,CAAC,GAAG;YACjF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,oCAAoC;IACnD;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACxD,sHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;;;;QAUT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,sHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;;;;;QAOT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;SAC3C;QAED,MAAM,aAAa;eACd,CAAC,aAAa,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACxC,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,KAAK,SAAS,EAAE,QAAQ;gBACzC,CAAC;eACE,CAAC,gBAAgB,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3C,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,KAAK,SAAS,EAAE,QAAQ;gBACzC,CAAC;SACF,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAElF,QAAQ,GAAG,CAAC,oCAAoC,WAAW,KAAK,CAAC,GAAG;QACpE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAW;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mDAAmD;QAEhE,4DAA4D;QAC5D,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,yBAAyB;QACzB,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACzD,IAAI,aAAa;QAEjB,IAAI,CAAC,aAAa;YAChB,cAAc,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;YACxD,aAAa;QACf;QAEA,IAAI,aAAa;YACf,qCAAqC;YACrC,IAAI,eAAe,SAAS;gBAC1B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,UAAU,QAAQ,YAAY,aAAa,IAAI;gBAChE;YACF,OAAO;gBACL,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,UAAU,QAAQ,YAAY,aAAa,IAAI;gBAChE;YACF;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YACtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;QAEA,uCAAuC;QACvC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmB;QACrD;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,aAAa;gBACb,cAAc,YAAY,kBAAkB,IAAI,EAAE;YACpD;YACA,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,aAAa;gBACb,cAAc,eAAe,qBAAqB,IAAI,EAAE;YAC1D;YACA,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,IAAI,CAAC,qBAAqB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,8BAA8B,OACzC,aACA;IAEA,IAAI;QACF,0BAA0B;QAC1B,MAAM,gBAAgB,MAAM,sBAAsB;QAClD,IAAI,CAAC,cAAc,OAAO,EAAE,MAAM,IAAI,MAAM;QAE5C,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QAEvC,oBAAoB;QACpB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,aAAa,KAAK,UAAU;YAEhC,uCAAuC;YACvC,IAAI,CAAC,YAAY;gBACf,MAAM,oBAAoB,MAAM,YAAY;oBAC1C,MAAM,KAAK,YAAY;oBACvB,UAAU,KAAK,QAAQ,IAAI;oBAC3B,cAAc,KAAK,YAAY,IAAI;oBACnC,mBAAmB,KAAK,gBAAgB,IAAI;oBAC5C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,QAAQ;oBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACtD;gBAEA,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,aAAa,kBAAkB,IAAI,CAAC,EAAE;gBACxC,OAAO;oBACL,QAAQ,KAAK,CAAC,8BAA8B,kBAAkB,KAAK;oBACnE;gBACF;YACF;YAEA,4BAA4B;YAC5B,MAAM,wBAAwB;gBAAC;oBAC7B,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,SAAS;oBAC1B,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,QAAQ;oBACxB,YAAY,KAAK,SAAS;oBAC1B,aAAa,KAAK,UAAU;oBAC5B,eAAe,KAAK,YAAY,IAAI;gBACtC;aAAE;YAEF,kCAAkC;YAClC,MAAM,cAAc,MAAM,iBAAiB;gBACzC,aAAa;gBACb,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,QAAQ;gBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACpD,aAAa,YAAY,WAAW;YACtC;YAEA,IAAI,YAAY,OAAO,EAAE;gBACvB,yBAAyB;gBACzB,MAAM,qBAAqB;oBACzB,mBAAmB,YAAY,IAAI,CAAC,EAAE;oBACtC,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;gBAChB;YACF;QACF;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB,UAAU;gBACV,QAAQ,YAAY,YAAY;gBAChC,aAAa,CAAC,mBAAmB,EAAE,YAAY,cAAc,EAAE;gBAC/D,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,OAAO,YAAY,KAAK;YAC1B;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO;IAUvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAgB,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kEAAkE;QAE/E,2BAA2B;QAC3B,IAAI;YACF,MAAM,oBAAoB;gBACxB,GAAG,eAAe;gBAClB,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACjE,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,uBAAuB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACrF,qBAAqB,IAAI,CAAC;YAC1B,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,QAAQ,GAAG,CAAC,2CAA2C;YACvD,QAAQ,GAAG,CAAC,4CAA4C,qBAAqB,MAAM;YAEnF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,sBAAsB,OAAO;IAMxC,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,8CAA8C;QAC9C,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,gDAAgD,aAAa,MAAM;YAE/E,IAAI,uBAAuB;YAE3B,IAAI,SAAS,YAAY;gBACvB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,UAAU;YAC5F;YACA,IAAI,SAAS,UAAU;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,QAAQ;YAC1F;YACA,IAAI,SAAS,kBAAkB;gBAC7B,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,QAAQ,gBAAgB;YACzG;YACA,IAAI,SAAS,UAAU;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;YACzF;YAEA,gCAAgC;YAChC,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAErG,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAqB;QACrD;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,sDAAsD;IACrE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,SAAS,YAAY;YACvB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,SAAS,UAAU;YACrB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,SAAS,kBAAkB;YAC7B,QAAQ,MAAM,EAAE,CAAC,oBAAoB,QAAQ,gBAAgB;QAC/D;QACA,IAAI,SAAS,UAAU;YACrB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;QAC/C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6DAA6D;QAC1E,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,iBAAiB;IAC5B,yBAAyB;IACzB,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,+CAA+C,aAAa,MAAM,EAAE;YAEhF,MAAM,UAAU,aAAa,MAAM,CAAC,CAAC,OAAe;gBAClD,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;YAChC,GAAG;YAEH,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,gDAAgD;IAC/D;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QAEjB,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,OAAO;YAClC,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;QAChC,GAAG;QAEH,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAE;IAClC;AACF;AAGO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QAE3E,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CAAC,6CAA6C,cAAc,MAAM,EAAE;YAE/E,mCAAmC;YACnC,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,UAC5C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAiB,OAAO;oBAAc;gBAC3D;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAkB,OAAO;oBAAc;gBAC5D;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;MAST,CAAC,EACA,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAEjF,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,QAAQ,GAAG,CAAC,6CAA6C,iBAAiB,MAAM,EAAE;YAElF,mCAAmC;YACnC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,UAC/C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAyB,gBAAgB;wBAAa,OAAO;oBAAc;gBAChG;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAsB,gBAAgB;wBAAa,OAAO;oBAAc;gBAC7F;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,sBAAsB,OACjC,aACA,WACA,eACA;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,YAAY,gBAAgB,UAAU,mBAAmB;QAE/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;YACN,gBAAgB;YAChB,GAAI,cAAc;gBAAE,aAAa;YAAW,CAAC;QAC/C,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,+CAA+C;QAC/C,IAAI,kBAAkB,UAAU,YAAY;YAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;YAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;YAEtD,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB;gBACA,QAAQ;gBACR,aAAa,CAAC,WAAW,EAAE,gBAAgB,UAAU,WAAW,UAAU,KAAK,EAAE,KAAK,cAAc,EAAE;gBACtG,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;YAClB;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gEAAgE;QAE7E,2BAA2B;QAC3B,IAAI;YACF,MAAM,aAAa,gBAAgB,UAAU,mBAAmB;YAChE,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YAEhE,MAAM,eAAe,SAAS,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YACjE,IAAI,iBAAiB,CAAC,GAAG;gBACvB,QAAQ,CAAC,aAAa,CAAC,cAAc,GAAG;gBACxC,IAAI,YAAY;oBACd,QAAQ,CAAC,aAAa,CAAC,WAAW,GAAG;gBACvC;gBAEA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,+CAA+C;gBAC/C,IAAI,kBAAkB,UAAU,YAAY;oBAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;oBAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;oBAEtD,MAAM,mBAAmB;wBACvB,kBAAkB;wBAClB;wBACA,QAAQ;wBACR,aAAa,CAAC,WAAW,EAAE,gBAAgB,UAAU,WAAW,UAAU,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,cAAc,EAAE;wBACxH,gBAAgB;wBAChB,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,QAAQ,GAAG,CAAC,2CAA2C,QAAQ,CAAC,aAAa;gBAC7E,OAAO;oBAAE,SAAS;oBAAM,MAAM,QAAQ,CAAC,aAAa;gBAAC;YACvD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IAMnC,IAAI;QACF,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAC,OAChC,KAAK,gBAAgB,EAAE,WAAW,OAAO,QAAQ,WAAW;QAGlE;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,sBAAsB,CAAC,IAAI,CAAC,CAAC,OACnC,KAAK,SAAS,EAAE,OAAO,QAAQ,WAAW;QAGhD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,aAAa,sHAAA,CAAA,WAAQ,CACtB,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,sHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,UAAU;YAC5D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAC1D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,aAAa,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrD;YACA;SACD;QAED,IAAI,YAAY,KAAK,EAAE,MAAM,YAAY,KAAK;QAC9C,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,OAAO,YAAY,IAAI;gBACvB,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,iBAAiB,sHAAA,CAAA,WAAQ,CAC1B,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,sHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,UAAU;YACpE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAClE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,iBAAiB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACzD;YACA;SACD;QAED,IAAI,gBAAgB,KAAK,EAAE,MAAM,gBAAgB,KAAK;QACtD,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,WAAW,gBAAgB,IAAI;gBAC/B,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,4BAA4B,OAAO,YAAoB;IAIlE,IAAI;QACF,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,uBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,qBAAqB;QACrB,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,WAC/B,SAAS,gBAAgB,EAAE,WAAW,OAAO;QAG/C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,eAAe;YAAE,WAAW;QAAK;QAE1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,IAAI,eAAe;QAEnB,qBAAqB;QACrB,IAAI,QAAQ,QAAQ,EAAE;YACpB,eAAe,aAAa,MAAM,CAAC,CAAA,QACjC,MAAM,SAAS,EAAE,aAAa,QAAQ,QAAQ;QAElD;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,SAAS,EAAE;YACrB,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,GAAG;QAC/D;QAEA,oBAAoB;QACpB,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG;QAClE;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,aAAa,EAAE;YACzB,MAAM,oBAAoB,IAAI;YAC9B,kBAAkB,OAAO,CAAC,kBAAkB,OAAO,KAAK;YACxD,MAAM,aAAa,kBAAkB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChE,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,eAAe,aAAa,MAAM,CAAC,CAAA,QACjC,MAAM,WAAW,IAAI,SAAS,MAAM,WAAW,IAAI;QAEvD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO,UAAkB,QAAgB;IACzE,MAAM,UAAe;QAAE;IAAO;IAC9B,IAAI,iBAAiB;QACnB,QAAQ,gBAAgB,GAAG;IAC7B;IACA,OAAO,aAAa,UAAU;AAChC;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,2BAA2B;QAC3B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,yBAAyB;QAClF,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,4BAA4B;QAExF,wBAAwB;QACxB,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC7D,IAAI,aAAa;YACf,MAAM,QAAQ,iBAAiB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAExE,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAE/E,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAC9D,IAAI,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACrD,IAAI,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBAExD,uDAAuD;gBACvD,IAAI,iBAAiB,cAAc,KAAK,iBAAiB,EAAE;oBACzD,MAAM,QAAQ,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBAC9E,IAAI,OAAO;wBACT,YAAY,MAAM,YAAY,IAAI;wBAClC,aAAa,MAAM,WAAW,IAAI;wBAElC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,MAAM,WAAW;wBACtE,IAAI,UAAU;4BACZ,eAAe,SAAS,IAAI,IAAI;wBAClC;oBACF;gBACF;gBAEA,2DAA2D;gBAC3D,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBACtD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,2BAA2B;QAC3B,cAAc,gBAAgB,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC5D,IAAI,aAAa;YACf,MAAM,QAAQ,oBAAoB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAE3E,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAElE,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAE9D,qDAAqD;gBACrD,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;oBACpD,WAAW,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI;oBAC9C,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACnD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;;;;;MAWT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,YAAY,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAc,CAAC;wBAC/E,GAAG,IAAI;wBACP,eAAe,KAAK,gBAAgB,EAAE,WAAW,QAAQ,KAAK,aAAa,IAAI;wBAC/E,YAAY,KAAK,UAAU,IAAI;wBAC/B,aAAa,KAAK,WAAW,IAAI;oBACnC,CAAC;YACH;YACA,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,eAAe,qBAAqB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAc,CAAC;wBACrF,GAAG,IAAI;wBACP,eAAe,KAAK,SAAS,EAAE,QAAQ,KAAK,aAAa,IAAI;wBAC7D,WAAW,KAAK,SAAS,IAAI;wBAC7B,YAAY,KAAK,UAAU,IAAI;oBACjC,CAAC;YACH;YACA,QAAQ,GAAG,CAAC,8CAA8C;YAC1D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAE/E,mCAAmC;QACnC,MAAM,aAAa,aAAa,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACnE,IAAI,eAAe,CAAC,GAAG;YACrB,YAAY,CAAC,WAAW,GAAG;gBAAE,GAAG,YAAY,CAAC,WAAW;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YAC3G,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,YAAY,CAAC,WAAW;YAAC;QACzD;QAEA,sCAAsC;QACtC,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACzE,IAAI,kBAAkB,CAAC,GAAG;YACxB,eAAe,CAAC,cAAc,GAAG;gBAAE,GAAG,eAAe,CAAC,cAAc;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YACvH,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,eAAe,CAAC,cAAc;YAAC;QAC/D;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 8403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/utils/larenPrintTemplate.ts"], "sourcesContent": ["import { PrintSettings } from '@/hooks/usePrintSettings'\n\nexport const generateLarenInvoiceHTML = (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {\n  let items, customerSupplier, documentTitle, documentNumber\n\n  if (type === 'return') {\n    items = invoice.return_invoice_items || invoice.sales_return_items || invoice.purchase_return_items || []\n    const returnType = invoice.type || invoice.return_type || 'sales'\n    customerSupplier = returnType === 'sales'\n      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')\n      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')\n    documentTitle = returnType === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات'\n    documentNumber = invoice.return_number || invoice.invoice_number || 'غير محدد'\n  } else {\n    items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items\n    customerSupplier = type === 'sales'\n      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')\n      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')\n    documentTitle = type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'\n    documentNumber = invoice.invoice_number || 'غير محدد'\n  }\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${documentTitle} - ${documentNumber}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n        \n        /* Header Section */\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .company-address {\n          font-size: 14px;\n          margin-bottom: 5px;\n        }\n        \n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .logo-text {\n          font-size: 16px;\n          font-weight: bold;\n          text-align: center;\n          line-height: 1.2;\n        }\n        \n        /* Invoice Details Section */\n        .invoice-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n        \n        .invoice-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n        \n        .customer-info {\n          border-right: none;\n        }\n        \n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n        \n        .detail-label {\n          font-weight: bold;\n          min-width: 80px;\n        }\n        \n        .detail-value {\n          text-align: left;\n        }\n        \n        /* Items Table */\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n        \n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n        \n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n        \n        /* Totals Section */\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n        \n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n        \n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n        \n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n        \n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n        \n        /* Footer Section */\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n        \n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n        \n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n        \n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n        \n        .notes-label {\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <!-- Header -->\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div class=\"company-address\">${settings.companyAddress}</div>\n          </div>\n          \n          <div class=\"logo-section\">\n            <div class=\"logo-text\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <!-- Document Details -->\n        <div class=\"invoice-details\">\n          <div class=\"invoice-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${type === 'return' ? 'رقم المرتجع:' : 'رقم الفاتورة:'}</span>\n              <span class=\"detail-value\">${documentNumber}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">التاريخ:</span>\n              <span class=\"detail-value\">${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</span>\n            </div>\n            ${type !== 'return' ? `\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">طريقة الدفع:</span>\n              <span class=\"detail-value\">${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</span>\n            </div>` : ''}\n            ${type === 'return' ? `\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">سبب المرتجع:</span>\n              <span class=\"detail-value\">${invoice.reason || 'غير محدد'}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الحالة:</span>\n              <span class=\"detail-value\">${invoice.status === 'approved' ? 'مقبول' : invoice.status === 'rejected' ? 'مرفوض' : 'قيد المراجعة'}</span>\n            </div>` : ''}\n            </div>\n          </div>\n          \n          <div class=\"customer-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase' ? 'المورد:' : 'العميل:')\n                  : (type === 'sales' ? 'العميل:' : 'المورد:')\n              }</span>\n              <span class=\"detail-value\">${customerSupplier}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الهاتف:</span>\n              <span class=\"detail-value\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                      ? (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || '')\n                      : (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || ''))\n                  : (type === 'sales'\n                      ? (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || '')\n                      : (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || ''))\n              }</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">العنوان:</span>\n              <span class=\"detail-value\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                      ? (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || '')\n                      : (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || ''))\n                  : (type === 'sales'\n                      ? (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || '')\n                      : (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || ''))\n              }</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Items Table -->\n        <table class=\"items-table\">\n          <thead>\n            <tr>\n              <th style=\"width: 40px;\">ت</th>\n              <th style=\"width: ${type === 'return' ? '150px' : '200px'};\">اسم المادة</th>\n              <th style=\"width: 60px;\">الكمية</th>\n              <th style=\"width: 80px;\">السعر</th>\n              <th style=\"width: 100px;\">المجموع</th>\n              ${type === 'return' ? '<th style=\"width: 100px;\">سبب المرتجع</th>' : ''}\n              <th style=\"width: 60px;\">EXP</th>\n              <th style=\"width: 60px;\">B.N</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any, index: number) => `\n              <tr>\n                <td>${index + 1}</td>\n                <td class=\"item-name\">${\n                  type === 'return'\n                    ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || item.medicines?.name || 'غير محدد')\n                    : type === 'sales'\n                      ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')\n                      : (item.medicine_name || item.medicineName || item.medicines?.name || item.name || 'غير محدد')\n                }</td>\n                <td>${item.quantity}</td>\n                <td>${\n                  type === 'return'\n                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                        ? (item.unit_cost || item.unitCost || 0).toLocaleString()\n                        : (item.unit_price || item.unitPrice || 0).toLocaleString())\n                    : type === 'sales'\n                      ? (item.unit_price || 0).toLocaleString()\n                      : (item.unit_cost || item.unitCost || 0).toLocaleString()\n                }</td>\n                <td>${\n                  type === 'return'\n                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                        ? (item.total_cost || item.totalCost || 0).toLocaleString()\n                        : (item.total_price || item.totalPrice || 0).toLocaleString())\n                    : type === 'sales'\n                      ? (item.total_price || 0).toLocaleString()\n                      : (item.total_cost || item.totalCost || 0).toLocaleString()\n                }</td>\n                ${type === 'return' ? `<td style=\"font-size: 10px;\">${item.return_reason || invoice.reason || 'غير محدد'}</td>` : ''}\n                <td>${\n                  type === 'return'\n                    ? (item.expiry_date || item.expiryDate || item.medicine_batches?.expiry_date\n                        ? new Date(item.expiry_date || item.expiryDate || item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/')\n                        : '')\n                    : type === 'sales'\n                      ? (item.medicine_batches?.expiry_date ? new Date(item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/') : '')\n                      : (item.expiry_date || item.expiryDate ? new Date(item.expiry_date || item.expiryDate).toLocaleDateString('en-GB').replace(/\\//g, '/') : '')\n                }</td>\n                <td>${\n                  type === 'return'\n                    ? (item.batch_code || item.batchCode || item.medicine_batches?.batch_number || '')\n                    : type === 'sales'\n                      ? (item.medicine_batches?.batch_number || '')\n                      : (item.batch_code || item.batchCode || '')\n                }</td>\n              </tr>\n            `).join('') || ''}\n            \n            <!-- Empty rows to fill space -->\n            ${Array.from({ length: Math.max(0, 10 - (items?.length || 0)) }, (_, i) => `\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                ${type === 'return' ? '<td>&nbsp;</td>' : ''}\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <!-- Totals -->\n        <div class=\"totals-section\">\n          <table>\n            ${type !== 'return' ? `\n            <tr>\n              <td class=\"total-label\">المجموع الفرعي:</td>\n              <td class=\"total-value\">${invoice.total_amount?.toLocaleString() || 0}</td>\n            </tr>\n            <tr>\n              <td class=\"total-label\">الخصم:</td>\n              <td class=\"total-value\">${invoice.discount_amount?.toLocaleString() || 0}</td>\n            </tr>\n            <tr style=\"background-color: #f0f0f0;\">\n              <td class=\"total-label\">المجموع النهائي:</td>\n              <td class=\"total-value\">${invoice.final_amount?.toLocaleString() || 0}</td>\n            </tr>` : `\n            <tr style=\"background-color: #f0f0f0;\">\n              <td class=\"total-label\">إجمالي المرتجع:</td>\n              <td class=\"total-value\">${invoice.total_amount?.toLocaleString() || 0}</td>\n            </tr>`}\n          </table>\n        </div>\n\n        <!-- Notes -->\n        <div class=\"notes-section\">\n          <div class=\"notes-label\">ملاحظات: ${invoice.notes || ''}</div>\n          ${type === 'return' && invoice.rejection_reason ? `\n          <div class=\"notes-label\" style=\"margin-top: 10px; color: #dc2626;\">سبب الرفض: ${invoice.rejection_reason}</div>` : ''}\n        </div>\n\n        <!-- Footer -->\n        <div class=\"footer\">\n          <div style=\"font-size: 12px;\">\n            صفحة 1 من 1\n          </div>\n          \n          <div class=\"signature-section\">\n            <div class=\"signature-box\">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n          \n          <div style=\"font-size: 12px; text-align: left;\">\n            ${settings.footerText || 'شكراً لتعاملكم معنا'}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n\n// دالة ترجمة أسماء الأعمدة\nconst translateColumnName = (columnName: string): string => {\n  const translations: { [key: string]: string } = {\n    'id': 'الرقم',\n    'name': 'الاسم',\n    'invoice_number': 'رقم الفاتورة',\n    'customer_name': 'اسم العميل',\n    'supplier_name': 'اسم المورد',\n    'total_amount': 'المبلغ الإجمالي',\n    'final_amount': 'المبلغ النهائي',\n    'payment_status': 'حالة الدفع',\n    'created_at': 'التاريخ',\n    'phone': 'الهاتف',\n    'address': 'العنوان',\n    'category': 'الفئة',\n    'quantity': 'الكمية',\n    'expiry_date': 'تاريخ الانتهاء',\n    'medicine_name': 'اسم الدواء',\n    'batch_code': 'رقم الدفعة',\n    'unit_price': 'سعر الوحدة',\n    'discount': 'الخصم',\n    'notes': 'ملاحظات'\n  }\n  return translations[columnName] || columnName\n}\n\n// دالة تنسيق قيم الخلايا\nconst formatCellValue = (value: any, columnName: string): string => {\n  if (value === null || value === undefined) return 'غير محدد'\n\n  // تنسيق التواريخ\n  if (columnName.includes('date') || columnName.includes('created_at')) {\n    try {\n      return new Date(value).toLocaleDateString('ar-EG')\n    } catch {\n      return value.toString()\n    }\n  }\n\n  // تنسيق المبالغ\n  if (columnName.includes('amount') || columnName.includes('price') || columnName.includes('cost')) {\n    const num = parseFloat(value)\n    return isNaN(num) ? value.toString() : `${num.toLocaleString()} د.ع`\n  }\n\n  // تنسيق حالة الدفع\n  if (columnName === 'payment_status') {\n    return value === 'paid' ? 'مدفوع' : value === 'pending' ? 'معلق' : value.toString()\n  }\n\n  return value.toString()\n}\n\nexport const generateLarenReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: 12px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n        }\n        \n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .logo-section {\n          width: 100px;\n          height: 100px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .report-title {\n          text-align: center;\n          font-size: 18px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 1px solid #000;\n          background-color: #f0f0f0;\n        }\n        \n        .report-date {\n          text-align: center;\n          margin-bottom: 20px;\n          font-size: 14px;\n        }\n        \n        table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        th, td {\n          border: 1px solid #000;\n          padding: 4px 6px;\n          text-align: right;\n          font-size: 10px;\n          vertical-align: top;\n          word-wrap: break-word;\n          max-width: 120px;\n        }\n\n        th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: center;\n          font-size: 9px;\n        }\n\n        tr:nth-child(even) {\n          background-color: #f9f9f9;\n        }\n\n        .number-cell {\n          text-align: center;\n          font-weight: bold;\n          width: 30px;\n        }\n        \n        .summary-section {\n          display: flex;\n          justify-content: space-around;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n          padding: 15px;\n        }\n        \n        .summary-item {\n          text-align: center;\n        }\n        \n        .summary-label {\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        \n        .summary-value {\n          font-size: 16px;\n          font-weight: bold;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div>${settings.companyAddress}</div>\n          </div>\n          \n          <div class=\"logo-section\">\n            <div style=\"font-size: 14px; font-weight: bold; text-align: center;\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class=\"report-title\">${title}</div>\n        <div class=\"report-date\">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>\n\n        ${Array.isArray(reportData) && reportData.length > 0 ? `\n          <div class=\"summary-section\">\n            <div class=\"summary-item\">\n              <div class=\"summary-label\">عدد السجلات</div>\n              <div class=\"summary-value\">${reportData.length}</div>\n            </div>\n            ${reportType.includes('sales') || reportType.includes('purchases') ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي المبلغ</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0).toLocaleString()} د.ع</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">متوسط المبلغ</div>\n                <div class=\"summary-value\">${Math.round(reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0) / reportData.length).toLocaleString()} د.ع</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الفواتير المدفوعة</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => item.payment_status === 'paid').length}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الفواتير المعلقة</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => item.payment_status === 'pending').length}</div>\n              </div>\n            ` : ''}\n            ${reportType === 'inventory' ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي الكمية</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0).toLocaleString()}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الأدوية منتهية الصلاحية</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => new Date(item.expiry_date) < new Date()).length}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الأدوية قليلة الكمية</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => (item.quantity || 0) < 10).length}</div>\n              </div>\n            ` : ''}\n          </div>\n\n          <table>\n            <thead>\n              <tr>\n                <th style=\"width: 30px;\">ت</th>\n                ${reportType.includes('sales') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>العميل</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType.includes('purchases') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>المورد</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'inventory' ? `\n                  <th>اسم الدواء</th>\n                  <th>الفئة</th>\n                  <th>الكمية</th>\n                  <th>تاريخ الانتهاء</th>\n                  <th>الحالة</th>\n                ` : ''}\n                ${reportType === 'financial' ? `\n                  <th>نوع العملية</th>\n                  <th>المبلغ</th>\n                  <th>الوصف</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'customers' ? `\n                  <th>اسم العميل</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ` : ''}\n                ${reportType === 'suppliers' ? `\n                  <th>اسم المورد</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ` : ''}\n                ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `\n                  ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `<th>${translateColumnName(key)}</th>`).join('')}\n                ` : ''}\n              </tr>\n            </thead>\n            <tbody>\n              ${reportData.map((item: any, index: number) => `\n                <tr>\n                  <td class=\"number-cell\">${index + 1}</td>\n                  ${reportType.includes('sales') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType.includes('purchases') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.suppliers?.name || 'غير محدد'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType === 'inventory' ? `\n                    <td>${item.medicines?.name || item.medicine_name || 'غير محدد'}</td>\n                    <td>${item.medicines?.category || item.category || 'غير محدد'}</td>\n                    <td>${item.quantity || 0}</td>\n                    <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'}</td>\n                    <td>${(item.quantity || 0) < 10 ? 'كمية قليلة' : 'طبيعي'}</td>\n                  ` : ''}\n                  ${reportType === 'financial' ? `\n                    <td>${item.type === 'income' ? 'دخل' : 'مصروف'}</td>\n                    <td>${item.amount?.toLocaleString() || 0} د.ع</td>\n                    <td>${item.description || 'غير محدد'}</td>\n                    <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : 'غير محدد'}</td>\n                  ` : ''}\n                  ${reportType === 'customers' ? `\n                    <td>${item.name || 'غير محدد'}</td>\n                    <td>${item.phone || 'غير محدد'}</td>\n                    <td>${item.address || 'غير محدد'}</td>\n                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>\n                  ` : ''}\n                  ${reportType === 'suppliers' ? `\n                    <td>${item.name || 'غير محدد'}</td>\n                    <td>${item.phone || 'غير محدد'}</td>\n                    <td>${item.address || 'غير محدد'}</td>\n                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>\n                  ` : ''}\n                  ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `\n                    ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `\n                      <td>${formatCellValue(item[key], key)}</td>\n                    `).join('')}\n                  ` : ''}\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n        ` : `\n          <div style=\"text-align: center; padding: 50px; border: 1px solid #000;\">\n            لا توجد بيانات للعرض\n          </div>\n        `}\n      </div>\n    </body>\n    </html>\n  `\n}\n\nexport const generateLarenReturnHTML = (returnRecord: any, settings: PrintSettings) => {\n  const items = returnRecord.return_items || []\n  const isSupplierReturn = returnRecord.type === 'purchase_return'\n  const customerSupplier = isSupplierReturn\n    ? (returnRecord.suppliers?.name || 'غير محدد')\n    : (returnRecord.customers?.name || returnRecord.customer_name || 'عميل نقدي')\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>سند إرجاع - ${returnRecord.return_number}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body {\n          font-family: 'Arial', sans-serif;\n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container {\n          max-width: 210mm;\n          margin: 0 auto;\n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n\n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n\n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n\n        .return-title {\n          text-align: center;\n          font-size: 20px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 2px solid #000;\n          background-color: #f0f0f0;\n        }\n\n        .return-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n\n        .return-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n\n        .customer-info {\n          border-right: none;\n        }\n\n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n\n        .detail-label {\n          font-weight: bold;\n          min-width: 100px;\n        }\n\n        .detail-value {\n          text-align: left;\n        }\n\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n\n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n\n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n\n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n\n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n\n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n\n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n\n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n\n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n\n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n\n        @media print {\n          body { margin: 0; }\n          .container {\n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div class=\"company-address\">${settings.companyAddress}</div>\n          </div>\n\n          <div class=\"logo-section\">\n            <div style=\"font-size: 16px; font-weight: bold; text-align: center; line-height: 1.2;\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class=\"return-title\">\n          سند إرجاع ${isSupplierReturn ? 'مشتريات' : 'مبيعات'}\n        </div>\n\n        <div class=\"return-details\">\n          <div class=\"return-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">رقم سند الإرجاع:</span>\n              <span class=\"detail-value\">${returnRecord.return_number}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">التاريخ:</span>\n              <span class=\"detail-value\">${new Date(returnRecord.created_at).toLocaleDateString('ar-EG')}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">رقم الفاتورة الأصلية:</span>\n              <span class=\"detail-value\">${returnRecord.original_invoice_number || 'غير محدد'}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">نوع الإرجاع:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? 'إرجاع للمورد' : 'إرجاع من العميل'}</span>\n            </div>\n          </div>\n\n          <div class=\"customer-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${isSupplierReturn ? 'المورد:' : 'العميل:'}</span>\n              <span class=\"detail-value\">${customerSupplier}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الهاتف:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? (returnRecord.suppliers?.phone || '') : (returnRecord.customers?.phone || '')}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">العنوان:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? (returnRecord.suppliers?.address || '') : (returnRecord.customers?.address || '')}</span>\n            </div>\n          </div>\n        </div>\n\n        <table class=\"items-table\">\n          <thead>\n            <tr>\n              <th style=\"width: 40px;\">ت</th>\n              <th style=\"width: 200px;\">اسم المادة</th>\n              <th style=\"width: 60px;\">الكمية</th>\n              <th style=\"width: 80px;\">السعر</th>\n              <th style=\"width: 100px;\">المجموع</th>\n              <th style=\"width: 100px;\">سبب الإرجاع</th>\n              <th style=\"width: 60px;\">EXP</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any, index: number) => `\n              <tr>\n                <td>${index + 1}</td>\n                <td class=\"item-name\">${item.medicine_name || item.medicines?.name || item.medicine?.name || 'غير محدد'}</td>\n                <td>${item.quantity}</td>\n                <td>${(item.unit_price || 0).toLocaleString()}</td>\n                <td>${(item.total_amount || 0).toLocaleString()}</td>\n                <td style=\"font-size: 10px;\">${item.return_reason || 'غير محدد'}</td>\n                <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/') : ''}</td>\n              </tr>\n            `).join('') || ''}\n\n            ${Array.from({ length: Math.max(0, 8 - (items?.length || 0)) }, (_, i) => `\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <div class=\"totals-section\">\n          <table>\n            <tr>\n              <td class=\"total-label\">إجمالي المبلغ المرتجع:</td>\n              <td class=\"total-value\">${returnRecord.total_amount?.toLocaleString() || 0} د.ع</td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"notes-section\">\n          <div style=\"font-weight: bold; margin-bottom: 5px;\">ملاحظات:</div>\n          <div>${returnRecord.notes || ''}</div>\n        </div>\n\n        <div class=\"footer\">\n          <div style=\"font-size: 12px;\">\n            صفحة 1 من 1\n          </div>\n\n          <div class=\"signature-section\">\n            <div class=\"signature-box\">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n\n          <div style=\"font-size: 12px; text-align: left;\">\n            ${settings.footerText || 'شكراً لتعاملكم معنا'}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,2BAA2B,CAAC,SAAc,MAAuC;IAC5F,IAAI,OAAO,kBAAkB,eAAe;IAE5C,IAAI,SAAS,UAAU;QACrB,QAAQ,QAAQ,oBAAoB,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,IAAI,EAAE;QACzG,MAAM,aAAa,QAAQ,IAAI,IAAI,QAAQ,WAAW,IAAI;QAC1D,mBAAmB,eAAe,UAC7B,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI,cAC5E,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI;QACjF,gBAAgB,eAAe,UAAU,iBAAiB;QAC1D,iBAAiB,QAAQ,aAAa,IAAI,QAAQ,cAAc,IAAI;IACtE,OAAO;QACL,QAAQ,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB;QACvF,mBAAmB,SAAS,UACvB,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI,cAC5E,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI;QACjF,gBAAgB,SAAS,UAAU,kBAAkB;QACrD,iBAAiB,QAAQ,cAAc,IAAI;IAC7C;IAEA,OAAO,CAAC;;;;;;aAMG,EAAE,cAAc,GAAG,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAiOR,EAAE,SAAS,WAAW,CAAC;yCACvB,EAAE,SAAS,aAAa,CAAC;yCACzB,EAAE,SAAS,cAAc,CAAC;;;;;;;;;;;;;;;yCAe1B,EAAE,SAAS,WAAW,iBAAiB,gBAAgB;yCACvD,EAAE,eAAe;;;;yCAIjB,EAAE,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,SAAS;;YAExF,EAAE,SAAS,WAAW,CAAC;;;yCAGM,EAAE,QAAQ,cAAc,KAAK,SAAS,UAAU,MAAM;kBAC7E,CAAC,GAAG,GAAG;YACb,EAAE,SAAS,WAAW,CAAC;;;yCAGM,EAAE,QAAQ,MAAM,IAAI,WAAW;;;;yCAI/B,EAAE,QAAQ,MAAM,KAAK,aAAa,UAAU,QAAQ,MAAM,KAAK,aAAa,UAAU,eAAe;kBAC5H,CAAC,GAAG,GAAG;;;;;;yCAMgB,EACzB,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aAAa,YAAY,YAChF,SAAS,UAAU,YAAY,UACrC;yCAC0B,EAAE,iBAAiB;;;;yCAInB,EACzB,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACnD,QAAQ,SAAS,EAAE,SAAS,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KAC/E,QAAQ,SAAS,EAAE,SAAS,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KACnF,SAAS,UACL,QAAQ,SAAS,EAAE,SAAS,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KAC/E,QAAQ,SAAS,EAAE,SAAS,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,GACzF;;;;yCAI0B,EACzB,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACnD,QAAQ,SAAS,EAAE,WAAW,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACrF,QAAQ,SAAS,EAAE,WAAW,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACzF,SAAS,UACL,QAAQ,SAAS,EAAE,WAAW,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACrF,QAAQ,SAAS,EAAE,WAAW,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,GAC/F;;;;;;;;;;gCAUiB,EAAE,SAAS,WAAW,UAAU,QAAQ;;;;cAI1D,EAAE,SAAS,WAAW,+CAA+C,GAAG;;;;;;YAM1E,EAAE,OAAO,IAAI,CAAC,MAAW,QAAkB,CAAC;;oBAEpC,EAAE,QAAQ,EAAE;sCACM,EACpB,SAAS,WACJ,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,KAAK,gBAAgB,EAAE,WAAW,QAAQ,KAAK,SAAS,EAAE,QAAQ,aAC9G,SAAS,UACN,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,KAAK,gBAAgB,EAAE,WAAW,QAAQ,aACrF,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,KAAK,SAAS,EAAE,QAAQ,KAAK,IAAI,IAAI,WACxF;oBACG,EAAE,KAAK,QAAQ,CAAC;oBAChB,EACF,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACpD,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,KACrD,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,KAC3D,SAAS,UACP,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KACrC,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,GAC5D;oBACG,EACF,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACpD,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,KACvD,CAAC,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KAC7D,SAAS,UACP,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KACtC,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,GAC9D;gBACD,EAAE,SAAS,WAAW,CAAC,6BAA6B,EAAE,KAAK,aAAa,IAAI,QAAQ,MAAM,IAAI,WAAW,KAAK,CAAC,GAAG,GAAG;oBACjH,EACF,SAAS,WACJ,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,gBAAgB,EAAE,cAC3D,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAC9H,KACJ,SAAS,UACN,KAAK,gBAAgB,EAAE,cAAc,IAAI,KAAK,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,KACnI,KAAK,WAAW,IAAI,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,GAC9I;oBACG,EACF,SAAS,WACJ,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,gBAAgB,EAAE,gBAAgB,KAC7E,SAAS,UACN,KAAK,gBAAgB,EAAE,gBAAgB,KACvC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,GAC7C;;YAEL,CAAC,EAAE,KAAK,OAAO,GAAG;;;YAGlB,EAAE,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,UAAU,CAAC;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;;;;;;;gBAOxE,EAAE,SAAS,WAAW,oBAAoB,GAAG;;;;YAIjD,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;YAOZ,EAAE,SAAS,WAAW,CAAC;;;sCAGG,EAAE,QAAQ,YAAY,EAAE,oBAAoB,EAAE;;;;sCAI9C,EAAE,QAAQ,eAAe,EAAE,oBAAoB,EAAE;;;;sCAIjD,EAAE,QAAQ,YAAY,EAAE,oBAAoB,EAAE;iBACnE,CAAC,GAAG,CAAC;;;sCAGgB,EAAE,QAAQ,YAAY,EAAE,oBAAoB,EAAE;iBACnE,CAAC,CAAC;;;;;;4CAMyB,EAAE,QAAQ,KAAK,IAAI,GAAG;UACxD,EAAE,SAAS,YAAY,QAAQ,gBAAgB,GAAG,CAAC;wFAC2B,EAAE,QAAQ,gBAAgB,CAAC,MAAM,CAAC,GAAG,GAAG;;;;;;;;;;;;;;;;;YAiBpH,EAAE,SAAS,UAAU,IAAI,sBAAsB;;;;;;EAMzD,CAAC;AACH;AAEA,2BAA2B;AAC3B,MAAM,sBAAsB,CAAC;IAC3B,MAAM,eAA0C;QAC9C,MAAM;QACN,QAAQ;QACR,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,YAAY;QACZ,SAAS;IACX;IACA,OAAO,YAAY,CAAC,WAAW,IAAI;AACrC;AAEA,yBAAyB;AACzB,MAAM,kBAAkB,CAAC,OAAY;IACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAElD,iBAAiB;IACjB,IAAI,WAAW,QAAQ,CAAC,WAAW,WAAW,QAAQ,CAAC,eAAe;QACpE,IAAI;YACF,OAAO,IAAI,KAAK,OAAO,kBAAkB,CAAC;QAC5C,EAAE,OAAM;YACN,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA,gBAAgB;IAChB,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,SAAS;QAChG,MAAM,MAAM,WAAW;QACvB,OAAO,MAAM,OAAO,MAAM,QAAQ,KAAK,GAAG,IAAI,cAAc,GAAG,IAAI,CAAC;IACtE;IAEA,mBAAmB;IACnB,IAAI,eAAe,kBAAkB;QACnC,OAAO,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,MAAM,QAAQ;IACnF;IAEA,OAAO,MAAM,QAAQ;AACvB;AAEO,MAAM,0BAA0B,CAAC,YAAiB,YAAoB,OAAe;IAC1F,OAAO,CAAC;;;;;;aAMG,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAoJoB,EAAE,SAAS,WAAW,CAAC;yCACvB,EAAE,SAAS,aAAa,CAAC;iBACjD,EAAE,SAAS,cAAc,CAAC;;;;;;;;;;;kCAWT,EAAE,MAAM;gDACM,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;QAEjF,EAAE,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,IAAI,CAAC;;;;yCAIvB,EAAE,WAAW,MAAM,CAAC;;YAEjD,EAAE,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,eAAe,CAAC;;;2CAGvC,EAAE,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc,GAAG;;;;2CAIzH,EAAE,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,WAAW,MAAM,EAAE,cAAc,GAAG;;;;2CAIzJ,EAAE,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,cAAc,KAAK,QAAQ,MAAM,CAAC;;;;2CAI1E,EAAE,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,cAAc,KAAK,WAAW,MAAM,CAAC;;YAE5G,CAAC,GAAG,GAAG;YACP,EAAE,eAAe,cAAc,CAAC;;;2CAGD,EAAE,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,GAAG,cAAc,GAAG;;;;2CAIhG,EAAE,WAAW,MAAM,CAAC,CAAC,OAAc,IAAI,KAAK,KAAK,WAAW,IAAI,IAAI,QAAQ,MAAM,CAAC;;;;2CAInF,EAAE,WAAW,MAAM,CAAC,CAAC,OAAc,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC;;YAEpG,CAAC,GAAG,GAAG;;;;;;;gBAOH,EAAE,WAAW,QAAQ,CAAC,WAAW,CAAC;;;;;;gBAMlC,CAAC,GAAG,GAAG;gBACP,EAAE,WAAW,QAAQ,CAAC,eAAe,CAAC;;;;;;gBAMtC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,cAAc,CAAC;;;;;;gBAMhC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,cAAc,CAAC;;;;;gBAKhC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,cAAc,CAAC;;;;;gBAKhC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,cAAc,CAAC;;;;;gBAKhC,CAAC,GAAG,GAAG;gBACP,EAAE,CAAC,WAAW,QAAQ,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC,gBAAgB,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,cAAc,CAAC;kBAC9L,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,MAAO,CAAC,IAAI,EAAE,oBAAoB,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;gBAC7G,CAAC,GAAG,GAAG;;;;cAIT,EAAE,WAAW,GAAG,CAAC,CAAC,MAAW,QAAkB,CAAC;;0CAEpB,EAAE,QAAQ,EAAE;kBACpC,EAAE,WAAW,QAAQ,CAAC,WAAW,CAAC;wBAC5B,EAAE,KAAK,cAAc,CAAC;wBACtB,EAAE,KAAK,SAAS,EAAE,QAAQ,KAAK,aAAa,IAAI,YAAY;wBAC5D,EAAE,KAAK,YAAY,EAAE,iBAAiB;wBACtC,EAAE,KAAK,cAAc,KAAK,SAAS,UAAU,OAAO;wBACpD,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;kBAC9D,CAAC,GAAG,GAAG;kBACP,EAAE,WAAW,QAAQ,CAAC,eAAe,CAAC;wBAChC,EAAE,KAAK,cAAc,CAAC;wBACtB,EAAE,KAAK,SAAS,EAAE,QAAQ,WAAW;wBACrC,EAAE,KAAK,YAAY,EAAE,iBAAiB;wBACtC,EAAE,KAAK,cAAc,KAAK,SAAS,UAAU,OAAO;wBACpD,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;kBAC9D,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,cAAc,CAAC;wBAC1B,EAAE,KAAK,SAAS,EAAE,QAAQ,KAAK,aAAa,IAAI,WAAW;wBAC3D,EAAE,KAAK,SAAS,EAAE,YAAY,KAAK,QAAQ,IAAI,WAAW;wBAC1D,EAAE,KAAK,QAAQ,IAAI,EAAE;wBACrB,EAAE,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,WAAW,WAAW;wBACzF,EAAE,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe,QAAQ;kBAC3D,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,cAAc,CAAC;wBAC1B,EAAE,KAAK,IAAI,KAAK,WAAW,QAAQ,QAAQ;wBAC3C,EAAE,KAAK,MAAM,EAAE,oBAAoB,EAAE;wBACrC,EAAE,KAAK,WAAW,IAAI,WAAW;wBACjC,EAAE,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAAW,WAAW;kBAC7F,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,cAAc,CAAC;wBAC1B,EAAE,KAAK,IAAI,IAAI,WAAW;wBAC1B,EAAE,KAAK,KAAK,IAAI,WAAW;wBAC3B,EAAE,KAAK,OAAO,IAAI,WAAW;wBAC7B,EAAE,KAAK,eAAe,EAAE,oBAAoB,EAAE;kBACpD,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,cAAc,CAAC;wBAC1B,EAAE,KAAK,IAAI,IAAI,WAAW;wBAC1B,EAAE,KAAK,KAAK,IAAI,WAAW;wBAC3B,EAAE,KAAK,OAAO,IAAI,WAAW;wBAC7B,EAAE,KAAK,eAAe,EAAE,oBAAoB,EAAE;kBACpD,CAAC,GAAG,GAAG;kBACP,EAAE,CAAC,WAAW,QAAQ,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC,gBAAgB,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,cAAc,CAAC;oBAC9L,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,MAAO,CAAC;0BACrD,EAAE,gBAAgB,IAAI,CAAC,IAAI,EAAE,KAAK;oBACxC,CAAC,EAAE,IAAI,CAAC,IAAI;kBACd,CAAC,GAAG,GAAG;;cAEX,CAAC,EAAE,IAAI,CAAC,IAAI;;;QAGlB,CAAC,GAAG,CAAC;;;;QAIL,CAAC,CAAC;;;;EAIR,CAAC;AACH;AAEO,MAAM,0BAA0B,CAAC,cAAmB;IACzD,MAAM,QAAQ,aAAa,YAAY,IAAI,EAAE;IAC7C,MAAM,mBAAmB,aAAa,IAAI,KAAK;IAC/C,MAAM,mBAAmB,mBACpB,aAAa,SAAS,EAAE,QAAQ,aAChC,aAAa,SAAS,EAAE,QAAQ,aAAa,aAAa,IAAI;IAEnE,OAAO,CAAC;;;;;;yBAMe,EAAE,aAAa,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAoNb,EAAE,SAAS,WAAW,CAAC;yCACvB,EAAE,SAAS,aAAa,CAAC;yCACzB,EAAE,SAAS,cAAc,CAAC;;;;;;;;;;;;oBAY/C,EAAE,mBAAmB,YAAY,SAAS;;;;;;;yCAOrB,EAAE,aAAa,aAAa,CAAC;;;;yCAI7B,EAAE,IAAI,KAAK,aAAa,UAAU,EAAE,kBAAkB,CAAC,SAAS;;;;yCAIhE,EAAE,aAAa,uBAAuB,IAAI,WAAW;;;;yCAIrD,EAAE,mBAAmB,iBAAiB,kBAAkB;;;;;;yCAMxD,EAAE,mBAAmB,YAAY,UAAU;yCAC3C,EAAE,iBAAiB;;;;yCAInB,EAAE,mBAAoB,aAAa,SAAS,EAAE,SAAS,KAAO,aAAa,SAAS,EAAE,SAAS,GAAI;;;;yCAInG,EAAE,mBAAoB,aAAa,SAAS,EAAE,WAAW,KAAO,aAAa,SAAS,EAAE,WAAW,GAAI;;;;;;;;;;;;;;;;;;YAkBpI,EAAE,OAAO,IAAI,CAAC,MAAW,QAAkB,CAAC;;oBAEpC,EAAE,QAAQ,EAAE;sCACM,EAAE,KAAK,aAAa,IAAI,KAAK,SAAS,EAAE,QAAQ,KAAK,QAAQ,EAAE,QAAQ,WAAW;oBACpG,EAAE,KAAK,QAAQ,CAAC;oBAChB,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,GAAG;oBAC1C,EAAE,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,cAAc,GAAG;6CACnB,EAAE,KAAK,aAAa,IAAI,WAAW;oBAC5D,EAAE,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,GAAG;;YAE7G,CAAC,EAAE,KAAK,OAAO,GAAG;;YAElB,EAAE,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,UAAU,CAAC;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;;;;;;;;;;YAU3E,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;sCAQc,EAAE,aAAa,YAAY,EAAE,oBAAoB,EAAE;;;;;;;eAO1E,EAAE,aAAa,KAAK,IAAI,GAAG;;;;;;;;;;;;;;;;YAgB9B,EAAE,SAAS,UAAU,IAAI,sBAAsB;;;;;;EAMzD,CAAC;AACH", "debugId": null}}, {"offset": {"line": 9531, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/hooks/usePrintSettings.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface PrintSettings {\n  companyName: string\n  companyNameEn: string\n  companyAddress: string\n  companyPhone: string\n  companyEmail: string\n  logo?: string\n  showLogo: boolean\n  showHeader: boolean\n  showFooter: boolean\n  footerText: string\n  fontSize: 'small' | 'medium' | 'large'\n  paperSize: 'A4' | 'A5' | 'thermal'\n  showBorders: boolean\n  showColors: boolean\n  includeBarcode: boolean\n  includeQRCode: boolean\n  watermark?: string\n  showWatermark: boolean\n  headerColor: string\n  accentColor: string\n  textColor: string\n  backgroundColor: string\n}\n\nconst defaultSettings: PrintSettings = {\n  companyName: 'مكتب لارين العلمي',\n  companyNameEn: 'LAREN SCIENTIFIC BUREAU',\n  companyAddress: 'بغداد - شارع فلسطين',\n  companyPhone: '+964 ************',\n  companyEmail: '<EMAIL>',\n  showLogo: true,\n  showHeader: true,\n  showFooter: true,\n  footerText: 'شكراً لتعاملكم معنا',\n  fontSize: 'medium',\n  paperSize: 'A4',\n  showBorders: true,\n  showColors: false,\n  includeBarcode: false,\n  includeQRCode: false,\n  showWatermark: false,\n  headerColor: '#1f2937',\n  accentColor: '#3b82f6',\n  textColor: '#374151',\n  backgroundColor: '#ffffff'\n}\n\nexport function usePrintSettings() {\n  const [settings, setSettings] = useState<PrintSettings>(defaultSettings)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadSettings()\n  }, [])\n\n  const loadSettings = () => {\n    try {\n      const savedSettings = localStorage.getItem('printSettings')\n      if (savedSettings) {\n        const parsed = JSON.parse(savedSettings)\n        setSettings({ ...defaultSettings, ...parsed })\n      }\n    } catch (error) {\n      console.error('Error loading print settings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateSettings = (newSettings: Partial<PrintSettings>) => {\n    const updatedSettings = { ...settings, ...newSettings }\n    setSettings(updatedSettings)\n    \n    try {\n      localStorage.setItem('printSettings', JSON.stringify(updatedSettings))\n    } catch (error) {\n      console.error('Error saving print settings:', error)\n    }\n  }\n\n  const resetSettings = () => {\n    setSettings(defaultSettings)\n    try {\n      localStorage.removeItem('printSettings')\n    } catch (error) {\n      console.error('Error resetting print settings:', error)\n    }\n  }\n\n  const exportSettings = () => {\n    const dataStr = JSON.stringify(settings, null, 2)\n    const dataBlob = new Blob([dataStr], { type: 'application/json' })\n    const url = URL.createObjectURL(dataBlob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = 'print-settings.json'\n    link.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const importSettings = (file: File) => {\n    return new Promise<void>((resolve, reject) => {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        try {\n          const importedSettings = JSON.parse(e.target?.result as string)\n          updateSettings(importedSettings)\n          resolve()\n        } catch (error) {\n          reject(error)\n        }\n      }\n      reader.onerror = () => reject(new Error('Failed to read file'))\n      reader.readAsText(file)\n    })\n  }\n\n  return {\n    settings,\n    printSettings: settings, // alias for compatibility\n    loading,\n    updateSettings,\n    resetSettings,\n    exportSettings,\n    importSettings\n  }\n}\n\n// Print utility functions\nexport const printInvoice = async (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {\n  try {\n    console.log('🖨️ بدء طباعة الفاتورة:', invoice)\n\n    // If we have an invoice ID, fetch fresh data from database for accurate printing\n    let printData = invoice\n    if (invoice.id && type === 'sales') {\n      const { getSalesInvoiceForPrint } = require('@/lib/database')\n      const result = await getSalesInvoiceForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original invoice\n          customerName: invoice.customerName || result.data.customer_name,\n          customerPhone: invoice.customerPhone || result.data.customers?.phone,\n          customerAddress: invoice.customerAddress || result.data.customers?.address,\n        }\n        console.log('✅ تم استرجاع بيانات فاتورة المبيعات من قاعدة البيانات:', printData)\n      }\n    } else if (invoice.id && type === 'purchase') {\n      const { getPurchaseInvoiceForPrint } = require('@/lib/database')\n      const result = await getPurchaseInvoiceForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original invoice\n          supplierName: invoice.supplierName || result.data.suppliers?.name,\n          supplierPhone: invoice.supplierPhone || result.data.suppliers?.phone,\n          supplierAddress: invoice.supplierAddress || result.data.suppliers?.address,\n        }\n        console.log('✅ تم استرجاع بيانات فاتورة المشتريات من قاعدة البيانات:', printData)\n      }\n    } else if (invoice.id && type === 'return') {\n      const { getReturnForPrint } = require('@/lib/database')\n      const result = await getReturnForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original return\n          customerName: invoice.customerName || result.data.customer_name,\n          supplierName: invoice.supplierName || result.data.supplier_name,\n        }\n        console.log('✅ تم استرجاع بيانات المرتجع من قاعدة البيانات:', printData)\n      }\n    }\n\n    const printWindow = window.open('', '_blank')\n    if (!printWindow) {\n      console.error('❌ فشل في فتح نافذة الطباعة')\n      return\n    }\n\n    // Use Laren template by default\n    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')\n    const invoiceHTML = generateLarenInvoiceHTML(printData, type, settings)\n\n    printWindow.document.write(invoiceHTML)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n\n    console.log('✅ تمت الطباعة بنجاح')\n  } catch (error) {\n    console.error('❌ خطأ في الطباعة:', error)\n\n    // Fallback to original method\n    const printWindow = window.open('', '_blank')\n    if (!printWindow) return\n\n    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')\n    const invoiceHTML = generateLarenInvoiceHTML(invoice, type, settings)\n\n    printWindow.document.write(invoiceHTML)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n  }\n}\n\nexport const printReport = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  const printWindow = window.open('', '_blank')\n  if (!printWindow) return\n\n  // Use Laren template by default\n  const { generateLarenReportHTML } = require('@/utils/larenPrintTemplate')\n  const reportHTML = generateLarenReportHTML(reportData, reportType, title, settings)\n\n  printWindow.document.write(reportHTML)\n  printWindow.document.close()\n  printWindow.focus()\n  printWindow.print()\n  printWindow.close()\n}\n\nconst generateInvoiceHTML = (invoice: any, type: 'sales' | 'purchase', settings: PrintSettings) => {\n  const items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items\n  const customerSupplier = type === 'sales' \n    ? (invoice.customers?.name || invoice.customer_name || 'عميل نقدي')\n    : (invoice.suppliers?.name || 'غير محدد')\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'} - ${invoice.invoice_number}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: ${settings.fontSize === 'small' ? '12px' : settings.fontSize === 'large' ? '16px' : '14px'};\n          color: ${settings.textColor};\n          background-color: ${settings.backgroundColor};\n          line-height: 1.6;\n        }\n        .container { \n          max-width: ${settings.paperSize === 'A5' ? '600px' : settings.paperSize === 'thermal' ? '300px' : '800px'}; \n          margin: 0 auto; \n          padding: 20px;\n          ${settings.showBorders ? 'border: 1px solid #ddd;' : ''}\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px; \n          ${settings.showHeader ? '' : 'display: none;'}\n          border-bottom: 2px solid ${settings.accentColor};\n          padding-bottom: 20px;\n        }\n        .logo { \n          width: 80px; \n          height: 80px; \n          background: ${settings.accentColor}; \n          border-radius: 50%; \n          margin: 0 auto 15px; \n          display: flex; \n          align-items: center; \n          justify-content: center; \n          color: white; \n          font-size: 24px; \n          font-weight: bold;\n          ${settings.showLogo ? '' : 'display: none;'}\n        }\n        .company-name { \n          font-size: 24px; \n          font-weight: bold; \n          color: ${settings.headerColor}; \n          margin-bottom: 10px; \n        }\n        .company-info { color: #666; margin-bottom: 5px; }\n        .invoice-title { \n          text-align: center; \n          font-size: 20px; \n          font-weight: bold; \n          margin: 20px 0; \n          color: ${settings.headerColor};\n        }\n        .invoice-details { \n          display: flex; \n          justify-content: space-between; \n          margin-bottom: 30px; \n        }\n        .invoice-info, .customer-info { flex: 1; }\n        .customer-info { text-align: left; }\n        table { \n          width: 100%; \n          border-collapse: collapse; \n          margin-bottom: 20px; \n        }\n        th, td { \n          border: 1px solid #ddd; \n          padding: 10px; \n          text-align: center; \n        }\n        th { \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; \n          color: ${settings.showColors ? 'white' : settings.textColor};\n          font-weight: bold;\n        }\n        .totals { \n          width: 300px; \n          margin-left: auto; \n          border: 1px solid #ddd; \n        }\n        .totals tr:last-child { \n          font-weight: bold; \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'};\n          color: ${settings.showColors ? 'white' : settings.textColor};\n        }\n        .footer { \n          text-align: center; \n          margin-top: 30px; \n          padding-top: 20px; \n          border-top: 1px solid #ddd; \n          color: #666;\n          ${settings.showFooter ? '' : 'display: none;'}\n        }\n        .notes { \n          margin-top: 20px; \n          padding: 15px; \n          background-color: #f9f9f9; \n          border-left: 4px solid ${settings.accentColor};\n        }\n        @media print {\n          body { margin: 0; }\n          .container { box-shadow: none; border: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"logo\">${settings.companyName.charAt(0)}</div>\n          <div class=\"company-name\">${settings.companyName}</div>\n          <div class=\"company-info\">${settings.companyAddress}</div>\n          <div class=\"company-info\">${settings.companyPhone}</div>\n          <div class=\"company-info\">${settings.companyEmail}</div>\n        </div>\n\n        <div class=\"invoice-title\">${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'}</div>\n\n        <div class=\"invoice-details\">\n          <div class=\"invoice-info\">\n            <div><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</div>\n            <div><strong>التاريخ:</strong> ${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</div>\n            <div><strong>طريقة الدفع:</strong> ${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</div>\n            <div><strong>حالة الدفع:</strong> ${invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</div>\n          </div>\n          <div class=\"customer-info\">\n            <div><strong>${type === 'sales' ? 'بيانات العميل' : 'بيانات المورد'}:</strong></div>\n            <div>${customerSupplier}</div>\n            ${type === 'sales' && invoice.customers?.phone ? `<div>الهاتف: ${invoice.customers.phone}</div>` : ''}\n            ${type === 'purchase' && invoice.suppliers?.phone ? `<div>الهاتف: ${invoice.suppliers.phone}</div>` : ''}\n          </div>\n        </div>\n\n        <table>\n          <thead>\n            <tr>\n              <th>اسم الدواء</th>\n              <th>الكمية</th>\n              <th>سعر الوحدة</th>\n              <th>المجموع</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any) => `\n              <tr>\n                <td>${type === 'sales'\n                  ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')\n                  : (item.medicine_name || item.medicineName || item.medicines?.name || 'غير محدد')\n                }</td>\n                <td>${item.quantity}</td>\n                <td>${type === 'sales'\n                  ? (item.unit_price || 0).toLocaleString()\n                  : (item.unit_cost || item.unitCost || 0).toLocaleString()\n                } د.ع</td>\n                <td>${type === 'sales'\n                  ? (item.total_price || 0).toLocaleString()\n                  : (item.total_cost || item.totalCost || 0).toLocaleString()\n                } د.ع</td>\n              </tr>\n            `).join('') || ''}\n          </tbody>\n        </table>\n\n        <table class=\"totals\">\n          <tr>\n            <td>المجموع الفرعي:</td>\n            <td>${invoice.total_amount?.toLocaleString()} د.ع</td>\n          </tr>\n          <tr>\n            <td>الخصم:</td>\n            <td>${invoice.discount_amount?.toLocaleString() || 0} د.ع</td>\n          </tr>\n          <tr>\n            <td>المجموع النهائي:</td>\n            <td>${invoice.final_amount?.toLocaleString()} د.ع</td>\n          </tr>\n        </table>\n\n        ${invoice.notes ? `\n          <div class=\"notes\">\n            <strong>ملاحظات:</strong><br>\n            ${invoice.notes}\n          </div>\n        ` : ''}\n\n        <div class=\"footer\">\n          <div>${settings.footerText}</div>\n          <div style=\"margin-top: 10px; font-size: 12px;\">\n            تم إنشاء هذا المستند بواسطة نظام إدارة الصيدلية - ${new Date().toLocaleDateString('ar-EG')}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n\nconst generateReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: ${settings.fontSize === 'small' ? '11px' : settings.fontSize === 'large' ? '15px' : '13px'};\n          color: ${settings.textColor};\n          background-color: ${settings.backgroundColor};\n          line-height: 1.5;\n        }\n        .container { \n          max-width: 100%; \n          margin: 0 auto; \n          padding: 20px;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px; \n          ${settings.showHeader ? '' : 'display: none;'}\n          border-bottom: 2px solid ${settings.accentColor};\n          padding-bottom: 20px;\n        }\n        .company-name { \n          font-size: 20px; \n          font-weight: bold; \n          color: ${settings.headerColor}; \n          margin-bottom: 10px; \n        }\n        .report-title { \n          text-align: center; \n          font-size: 18px; \n          font-weight: bold; \n          margin: 20px 0; \n          color: ${settings.headerColor};\n        }\n        .summary { \n          display: flex; \n          justify-content: space-around; \n          margin-bottom: 30px; \n          flex-wrap: wrap;\n        }\n        .summary-item { \n          text-align: center; \n          padding: 15px; \n          border: 1px solid #ddd; \n          border-radius: 5px; \n          margin: 5px;\n          min-width: 150px;\n          background-color: ${settings.showColors ? '#f8f9fa' : 'transparent'};\n        }\n        .summary-label { \n          font-size: 12px; \n          color: #666; \n          margin-bottom: 5px; \n        }\n        .summary-value { \n          font-size: 18px; \n          font-weight: bold; \n          color: ${settings.accentColor};\n        }\n        table { \n          width: 100%; \n          border-collapse: collapse; \n          margin-bottom: 20px; \n          font-size: 11px;\n        }\n        th, td { \n          border: 1px solid #ddd; \n          padding: 8px; \n          text-align: center; \n        }\n        th { \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; \n          color: ${settings.showColors ? 'white' : settings.textColor};\n          font-weight: bold;\n        }\n        .footer { \n          text-align: center; \n          margin-top: 30px; \n          padding-top: 20px; \n          border-top: 1px solid #ddd; \n          color: #666;\n          ${settings.showFooter ? '' : 'display: none;'}\n        }\n        @media print {\n          body { margin: 0; }\n          .container { box-shadow: none; }\n          table { page-break-inside: auto; }\n          tr { page-break-inside: avoid; page-break-after: auto; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-name\">${settings.companyName}</div>\n          <div>${settings.companyAddress}</div>\n        </div>\n\n        <div class=\"report-title\">${title}</div>\n        <div style=\"text-align: center; margin-bottom: 20px; color: #666;\">\n          تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}\n        </div>\n\n        ${Array.isArray(reportData) && reportData.length > 0 ? `\n          <div class=\"summary\">\n            <div class=\"summary-item\">\n              <div class=\"summary-label\">عدد السجلات</div>\n              <div class=\"summary-value\">${reportData.length}</div>\n            </div>\n            ${reportType.includes('sales') || reportType.includes('purchases') ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي المبلغ</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع</div>\n              </div>\n            ` : ''}\n          </div>\n\n          <table>\n            <thead>\n              <tr>\n                ${reportType.includes('sales') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>العميل</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType.includes('purchases') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>المورد</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'inventory' ? `\n                  <th>اسم الدواء</th>\n                  <th>الفئة</th>\n                  <th>الكمية</th>\n                  <th>تاريخ الانتهاء</th>\n                  <th>الحالة</th>\n                ` : ''}\n                ${reportType === 'cashbox' ? `\n                  <th>النوع</th>\n                  <th>الفئة</th>\n                  <th>الوصف</th>\n                  <th>المبلغ</th>\n                  <th>التاريخ</th>\n                ` : ''}\n              </tr>\n            </thead>\n            <tbody>\n              ${reportData.slice(0, 100).map((item: any) => `\n                <tr>\n                  ${reportType.includes('sales') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType.includes('purchases') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.suppliers?.name || 'غير محدد'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType === 'inventory' ? `\n                    <td>${item.medicines?.name || 'غير محدد'}</td>\n                    <td>${item.medicines?.category || 'غير محدد'}</td>\n                    <td>${item.quantity}</td>\n                    <td>${new Date(item.expiry_date).toLocaleDateString('ar-EG')}</td>\n                    <td>${item.quantity < 10 ? 'كمية قليلة' : 'طبيعي'}</td>\n                  ` : ''}\n                  ${reportType === 'cashbox' ? `\n                    <td>${item.transaction_type === 'income' ? 'وارد' : 'مصروف'}</td>\n                    <td>${item.category}</td>\n                    <td>${item.description}</td>\n                    <td>${item.transaction_type === 'income' ? '+' : '-'}${item.amount?.toLocaleString()} د.ع</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n\n          ${reportData.length > 100 ? `\n            <div style=\"text-align: center; color: #666; margin-top: 20px;\">\n              تم عرض أول 100 سجل من أصل ${reportData.length} سجل\n            </div>\n          ` : ''}\n        ` : `\n          <div style=\"text-align: center; padding: 50px; color: #666;\">\n            لا توجد بيانات للعرض\n          </div>\n        `}\n\n        <div class=\"footer\">\n          <div>${settings.footerText}</div>\n          <div style=\"margin-top: 10px; font-size: 11px;\">\n            تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AA6BA,MAAM,kBAAiC;IACrC,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,WAAW;IACX,aAAa;IACb,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,aAAa;IACb,aAAa;IACb,WAAW;IACX,iBAAiB;AACnB;AAEO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,MAAM;gBAAC;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,kBAAkB;YAAE,GAAG,QAAQ;YAAE,GAAG,WAAW;QAAC;QACtD,YAAY;QAEZ,IAAI;YACF,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAU,KAAK,SAAS,CAAC,UAAU,MAAM;QAC/C,MAAM,WAAW,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI;oBACF,MAAM,mBAAmB,KAAK,KAAK,CAAC,EAAE,MAAM,EAAE;oBAC9C,eAAe;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,OAAO;QACL;QACA,eAAe;QACf;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,eAAe,OAAO,SAAc,MAAuC;IACtF,IAAI;QACF,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,iFAAiF;QACjF,IAAI,YAAY;QAChB,IAAI,QAAQ,EAAE,IAAI,SAAS,SAAS;YAClC,MAAM,EAAE,uBAAuB,EAAE;YACjC,MAAM,SAAS,MAAM,wBAAwB,QAAQ,EAAE;YACvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,yDAAyD;oBACzD,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;oBAC/D,eAAe,QAAQ,aAAa,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;oBAC/D,iBAAiB,QAAQ,eAAe,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;gBACrE;gBACA,QAAQ,GAAG,CAAC,0DAA0D;YACxE;QACF,OAAO,IAAI,QAAQ,EAAE,IAAI,SAAS,YAAY;YAC5C,MAAM,EAAE,0BAA0B,EAAE;YACpC,MAAM,SAAS,MAAM,2BAA2B,QAAQ,EAAE;YAC1D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,yDAAyD;oBACzD,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;oBAC7D,eAAe,QAAQ,aAAa,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;oBAC/D,iBAAiB,QAAQ,eAAe,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;gBACrE;gBACA,QAAQ,GAAG,CAAC,2DAA2D;YACzE;QACF,OAAO,IAAI,QAAQ,EAAE,IAAI,SAAS,UAAU;YAC1C,MAAM,EAAE,iBAAiB,EAAE;YAC3B,MAAM,SAAS,MAAM,kBAAkB,QAAQ,EAAE;YACjD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,wDAAwD;oBACxD,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;oBAC/D,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;gBACjE;gBACA,QAAQ,GAAG,CAAC,kDAAkD;YAChE;QACF;QAEA,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,gCAAgC;QAChC,MAAM,EAAE,wBAAwB,EAAE;QAClC,MAAM,cAAc,yBAAyB,WAAW,MAAM;QAE9D,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;QAEjB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,8BAA8B;QAC9B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;QAElB,MAAM,EAAE,wBAAwB,EAAE;QAClC,MAAM,cAAc,yBAAyB,SAAS,MAAM;QAE5D,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;IACnB;AACF;AAEO,MAAM,cAAc,CAAC,YAAiB,YAAoB,OAAe;IAC9E,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,CAAC,aAAa;IAElB,gCAAgC;IAChC,MAAM,EAAE,uBAAuB,EAAE;IACjC,MAAM,aAAa,wBAAwB,YAAY,YAAY,OAAO;IAE1E,YAAY,QAAQ,CAAC,KAAK,CAAC;IAC3B,YAAY,QAAQ,CAAC,KAAK;IAC1B,YAAY,KAAK;IACjB,YAAY,KAAK;IACjB,YAAY,KAAK;AACnB;AAEA,MAAM,sBAAsB,CAAC,SAAc,MAA4B;IACrE,MAAM,QAAQ,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB;IAC7F,MAAM,mBAAmB,SAAS,UAC7B,QAAQ,SAAS,EAAE,QAAQ,QAAQ,aAAa,IAAI,cACpD,QAAQ,SAAS,EAAE,QAAQ;IAEhC,OAAO,CAAC;;;;;;aAMG,EAAE,SAAS,UAAU,kBAAkB,iBAAiB,GAAG,EAAE,QAAQ,cAAc,CAAC;;;;;qBAK5E,EAAE,SAAS,QAAQ,KAAK,UAAU,SAAS,SAAS,QAAQ,KAAK,UAAU,SAAS,OAAO;iBAC/F,EAAE,SAAS,SAAS,CAAC;4BACV,EAAE,SAAS,eAAe,CAAC;;;;qBAIlC,EAAE,SAAS,SAAS,KAAK,OAAO,UAAU,SAAS,SAAS,KAAK,YAAY,UAAU,QAAQ;;;UAG1G,EAAE,SAAS,WAAW,GAAG,4BAA4B,GAAG;;;;;UAKxD,EAAE,SAAS,UAAU,GAAG,KAAK,iBAAiB;mCACrB,EAAE,SAAS,WAAW,CAAC;;;;;;sBAMpC,EAAE,SAAS,WAAW,CAAC;;;;;;;;;UASnC,EAAE,SAAS,QAAQ,GAAG,KAAK,iBAAiB;;;;;iBAKrC,EAAE,SAAS,WAAW,CAAC;;;;;;;;;iBASvB,EAAE,SAAS,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;4BAoBZ,EAAE,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU;iBACpE,EAAE,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,CAAC;;;;;;;;;;4BAU1C,EAAE,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU;iBACpE,EAAE,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,CAAC;;;;;;;;UAQ5D,EAAE,SAAS,UAAU,GAAG,KAAK,iBAAiB;;;;;;iCAMvB,EAAE,SAAS,WAAW,CAAC;;;;;;;;;;;4BAW5B,EAAE,SAAS,WAAW,CAAC,MAAM,CAAC,GAAG;oCACzB,EAAE,SAAS,WAAW,CAAC;oCACvB,EAAE,SAAS,cAAc,CAAC;oCAC1B,EAAE,SAAS,YAAY,CAAC;oCACxB,EAAE,SAAS,YAAY,CAAC;;;mCAGzB,EAAE,SAAS,UAAU,kBAAkB,iBAAiB;;;;gDAI3C,EAAE,QAAQ,cAAc,CAAC;2CAC9B,EAAE,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,SAAS;+CACvD,EAAE,QAAQ,cAAc,KAAK,SAAS,UAAU,MAAM;8CACvD,EAAE,QAAQ,cAAc,KAAK,SAAS,UAAU,OAAO;;;yBAG5E,EAAE,SAAS,UAAU,kBAAkB,gBAAgB;iBAC/D,EAAE,iBAAiB;YACxB,EAAE,SAAS,WAAW,QAAQ,SAAS,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG;YACtG,EAAE,SAAS,cAAc,QAAQ,SAAS,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG;;;;;;;;;;;;;;YAczG,EAAE,OAAO,IAAI,CAAC,OAAc,CAAC;;oBAErB,EAAE,SAAS,UACV,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,KAAK,gBAAgB,EAAE,WAAW,QAAQ,aACrF,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,KAAK,SAAS,EAAE,QAAQ,WACvE;oBACG,EAAE,KAAK,QAAQ,CAAC;oBAChB,EAAE,SAAS,UACX,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KACrC,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,GACxD;oBACG,EAAE,SAAS,UACX,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KACtC,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,GAC1D;;YAEL,CAAC,EAAE,KAAK,OAAO,GAAG;;;;;;;gBAOd,EAAE,QAAQ,YAAY,EAAE,iBAAiB;;;;gBAIzC,EAAE,QAAQ,eAAe,EAAE,oBAAoB,EAAE;;;;gBAIjD,EAAE,QAAQ,YAAY,EAAE,iBAAiB;;;;QAIjD,EAAE,QAAQ,KAAK,GAAG,CAAC;;;YAGf,EAAE,QAAQ,KAAK,CAAC;;QAEpB,CAAC,GAAG,GAAG;;;eAGA,EAAE,SAAS,UAAU,CAAC;;8DAEyB,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;;;;EAMrG,CAAC;AACH;AAEA,MAAM,qBAAqB,CAAC,YAAiB,YAAoB,OAAe;IAC9E,OAAO,CAAC;;;;;;aAMG,EAAE,MAAM;;;;;qBAKA,EAAE,SAAS,QAAQ,KAAK,UAAU,SAAS,SAAS,QAAQ,KAAK,UAAU,SAAS,OAAO;iBAC/F,EAAE,SAAS,SAAS,CAAC;4BACV,EAAE,SAAS,eAAe,CAAC;;;;;;;;;;;UAW7C,EAAE,SAAS,UAAU,GAAG,KAAK,iBAAiB;mCACrB,EAAE,SAAS,WAAW,CAAC;;;;;;iBAMzC,EAAE,SAAS,WAAW,CAAC;;;;;;;;iBAQvB,EAAE,SAAS,WAAW,CAAC;;;;;;;;;;;;;;;4BAeZ,EAAE,SAAS,UAAU,GAAG,YAAY,cAAc;;;;;;;;;;iBAU7D,EAAE,SAAS,WAAW,CAAC;;;;;;;;;;;;;;4BAcZ,EAAE,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU;iBACpE,EAAE,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,CAAC;;;;;;;;;UAS5D,EAAE,SAAS,UAAU,GAAG,KAAK,iBAAiB;;;;;;;;;;;;;oCAapB,EAAE,SAAS,WAAW,CAAC;eAC5C,EAAE,SAAS,cAAc,CAAC;;;kCAGP,EAAE,MAAM;;yBAEjB,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;QAG1D,EAAE,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,IAAI,CAAC;;;;yCAIvB,EAAE,WAAW,MAAM,CAAC;;YAEjD,EAAE,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,eAAe,CAAC;;;2CAGvC,EAAE,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc,GAAG;;YAEnI,CAAC,GAAG,GAAG;;;;;;gBAMH,EAAE,WAAW,QAAQ,CAAC,WAAW,CAAC;;;;;;gBAMlC,CAAC,GAAG,GAAG;gBACP,EAAE,WAAW,QAAQ,CAAC,eAAe,CAAC;;;;;;gBAMtC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,cAAc,CAAC;;;;;;gBAMhC,CAAC,GAAG,GAAG;gBACP,EAAE,eAAe,YAAY,CAAC;;;;;;gBAM9B,CAAC,GAAG,GAAG;;;;cAIT,EAAE,WAAW,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;;kBAE3C,EAAE,WAAW,QAAQ,CAAC,WAAW,CAAC;wBAC5B,EAAE,KAAK,cAAc,CAAC;wBACtB,EAAE,KAAK,SAAS,EAAE,QAAQ,KAAK,aAAa,IAAI,YAAY;wBAC5D,EAAE,KAAK,YAAY,EAAE,iBAAiB;wBACtC,EAAE,KAAK,cAAc,KAAK,SAAS,UAAU,OAAO;wBACpD,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;kBAC9D,CAAC,GAAG,GAAG;kBACP,EAAE,WAAW,QAAQ,CAAC,eAAe,CAAC;wBAChC,EAAE,KAAK,cAAc,CAAC;wBACtB,EAAE,KAAK,SAAS,EAAE,QAAQ,WAAW;wBACrC,EAAE,KAAK,YAAY,EAAE,iBAAiB;wBACtC,EAAE,KAAK,cAAc,KAAK,SAAS,UAAU,OAAO;wBACpD,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;kBAC9D,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,cAAc,CAAC;wBAC1B,EAAE,KAAK,SAAS,EAAE,QAAQ,WAAW;wBACrC,EAAE,KAAK,SAAS,EAAE,YAAY,WAAW;wBACzC,EAAE,KAAK,QAAQ,CAAC;wBAChB,EAAE,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS;wBACzD,EAAE,KAAK,QAAQ,GAAG,KAAK,eAAe,QAAQ;kBACpD,CAAC,GAAG,GAAG;kBACP,EAAE,eAAe,YAAY,CAAC;wBACxB,EAAE,KAAK,gBAAgB,KAAK,WAAW,SAAS,QAAQ;wBACxD,EAAE,KAAK,QAAQ,CAAC;wBAChB,EAAE,KAAK,WAAW,CAAC;wBACnB,EAAE,KAAK,gBAAgB,KAAK,WAAW,MAAM,MAAM,KAAK,MAAM,EAAE,iBAAiB;wBACjF,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;kBAC9D,CAAC,GAAG,GAAG;;cAEX,CAAC,EAAE,IAAI,CAAC,IAAI;;;;UAIhB,EAAE,WAAW,MAAM,GAAG,MAAM,CAAC;;wCAEC,EAAE,WAAW,MAAM,CAAC;;UAElD,CAAC,GAAG,GAAG;QACT,CAAC,GAAG,CAAC;;;;QAIL,CAAC,CAAC;;;eAGK,EAAE,SAAS,UAAU,CAAC;;;;;;;;EAQnC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 10140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/exportUtils.ts"], "sourcesContent": ["// Utility functions for exporting data to Excel and other formats\n\nexport interface ExportData {\n  headers: string[]\n  rows: (string | number)[][]\n  filename: string\n  sheetName?: string\n}\n\nexport const exportToCSV = (data: ExportData) => {\n  const csvContent = [\n    data.headers.join(','),\n    ...data.rows.map(row => row.map(cell => `\"${cell}\"`).join(','))\n  ].join('\\n')\n\n  const blob = new Blob(['\\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })\n  const link = document.createElement('a')\n  const url = URL.createObjectURL(blob)\n  \n  link.setAttribute('href', url)\n  link.setAttribute('download', `${data.filename}.csv`)\n  link.style.visibility = 'hidden'\n  \n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n}\n\nexport const exportToExcel = async (data: ExportData) => {\n  try {\n    // Create a simple Excel-compatible format using HTML table\n    const htmlTable = `\n      <table>\n        <thead>\n          <tr>\n            ${data.headers.map(header => `<th>${header}</th>`).join('')}\n          </tr>\n        </thead>\n        <tbody>\n          ${data.rows.map(row => \n            `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`\n          ).join('')}\n        </tbody>\n      </table>\n    `\n\n    const blob = new Blob([htmlTable], { \n      type: 'application/vnd.ms-excel;charset=utf-8;' \n    })\n    \n    const link = document.createElement('a')\n    const url = URL.createObjectURL(blob)\n    \n    link.setAttribute('href', url)\n    link.setAttribute('download', `${data.filename}.xls`)\n    link.style.visibility = 'hidden'\n    \n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    \n    return true\n  } catch (error) {\n    console.error('Error exporting to Excel:', error)\n    return false\n  }\n}\n\n// Export functions for different data types\nexport const exportSalesData = (salesData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'رقم الفاتورة',\n      'التاريخ',\n      'العميل',\n      'المجموع الفرعي',\n      'الخصم',\n      'المجموع النهائي',\n      'طريقة الدفع',\n      'ملاحظات'\n    ],\n    rows: salesData.map(sale => [\n      sale.invoiceNumber || '',\n      sale.date || '',\n      sale.customerName || '',\n      sale.subtotal || 0,\n      sale.discount || 0,\n      sale.finalAmount || 0,\n      sale.paymentMethod || '',\n      sale.notes || ''\n    ]),\n    filename: `مبيعات_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'المبيعات'\n  }\n  \n  return exportToExcel(data)\n}\n\nexport const exportPurchasesData = (purchasesData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'رقم الفاتورة',\n      'التاريخ',\n      'المورد',\n      'المجموع الفرعي',\n      'الخصم',\n      'المجموع النهائي',\n      'طريقة الدفع',\n      'ملاحظات'\n    ],\n    rows: purchasesData.map(purchase => [\n      purchase.invoiceNumber || '',\n      purchase.date || '',\n      purchase.supplierName || '',\n      purchase.subtotal || 0,\n      purchase.discount || 0,\n      purchase.finalAmount || 0,\n      purchase.paymentMethod || '',\n      purchase.notes || ''\n    ]),\n    filename: `مشتريات_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'المشتريات'\n  }\n  \n  return exportToExcel(data)\n}\n\nexport const exportInventoryData = (inventoryData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'اسم الدواء',\n      'الفئة',\n      'الشركة المصنعة',\n      'التركيز',\n      'الشكل',\n      'كود الوجبة',\n      'الكمية',\n      'تاريخ الانتهاء',\n      'سعر التكلفة',\n      'سعر البيع'\n    ],\n    rows: inventoryData.map(item => [\n      item.medicineName || '',\n      item.category || '',\n      item.manufacturer || '',\n      item.strength || '',\n      item.form || '',\n      item.batchCode || '',\n      item.quantity || 0,\n      item.expiryDate || '',\n      item.costPrice || 0,\n      item.sellingPrice || 0\n    ]),\n    filename: `مخزون_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'المخزون'\n  }\n  \n  return exportToExcel(data)\n}\n\nexport const exportCustomersData = (customersData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'اسم العميل',\n      'رقم الهاتف',\n      'البريد الإلكتروني',\n      'العنوان',\n      'ملاحظات',\n      'تاريخ الإضافة'\n    ],\n    rows: customersData.map(customer => [\n      customer.name || '',\n      customer.phone || '',\n      customer.email || '',\n      customer.address || '',\n      customer.notes || '',\n      customer.created_at || ''\n    ]),\n    filename: `عملاء_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'العملاء'\n  }\n  \n  return exportToExcel(data)\n}\n\nexport const exportSuppliersData = (suppliersData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'اسم المورد',\n      'جهة الاتصال',\n      'رقم الهاتف',\n      'البريد الإلكتروني',\n      'العنوان',\n      'ملاحظات',\n      'تاريخ الإضافة'\n    ],\n    rows: suppliersData.map(supplier => [\n      supplier.name || '',\n      supplier.contact_person || '',\n      supplier.phone || '',\n      supplier.email || '',\n      supplier.address || '',\n      supplier.notes || '',\n      supplier.created_at || ''\n    ]),\n    filename: `موردين_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'الموردين'\n  }\n  \n  return exportToExcel(data)\n}\n\nexport const exportReturnsData = (returnsData: any[]) => {\n  const data: ExportData = {\n    headers: [\n      'رقم المرتجع',\n      'النوع',\n      'الفاتورة الأصلية',\n      'العميل/المورد',\n      'المبلغ',\n      'السبب',\n      'الحالة',\n      'التاريخ'\n    ],\n    rows: returnsData.map(returnItem => [\n      returnItem.returnNumber || '',\n      returnItem.type === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات',\n      returnItem.originalInvoiceNumber || '',\n      returnItem.customerOrSupplier || '',\n      returnItem.totalAmount || 0,\n      returnItem.reason || '',\n      returnItem.status === 'pending' ? 'قيد المراجعة' : \n      returnItem.status === 'approved' ? 'مقبول' : 'مرفوض',\n      returnItem.created_at || ''\n    ]),\n    filename: `مرتجعات_${new Date().toISOString().split('T')[0]}`,\n    sheetName: 'المرتجعات'\n  }\n  \n  return exportToExcel(data)\n}\n\n// Print utilities\nexport const printElement = (elementId: string) => {\n  const printContent = document.getElementById(elementId)\n  if (!printContent) {\n    console.error('Element not found for printing')\n    return false\n  }\n\n  const originalContent = document.body.innerHTML\n  document.body.innerHTML = printContent.innerHTML\n  \n  window.print()\n  \n  document.body.innerHTML = originalContent\n  window.location.reload() // Reload to restore event listeners\n  \n  return true\n}\n\nexport const generatePrintableReport = (title: string, data: any[], headers: string[]) => {\n  const printWindow = window.open('', '_blank')\n  if (!printWindow) return false\n\n  const htmlContent = `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }\n        .company-info { margin-bottom: 20px; }\n        table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n        th { background-color: #f2f2f2; font-weight: bold; }\n        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <h1>صيدلية الشفاء</h1>\n        <div class=\"company-info\">\n          <p>بغداد - الكرادة - شارع الرئيسي</p>\n          <p>هاتف: 07901234567 | البريد الإلكتروني: <EMAIL></p>\n        </div>\n        <h2>${title}</h2>\n        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>\n      </div>\n      \n      <table>\n        <thead>\n          <tr>\n            ${headers.map(header => `<th>${header}</th>`).join('')}\n          </tr>\n        </thead>\n        <tbody>\n          ${data.map(row => \n            `<tr>${Object.values(row).map(cell => `<td>${cell}</td>`).join('')}</tr>`\n          ).join('')}\n        </tbody>\n      </table>\n      \n      <div class=\"footer\">\n        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية</p>\n      </div>\n      \n      <script>\n        window.onload = function() {\n          window.print();\n          window.onafterprint = function() {\n            window.close();\n          }\n        }\n      </script>\n    </body>\n    </html>\n  `\n\n  printWindow.document.write(htmlContent)\n  printWindow.document.close()\n  \n  return true\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;;;;;;;;;AAS3D,MAAM,cAAc,CAAC;IAC1B,MAAM,aAAa;QACjB,KAAK,OAAO,CAAC,IAAI,CAAC;WACf,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;KAC3D,CAAC,IAAI,CAAC;IAEP,MAAM,OAAO,IAAI,KAAK;QAAC,WAAW;KAAW,EAAE;QAAE,MAAM;IAA0B;IACjF,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC;IACpD,KAAK,KAAK,CAAC,UAAU,GAAG;IAExB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,2DAA2D;QAC3D,MAAM,YAAY,CAAC;;;;YAIX,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;;;;UAI9D,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,MACd,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,EAC1D,IAAI,CAAC,IAAI;;;IAGjB,CAAC;QAED,MAAM,OAAO,IAAI,KAAK;YAAC;SAAU,EAAE;YACjC,MAAM;QACR;QAEA,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,YAAY,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC;QACpD,KAAK,KAAK,CAAC,UAAU,GAAG;QAExB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,UAAU,GAAG,CAAC,CAAA,OAAQ;gBAC1B,KAAK,aAAa,IAAI;gBACtB,KAAK,IAAI,IAAI;gBACb,KAAK,YAAY,IAAI;gBACrB,KAAK,QAAQ,IAAI;gBACjB,KAAK,QAAQ,IAAI;gBACjB,KAAK,WAAW,IAAI;gBACpB,KAAK,aAAa,IAAI;gBACtB,KAAK,KAAK,IAAI;aACf;QACD,UAAU,CAAC,OAAO,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC5D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,cAAc,GAAG,CAAC,CAAA,WAAY;gBAClC,SAAS,aAAa,IAAI;gBAC1B,SAAS,IAAI,IAAI;gBACjB,SAAS,YAAY,IAAI;gBACzB,SAAS,QAAQ,IAAI;gBACrB,SAAS,QAAQ,IAAI;gBACrB,SAAS,WAAW,IAAI;gBACxB,SAAS,aAAa,IAAI;gBAC1B,SAAS,KAAK,IAAI;aACnB;QACD,UAAU,CAAC,QAAQ,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC7D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,cAAc,GAAG,CAAC,CAAA,OAAQ;gBAC9B,KAAK,YAAY,IAAI;gBACrB,KAAK,QAAQ,IAAI;gBACjB,KAAK,YAAY,IAAI;gBACrB,KAAK,QAAQ,IAAI;gBACjB,KAAK,IAAI,IAAI;gBACb,KAAK,SAAS,IAAI;gBAClB,KAAK,QAAQ,IAAI;gBACjB,KAAK,UAAU,IAAI;gBACnB,KAAK,SAAS,IAAI;gBAClB,KAAK,YAAY,IAAI;aACtB;QACD,UAAU,CAAC,MAAM,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC3D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,cAAc,GAAG,CAAC,CAAA,WAAY;gBAClC,SAAS,IAAI,IAAI;gBACjB,SAAS,KAAK,IAAI;gBAClB,SAAS,KAAK,IAAI;gBAClB,SAAS,OAAO,IAAI;gBACpB,SAAS,KAAK,IAAI;gBAClB,SAAS,UAAU,IAAI;aACxB;QACD,UAAU,CAAC,MAAM,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC3D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,cAAc,GAAG,CAAC,CAAA,WAAY;gBAClC,SAAS,IAAI,IAAI;gBACjB,SAAS,cAAc,IAAI;gBAC3B,SAAS,KAAK,IAAI;gBAClB,SAAS,KAAK,IAAI;gBAClB,SAAS,OAAO,IAAI;gBACpB,SAAS,KAAK,IAAI;gBAClB,SAAS,UAAU,IAAI;aACxB;QACD,UAAU,CAAC,OAAO,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC5D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAEO,MAAM,oBAAoB,CAAC;IAChC,MAAM,OAAmB;QACvB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,YAAY,GAAG,CAAC,CAAA,aAAc;gBAClC,WAAW,YAAY,IAAI;gBAC3B,WAAW,IAAI,KAAK,UAAU,iBAAiB;gBAC/C,WAAW,qBAAqB,IAAI;gBACpC,WAAW,kBAAkB,IAAI;gBACjC,WAAW,WAAW,IAAI;gBAC1B,WAAW,MAAM,IAAI;gBACrB,WAAW,MAAM,KAAK,YAAY,iBAClC,WAAW,MAAM,KAAK,aAAa,UAAU;gBAC7C,WAAW,UAAU,IAAI;aAC1B;QACD,UAAU,CAAC,QAAQ,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC7D,WAAW;IACb;IAEA,OAAO,cAAc;AACvB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,eAAe,SAAS,cAAc,CAAC;IAC7C,IAAI,CAAC,cAAc;QACjB,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,kBAAkB,SAAS,IAAI,CAAC,SAAS;IAC/C,SAAS,IAAI,CAAC,SAAS,GAAG,aAAa,SAAS;IAEhD,OAAO,KAAK;IAEZ,SAAS,IAAI,CAAC,SAAS,GAAG;IAC1B,OAAO,QAAQ,CAAC,MAAM,IAAG,oCAAoC;IAE7D,OAAO;AACT;AAEO,MAAM,0BAA0B,CAAC,OAAe,MAAa;IAClE,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,cAAc,CAAC;;;;;;aAMV,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;YAsBT,EAAE,MAAM;0BACM,EAAE,IAAI,OAAO,kBAAkB,CAAC,SAAS;;;;;;YAMvD,EAAE,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;;;;UAIzD,EAAE,KAAK,GAAG,CAAC,CAAA,MACT,CAAC,IAAI,EAAE,OAAO,MAAM,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,EACzE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;EAkBnB,CAAC;IAED,YAAY,QAAQ,CAAC,KAAK,CAAC;IAC3B,YAAY,QAAQ,CAAC,KAAK;IAE1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 10448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/hooks/useClientDate.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport function useClientDate() {\n  const [mounted, setMounted] = useState(false)\n  const [currentDate, setCurrentDate] = useState('')\n\n  useEffect(() => {\n    setMounted(true)\n    setCurrentDate(new Date().toLocaleDateString('ar-EG'))\n  }, [])\n\n  return {\n    mounted,\n    currentDate,\n    generateInvoiceNumber: () => `INV-${Date.now()}`,\n    getCurrentDateISO: () => new Date().toISOString().split('T')[0],\n    formatNumber: (num: number) => mounted ? num.toLocaleString() : num.toString()\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,eAAe,IAAI,OAAO,kBAAkB,CAAC;IAC/C,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,uBAAuB,IAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QAChD,mBAAmB,IAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/D,cAAc,CAAC,MAAgB,UAAU,IAAI,cAAc,KAAK,IAAI,QAAQ;IAC9E;AACF", "debugId": null}}, {"offset": {"line": 10473, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/app/sales/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport AppLayout from '@/components/AppLayout'\nimport {\n  Plus,\n  Search,\n  ShoppingCart,\n  User,\n  Calculator,\n  Gift,\n  Percent,\n  Printer,\n  Save,\n  X,\n  Calendar,\n  Download\n} from 'lucide-react'\nimport PrintableInvoice from '@/components/PrintableInvoice'\nimport PrintTemplate, { InvoicePrint } from '@/components/PrintTemplate'\nimport { usePrintSettings, printInvoice } from '@/hooks/usePrintSettings'\nimport { exportSalesData } from '@/lib/exportUtils'\nimport { getMedicines, getCustomers, completeSalesTransaction, initializeSystemData } from '@/lib/database'\nimport { useEffect } from 'react'\nimport { useClientDate } from '@/hooks/useClientDate'\n\ninterface InvoiceItem {\n  id: string\n  batchId: string\n  medicineName: string\n  batchCode: string\n  quantity: number\n  giftQuantity: number\n  unitPrice: number\n  totalPrice: number\n  expiryDate: string\n  availableQuantity: number\n}\n\nexport default function SalesPage() {\n  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])\n  const [customerName, setCustomerName] = useState('')\n  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)\n  const [discountAmount, setDiscountAmount] = useState(0)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showCustomerModal, setShowCustomerModal] = useState(false)\n  const [showPrintPreview, setShowPrintPreview] = useState(false)\n  const [currentInvoice, setCurrentInvoice] = useState<any>(null)\n  const [availableMedicines, setAvailableMedicines] = useState<any[]>([])\n  const [customers, setCustomers] = useState<any[]>([])\n  const [loading, setLoading] = useState(false)\n  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'credit'>('cash')\n  const [notes, setNotes] = useState('')\n  const [privateNotes, setPrivateNotes] = useState('')\n  const { settings: printSettings } = usePrintSettings()\n  const { mounted, currentDate, generateInvoiceNumber, getCurrentDateISO, formatNumber } = useClientDate()\n\n  // Load data on component mount\n  useEffect(() => {\n    loadMedicines()\n    loadCustomers()\n  }, [])\n\n  const loadMedicines = async () => {\n    try {\n      // Initialize system data first\n      await initializeSystemData()\n\n      const result = await getMedicines()\n      if (result.success && result.data) {\n        // Transform data to include batches\n        const medicinesWithBatches: any[] = []\n        result.data.forEach((medicine: any) => {\n          if (medicine.medicine_batches && Array.isArray(medicine.medicine_batches)) {\n            medicine.medicine_batches.forEach((batch: any) => {\n              if (batch.quantity > 0) { // Only show batches with available quantity\n                medicinesWithBatches.push({\n                  id: medicine.id,\n                  batchId: batch.id,\n                  name: medicine.name,\n                  batchCode: batch.batch_code,\n                  price: batch.selling_price,\n                  quantity: batch.quantity,\n                  expiry: batch.expiry_date,\n                  category: medicine.category,\n                  manufacturer: medicine.manufacturer\n                })\n              }\n            })\n          }\n        })\n        setAvailableMedicines(medicinesWithBatches)\n      } else {\n        console.error('Failed to load medicines:', result.error)\n        // Use sample data as fallback\n        setAvailableMedicines([\n          { id: '1', batchId: 'b1', name: 'باراسيتامول 500mg', batchCode: 'B001', price: 750, quantity: 150, expiry: '2024-12-15' },\n          { id: '2', batchId: 'b2', name: 'أموكسيسيلين 250mg', batchCode: 'B003', price: 1800, quantity: 200, expiry: '2025-03-10' }\n        ])\n      }\n    } catch (error) {\n      console.error('Error loading medicines:', error)\n      // Use sample data as fallback\n      setAvailableMedicines([\n        { id: '1', batchId: 'b1', name: 'باراسيتامول 500mg', batchCode: 'B001', price: 750, quantity: 150, expiry: '2024-12-15' },\n        { id: '2', batchId: 'b2', name: 'أموكسيسيلين 250mg', batchCode: 'B003', price: 1800, quantity: 200, expiry: '2025-03-10' }\n      ])\n    }\n  }\n\n  const loadCustomers = async () => {\n    try {\n      const result = await getCustomers()\n      if (result.success && result.data) {\n        setCustomers(result.data)\n      } else {\n        console.error('Failed to load customers:', result.error)\n        // Use sample data as fallback\n        setCustomers([\n          { id: '1', name: 'أحمد محمد علي', phone: '07901111111', address: 'بغداد - الكرادة' },\n          { id: '2', name: 'فاطمة حسن محمد', phone: '07802222222', address: 'بغداد - الجادرية' }\n        ])\n      }\n    } catch (error) {\n      console.error('Error loading customers:', error)\n      // Use sample data as fallback\n      setCustomers([\n        { id: '1', name: 'أحمد محمد علي', phone: '07901111111', address: 'بغداد - الكرادة' },\n        { id: '2', name: 'فاطمة حسن محمد', phone: '07802222222', address: 'بغداد - الجادرية' }\n      ])\n    }\n  }\n\n  const filteredMedicines = availableMedicines.filter(medicine =>\n    medicine.name.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  const addToInvoice = (medicine: any) => {\n    // التحقق من توفر الكمية\n    if (medicine.quantity <= 0) {\n      alert('هذا الدواء غير متوفر في المخزون')\n      return\n    }\n\n    const existingItem = invoiceItems.find(item =>\n      item.batchId === medicine.batchId\n    )\n\n    if (existingItem) {\n      // التحقق من عدم تجاوز الكمية المتوفرة\n      const totalRequested = existingItem.quantity + existingItem.giftQuantity + 1\n      if (totalRequested > medicine.quantity) {\n        alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')\n        return\n      }\n\n      setInvoiceItems(items =>\n        items.map(item =>\n          item.batchId === medicine.batchId\n            ? {\n                ...item,\n                quantity: item.quantity + 1,\n                totalPrice: (item.quantity + 1) * item.unitPrice\n              }\n            : item\n        )\n      )\n    } else {\n      const newItem: InvoiceItem = {\n        id: medicine.id,\n        batchId: medicine.batchId,\n        medicineName: medicine.name,\n        batchCode: medicine.batchCode,\n        quantity: 1,\n        giftQuantity: 0,\n        unitPrice: medicine.price,\n        totalPrice: medicine.price,\n        expiryDate: medicine.expiry,\n        availableQuantity: medicine.quantity\n      }\n      setInvoiceItems([...invoiceItems, newItem])\n    }\n    setSearchTerm('')\n  }\n\n  const updateQuantity = (batchId: string, newQuantity: number) => {\n    // السماح بالقيم الفارغة أو الصفر دون حذف العنصر\n    const quantity = Math.max(0, newQuantity || 0)\n\n    setInvoiceItems(items =>\n      items.map(item => {\n        if (item.batchId === batchId) {\n          const totalRequested = quantity + item.giftQuantity\n          if (totalRequested > item.availableQuantity && quantity > 0) {\n            alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')\n            return item\n          }\n          return {\n            ...item,\n            quantity: quantity,\n            totalPrice: quantity * item.unitPrice\n          }\n        }\n        return item\n      })\n    )\n  }\n\n  const updateGiftQuantity = (batchId: string, newGiftQuantity: number) => {\n    if (newGiftQuantity < 0) return\n\n    setInvoiceItems(items =>\n      items.map(item => {\n        if (item.batchId === batchId) {\n          const totalRequested = item.quantity + newGiftQuantity\n          if (totalRequested > item.availableQuantity) {\n            alert('الكمية المطلوبة تتجاوز المتوفر في المخزون')\n            return item\n          }\n          return { ...item, giftQuantity: newGiftQuantity }\n        }\n        return item\n      })\n    )\n  }\n\n  const removeItem = (batchId: string) => {\n    setInvoiceItems(items =>\n      items.filter(item => item.batchId !== batchId)\n    )\n  }\n\n  const calculateSubtotal = () => {\n    return invoiceItems.reduce((total, item) =>\n      total + item.totalPrice, 0\n    )\n  }\n\n  const calculateFinalAmount = () => {\n    return calculateSubtotal() - discountAmount\n  }\n\n  const handleSaveInvoice = async () => {\n    console.log('🔄 بدء عملية حفظ الفاتورة...')\n    console.log('📦 عدد العناصر:', invoiceItems.length)\n    console.log('📋 العناصر:', invoiceItems)\n\n    if (invoiceItems.length === 0) {\n      alert('يرجى إضافة عناصر للفاتورة')\n      return\n    }\n\n    setLoading(true)\n    try {\n      const invoiceNumber = generateInvoiceNumber()\n\n      // Prepare invoice data\n      const invoiceData = {\n        invoice_number: invoiceNumber,\n        customer_id: selectedCustomer?.id,\n        customer_name: selectedCustomer?.name || customerName,\n        total_amount: calculateSubtotal(),\n        discount_amount: discountAmount,\n        final_amount: calculateFinalAmount(),\n        payment_method: paymentMethod,\n        payment_status: paymentMethod === 'cash' ? 'paid' : 'pending',\n        notes: notes,\n        private_notes: privateNotes\n      }\n\n      // Prepare items for database\n      const dbItems = []\n      for (const item of invoiceItems) {\n        // Find medicine name for this item - availableMedicines has flat structure\n        const medicine = availableMedicines.find(m => m.batchId === item.batchId)\n        const medicineName = medicine?.name || 'غير محدد'\n\n        console.log(`🔍 البحث عن الدواء للدفعة ${item.batchId}:`)\n        console.log(`📋 الدواء الموجود:`, medicine)\n        console.log(`💊 اسم الدواء: ${medicineName}`)\n\n        // Add regular sale item\n        if (item.quantity > 0) {\n          dbItems.push({\n            medicine_batch_id: item.batchId,\n            quantity: item.quantity,\n            unit_price: item.unitPrice,\n            total_price: item.totalPrice,\n            is_gift: false,\n            medicine_name: medicineName,\n            medicineName: medicineName\n          })\n        }\n\n        // Add gift item if any\n        if (item.giftQuantity > 0) {\n          dbItems.push({\n            medicine_batch_id: item.batchId,\n            quantity: item.giftQuantity,\n            unit_price: 0,\n            total_price: 0,\n            is_gift: true,\n            medicine_name: medicineName,\n            medicineName: medicineName\n          })\n        }\n      }\n\n      // Save to database\n      console.log('💾 حفظ البيانات في قاعدة البيانات...')\n      console.log('📄 بيانات الفاتورة:', invoiceData)\n      console.log('📦 عناصر الفاتورة:', dbItems)\n\n      const result = await completeSalesTransaction(invoiceData, dbItems)\n      console.log('✅ نتيجة الحفظ:', result)\n\n      if (result.success) {\n        // Create enhanced items for printing with medicine names\n        const enhancedItems = dbItems.map(item => {\n          const medicine = availableMedicines.find(m => m.batchId === item.medicine_batch_id)\n          const medicineName = medicine?.name || item.medicine_name || item.medicineName || 'غير محدد'\n\n          console.log(`🖨️ تحضير عنصر للطباعة:`)\n          console.log(`📋 الدواء الموجود:`, medicine)\n          console.log(`💊 اسم الدواء للطباعة: ${medicineName}`)\n\n          return {\n            id: item.id || `item_${Date.now()}_${Math.random()}`,\n            batchId: item.medicine_batch_id || item.batchId,\n            name: medicineName,\n            quantity: item.quantity,\n            unitPrice: item.unit_price || item.unitPrice,\n            totalPrice: item.total_price || item.totalPrice,\n            isGift: item.is_gift || item.isGift || false,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicine_batches: {\n              batch_code: medicine?.batchCode || '',\n              expiry_date: medicine?.expiry || '',\n              medicines: {\n                name: medicineName,\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        const completeInvoiceData = {\n          ...invoiceData,\n          invoiceNumber,\n          date: getCurrentDateISO(),\n          customerName: selectedCustomer?.name || customerName,\n          customerPhone: selectedCustomer?.phone,\n          customerAddress: selectedCustomer?.address,\n          items: enhancedItems, // Use enhanced items instead of original invoiceItems\n          subtotal: calculateSubtotal(),\n          discount: discountAmount,\n          finalAmount: calculateFinalAmount(),\n          sales_invoice_items: enhancedItems // Use enhanced items for sales_invoice_items too\n        }\n\n        setCurrentInvoice(completeInvoiceData)\n        alert('تم حفظ الفاتورة بنجاح!')\n\n        // Auto print invoice\n        setTimeout(() => {\n          printInvoice(completeInvoiceData, 'sales', printSettings)\n        }, 500)\n\n        // Reload medicines to update quantities\n        await loadMedicines()\n\n        // Clear form\n        setInvoiceItems([])\n        setSelectedCustomer(null)\n        setCustomerName('')\n        setDiscountAmount(0)\n        setPaymentMethod('cash')\n        setNotes('')\n        setPrivateNotes('')\n      } else {\n        console.error('❌ فشل في حفظ الفاتورة:', result.error)\n        const errorMessage = result.error?.message || 'خطأ غير معروف'\n        alert(`حدث خطأ أثناء حفظ الفاتورة:\\n${errorMessage}\\n\\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.`)\n      }\n    } catch (error) {\n      console.error('💥 خطأ غير متوقع في حفظ الفاتورة:', error)\n      alert(`حدث خطأ غير متوقع أثناء حفظ الفاتورة:\\n${error}\\n\\nيرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handlePrintInvoice = () => {\n    if (!currentInvoice && invoiceItems.length > 0) {\n      handleSaveInvoice()\n    }\n    setShowPrintPreview(true)\n  }\n\n  const handleDirectPrint = () => {\n    if (currentInvoice) {\n      printInvoice(currentInvoice, 'sales', printSettings)\n    } else {\n      alert('لا توجد فاتورة للطباعة')\n    }\n  }\n\n  const handleExportInvoice = async () => {\n    if (invoiceItems.length === 0) {\n      alert('لا توجد بيانات للتصدير')\n      return\n    }\n\n    const invoiceData = currentInvoice || {\n      invoiceNumber: generateInvoiceNumber(),\n      date: getCurrentDateISO(),\n      customerName: selectedCustomer?.name || customerName,\n      subtotal: calculateSubtotal(),\n      discount: discountAmount,\n      finalAmount: calculateFinalAmount()\n    }\n\n    const success = await exportSalesData([invoiceData])\n    if (success) {\n      alert('تم تصدير الفاتورة بنجاح!')\n    } else {\n      alert('حدث خطأ أثناء التصدير')\n    }\n  }\n\n  const selectCustomer = (customer: any) => {\n    setSelectedCustomer(customer)\n    setCustomerName(customer.name)\n    setShowCustomerModal(false)\n  }\n\n  const clearCustomer = () => {\n    setSelectedCustomer(null)\n    setCustomerName('')\n  }\n\n  return (\n    <AppLayout>\n      <div className=\"space-y-4 md:space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row md:items-center justify-between gap-3\">\n          <div>\n            <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900\">المبيعات</h1>\n            <p className=\"text-gray-600 mt-1 text-sm md:text-base\">إنشاء فاتورة مبيعات جديدة</p>\n          </div>\n          <div className=\"flex items-center gap-2 text-xs md:text-sm text-gray-500\">\n            <Calendar className=\"h-3 w-3 md:h-4 md:w-4\" />\n            <span>{mounted ? currentDate : ''}</span>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6\">\n          {/* Medicine Search */}\n          <div className=\"lg:col-span-2 space-y-4 md:space-y-6\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 mobile-card\">\n              <h2 className=\"text-base md:text-lg font-semibold text-gray-900 mb-4\">البحث عن الأدوية</h2>\n              <div className=\"relative\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"ابحث عن دواء...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-4 pr-10 py-3 md:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm md:text-base min-h-[48px] md:min-h-[auto]\"\n                />\n              </div>\n\n              {searchTerm && (\n                <div className=\"mt-4 max-h-48 md:max-h-60 overflow-y-auto scroll-container\">\n                  {filteredMedicines.map((medicine) => (\n                    <div\n                      key={`${medicine.id}-${medicine.batchCode}`}\n                      onClick={() => addToInvoice(medicine)}\n                      className=\"flex items-center justify-between p-3 md:p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 min-h-[60px] touch-friendly\"\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"font-medium text-gray-900 text-sm md:text-base\">{medicine.name}</p>\n                        <p className=\"text-xs md:text-sm text-gray-500 mt-1\">\n                          وجبة: {medicine.batchCode} • متوفر: {medicine.quantity} • انتهاء: {medicine.expiry}\n                        </p>\n                      </div>\n                      <div className=\"text-left ml-2\">\n                        <p className=\"font-medium text-gray-900 text-sm md:text-base\">{medicine.price} د.ع</p>\n                        <button className=\"text-blue-600 text-xs md:text-sm hover:text-blue-800 mt-1 px-2 py-1 rounded bg-blue-50 hover:bg-blue-100\">\n                          إضافة\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                  {filteredMedicines.length === 0 && (\n                    <p className=\"text-gray-500 text-center py-4 text-sm md:text-base\">لا توجد نتائج</p>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Invoice Items */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 mobile-card\">\n              <h2 className=\"text-base md:text-lg font-semibold text-gray-900 mb-4\">عناصر الفاتورة</h2>\n\n              {invoiceItems.length === 0 ? (\n                <div className=\"text-center py-6 md:py-8\">\n                  <ShoppingCart className=\"h-10 w-10 md:h-12 md:w-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500 text-sm md:text-base\">لا توجد عناصر في الفاتورة</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {invoiceItems.map((item) => (\n                    <div key={item.batchId} className=\"p-3 md:p-4 border border-gray-200 rounded-lg mobile-card\">\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex-1 pr-2\">\n                          <p className=\"font-medium text-gray-900 text-sm md:text-base\">{item.medicineName}</p>\n                          <p className=\"text-xs md:text-sm text-gray-500 mt-1\">\n                            وجبة: {item.batchCode} • انتهاء: {item.expiryDate} • متوفر: {item.availableQuantity}\n                          </p>\n                        </div>\n                        <button\n                          onClick={() => removeItem(item.batchId)}\n                          className=\"p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 min-h-[44px] min-w-[44px] flex items-center justify-center\"\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4\">\n                        {/* Regular Sale */}\n                        <div className=\"bg-blue-50 p-3 rounded-lg\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <span className=\"text-xs md:text-sm font-medium text-blue-800\">البيع</span>\n                            <span className=\"text-xs md:text-sm text-blue-600\">{item.unitPrice} د.ع/قطعة</span>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <button\n                              onClick={() => updateQuantity(item.batchId, item.quantity - 1)}\n                              className=\"w-10 h-10 md:w-8 md:h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200 touch-friendly\"\n                            >\n                              -\n                            </button>\n                            <input\n                              key={`quantity-${item.batchId}`}\n                              type=\"number\"\n                              value={item.quantity || ''}\n                              onChange={(e) => {\n                                const value = e.target.value\n                                if (value === '') {\n                                  updateQuantity(item.batchId, 0)\n                                } else {\n                                  const numValue = parseInt(value)\n                                  if (!isNaN(numValue)) {\n                                    updateQuantity(item.batchId, numValue)\n                                  }\n                                }\n                              }}\n                              onBlur={(e) => {\n                                if (e.target.value === '' || parseInt(e.target.value) === 0) {\n                                  updateQuantity(item.batchId, 1)\n                                }\n                              }}\n                              className=\"w-16 md:w-16 text-center font-medium border border-gray-300 rounded px-2 py-2 md:py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm min-h-[44px] md:min-h-[auto]\"\n                              min=\"0\"\n                              max={item.availableQuantity}\n                              placeholder=\"1\"\n                            />\n                            <button\n                              onClick={() => updateQuantity(item.batchId, item.quantity + 1)}\n                              className=\"w-10 h-10 md:w-8 md:h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200 touch-friendly\"\n                            >\n                              +\n                            </button>\n                          </div>\n                        </div>\n\n                        {/* Gift */}\n                        <div className=\"bg-green-50 p-3 rounded-lg\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <span className=\"text-xs md:text-sm font-medium text-green-800\">هدية</span>\n                            <span className=\"text-xs md:text-sm text-green-600\">مجاناً</span>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <button\n                              onClick={() => updateGiftQuantity(item.batchId, item.giftQuantity - 1)}\n                              className=\"w-10 h-10 md:w-8 md:h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200 touch-friendly\"\n                            >\n                              -\n                            </button>\n                            <input\n                              type=\"number\"\n                              value={item.giftQuantity || ''}\n                              onChange={(e) => {\n                                const value = e.target.value\n                                if (value === '') {\n                                  updateGiftQuantity(item.batchId, 0)\n                                } else {\n                                  const numValue = parseInt(value)\n                                  if (!isNaN(numValue)) {\n                                    updateGiftQuantity(item.batchId, numValue)\n                                  }\n                                }\n                              }}\n                              className=\"w-16 md:w-16 text-center font-medium border border-gray-300 rounded px-2 py-2 md:py-1 focus:outline-none focus:ring-2 focus:ring-green-500 text-sm min-h-[44px] md:min-h-[auto]\"\n                              min=\"0\"\n                              max={item.availableQuantity}\n                              placeholder=\"0\"\n                            />\n                            <button\n                              onClick={() => updateGiftQuantity(item.batchId, item.giftQuantity + 1)}\n                              className=\"w-10 h-10 md:w-8 md:h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 hover:bg-green-200 touch-friendly\"\n                            >\n                              +\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"mt-3 text-right\">\n                        <span className=\"text-sm text-gray-500\">\n                          إجمالي الكمية: {item.quantity + item.giftQuantity} •\n                          المتبقي: {item.availableQuantity - (item.quantity + item.giftQuantity)}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Invoice Summary */}\n          <div className=\"space-y-4 md:space-y-6\">\n            {/* Customer Info */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 mobile-card\">\n              <h2 className=\"text-base md:text-lg font-semibold text-gray-900 mb-4\">معلومات العميل</h2>\n              <div className=\"space-y-4\">\n                {selectedCustomer ? (\n                  <div className=\"bg-blue-50 p-3 md:p-4 rounded-lg\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <p className=\"font-medium text-gray-900 text-sm md:text-base\">{selectedCustomer.name}</p>\n                        <p className=\"text-xs md:text-sm text-gray-600 mt-1\">{selectedCustomer.phone}</p>\n                        <p className=\"text-xs md:text-sm text-gray-600\">{selectedCustomer.address}</p>\n                      </div>\n                      <button\n                        onClick={clearCustomer}\n                        className=\"text-red-600 hover:text-red-800 p-1 min-h-[44px] min-w-[44px] flex items-center justify-center\"\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <>\n                    <div>\n                      <label className=\"block text-xs md:text-sm font-medium text-gray-700 mb-2\">\n                        اسم العميل\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={customerName}\n                        onChange={(e) => setCustomerName(e.target.value)}\n                        placeholder=\"اسم العميل (اختياري)\"\n                        className=\"w-full px-3 py-3 md:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm md:text-base min-h-[48px] md:min-h-[auto]\"\n                      />\n                    </div>\n                    <button\n                      onClick={() => setShowCustomerModal(true)}\n                      className=\"w-full bg-gray-100 text-gray-700 px-4 py-3 md:py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2 min-h-[48px] text-sm md:text-base\"\n                    >\n                      <User className=\"h-4 w-4\" />\n                      اختيار من قائمة العملاء\n                    </button>\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Invoice Total */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">إجمالي الفاتورة</h2>\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">المجموع الفرعي:</span>\n                  <span className=\"font-medium\">{formatNumber(calculateSubtotal())} د.ع</span>\n                </div>\n                \n                <div className=\"flex items-center gap-2\">\n                  <Percent className=\"h-4 w-4 text-gray-400\" />\n                  <input\n                    type=\"number\"\n                    value={discountAmount}\n                    onChange={(e) => setDiscountAmount(Number(e.target.value))}\n                    placeholder=\"مبلغ الخصم\"\n                    className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-gray-600\">د.ع</span>\n                </div>\n                \n                <div className=\"border-t pt-3\">\n                  <div className=\"flex justify-between text-lg font-bold\">\n                    <span>المجموع النهائي:</span>\n                    <span className=\"text-blue-600\">{formatNumber(calculateFinalAmount())} د.ع</span>\n                  </div>\n                </div>\n\n                {/* Invoice Status */}\n                <div className=\"border-t pt-3 mt-3\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">حالة الفاتورة:</span>\n                    <span className={`font-medium px-2 py-1 rounded-full text-xs ${\n                      invoiceItems.length > 0\n                        ? 'bg-green-100 text-green-700'\n                        : 'bg-orange-100 text-orange-700'\n                    }`}>\n                      {invoiceItems.length > 0 ? '✓ جاهزة للحفظ' : '⚠ تحتاج عناصر'}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm mt-2\">\n                    <span className=\"text-gray-600\">عدد العناصر:</span>\n                    <span className=\"font-medium text-blue-600\">{invoiceItems.length}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Payment Method */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">طريقة الدفع</h2>\n\n              <div className=\"grid grid-cols-2 gap-3\">\n                <button\n                  onClick={() => setPaymentMethod('cash')}\n                  className={`p-3 border-2 rounded-lg text-center transition-colors ${\n                    paymentMethod === 'cash'\n                      ? 'border-green-500 bg-green-50 text-green-700'\n                      : 'border-gray-300 text-gray-600 hover:border-green-300'\n                  }`}\n                >\n                  <div className=\"font-medium\">نقداً</div>\n                  <div className=\"text-sm\">دفع فوري</div>\n                </button>\n\n                <button\n                  onClick={() => setPaymentMethod('credit')}\n                  className={`p-3 border-2 rounded-lg text-center transition-colors ${\n                    paymentMethod === 'credit'\n                      ? 'border-orange-500 bg-orange-50 text-orange-700'\n                      : 'border-gray-300 text-gray-600 hover:border-orange-300'\n                  }`}\n                >\n                  <div className=\"font-medium\">آجل</div>\n                  <div className=\"text-sm\">دفع لاحق</div>\n                </button>\n              </div>\n\n              {paymentMethod === 'credit' && (\n                <div className=\"mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg\">\n                  <p className=\"text-orange-800 text-sm\">\n                    ⚠️ سيتم إضافة هذا المبلغ لحساب العميل كدين\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* Notes */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">الملاحظات</h2>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    ملاحظات عامة (تظهر في الطباعة)\n                  </label>\n                  <textarea\n                    value={notes}\n                    onChange={(e) => setNotes(e.target.value)}\n                    rows={2}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"ملاحظات للعميل...\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    ملاحظات خاصة (للنظام فقط)\n                  </label>\n                  <textarea\n                    value={privateNotes}\n                    onChange={(e) => setPrivateNotes(e.target.value)}\n                    rows={2}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50\"\n                    placeholder=\"ملاحظات داخلية...\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Actions */}\n            <div className=\"space-y-3\">\n              {/* Save Button - Always Visible */}\n              <button\n                onClick={handleSaveInvoice}\n                disabled={invoiceItems.length === 0 || loading}\n                className={`w-full px-4 py-3 rounded-lg flex items-center justify-center gap-2 font-semibold transition-all duration-200 min-h-[48px] ${\n                  invoiceItems.length === 0 || loading\n                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                    : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg transform hover:scale-105'\n                }`}\n              >\n                <Save className=\"h-5 w-5\" />\n                {loading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                    جاري الحفظ...\n                  </>\n                ) : (\n                  'حفظ الفاتورة'\n                )}\n              </button>\n\n              {/* Status Message */}\n              {invoiceItems.length === 0 && (\n                <p className=\"text-center text-sm text-gray-500 bg-gray-50 p-2 rounded-lg\">\n                  أضف عناصر للفاتورة لتفعيل زر الحفظ\n                </p>\n              )}\n\n              <div className=\"grid grid-cols-2 gap-3\">\n                <button\n                  onClick={handlePrintInvoice}\n                  disabled={invoiceItems.length === 0}\n                  className=\"bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n                >\n                  <Printer className=\"h-4 w-4\" />\n                  معاينة وطباعة\n                </button>\n\n                <button\n                  onClick={handleDirectPrint}\n                  disabled={!currentInvoice}\n                  className=\"bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n                >\n                  <Printer className=\"h-4 w-4\" />\n                  طباعة مباشرة\n                </button>\n              </div>\n\n              <button\n                onClick={handleExportInvoice}\n                disabled={invoiceItems.length === 0}\n                className=\"w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n              >\n                <Download className=\"h-4 w-4\" />\n                تصدير للإكسل\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Customer Selection Modal */}\n        {showCustomerModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-96 overflow-y-auto\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">اختيار عميل</h2>\n                <button\n                  onClick={() => setShowCustomerModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-2\">\n                {customers.map((customer) => (\n                  <div\n                    key={customer.id}\n                    onClick={() => selectCustomer(customer)}\n                    className=\"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\"\n                  >\n                    <p className=\"font-medium text-gray-900\">{customer.name}</p>\n                    <p className=\"text-sm text-gray-600\">{customer.phone}</p>\n                    <p className=\"text-sm text-gray-600\">{customer.address}</p>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"mt-4 pt-4 border-t\">\n                <button\n                  onClick={() => setShowCustomerModal(false)}\n                  className=\"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200\"\n                >\n                  إلغاء\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Print Preview Modal */}\n        {showPrintPreview && currentInvoice && (\n          <PrintTemplate\n            title=\"فاتورة مبيعات\"\n            data={currentInvoice}\n            type=\"invoice\"\n            settings={printSettings}\n            onClose={() => setShowPrintPreview(false)}\n          >\n            <InvoicePrint\n              invoice={currentInvoice}\n              type=\"sales\"\n              settings={printSettings}\n            />\n          </PrintTemplate>\n        )}\n      </div>\n    </AppLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AAEA;AAxBA;;;;;;;;;;;AAuCe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,UAAU,aAAa,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IACnD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAErG,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,+BAA+B;YAC/B,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;YAEzB,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;YAChC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,oCAAoC;gBACpC,MAAM,uBAA8B,EAAE;gBACtC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnB,IAAI,SAAS,gBAAgB,IAAI,MAAM,OAAO,CAAC,SAAS,gBAAgB,GAAG;wBACzE,SAAS,gBAAgB,CAAC,OAAO,CAAC,CAAC;4BACjC,IAAI,MAAM,QAAQ,GAAG,GAAG;gCACtB,qBAAqB,IAAI,CAAC;oCACxB,IAAI,SAAS,EAAE;oCACf,SAAS,MAAM,EAAE;oCACjB,MAAM,SAAS,IAAI;oCACnB,WAAW,MAAM,UAAU;oCAC3B,OAAO,MAAM,aAAa;oCAC1B,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,WAAW;oCACzB,UAAU,SAAS,QAAQ;oCAC3B,cAAc,SAAS,YAAY;gCACrC;4BACF;wBACF;oBACF;gBACF;gBACA,sBAAsB;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,OAAO,KAAK;gBACvD,8BAA8B;gBAC9B,sBAAsB;oBACpB;wBAAE,IAAI;wBAAK,SAAS;wBAAM,MAAM;wBAAqB,WAAW;wBAAQ,OAAO;wBAAK,UAAU;wBAAK,QAAQ;oBAAa;oBACxH;wBAAE,IAAI;wBAAK,SAAS;wBAAM,MAAM;wBAAqB,WAAW;wBAAQ,OAAO;wBAAM,UAAU;wBAAK,QAAQ;oBAAa;iBAC1H;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,8BAA8B;YAC9B,sBAAsB;gBACpB;oBAAE,IAAI;oBAAK,SAAS;oBAAM,MAAM;oBAAqB,WAAW;oBAAQ,OAAO;oBAAK,UAAU;oBAAK,QAAQ;gBAAa;gBACxH;oBAAE,IAAI;oBAAK,SAAS;oBAAM,MAAM;oBAAqB,WAAW;oBAAQ,OAAO;oBAAM,UAAU;oBAAK,QAAQ;gBAAa;aAC1H;QACH;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;YAChC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,aAAa,OAAO,IAAI;YAC1B,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,OAAO,KAAK;gBACvD,8BAA8B;gBAC9B,aAAa;oBACX;wBAAE,IAAI;wBAAK,MAAM;wBAAiB,OAAO;wBAAe,SAAS;oBAAkB;oBACnF;wBAAE,IAAI;wBAAK,MAAM;wBAAkB,OAAO;wBAAe,SAAS;oBAAmB;iBACtF;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,8BAA8B;YAC9B,aAAa;gBACX;oBAAE,IAAI;oBAAK,MAAM;oBAAiB,OAAO;oBAAe,SAAS;gBAAkB;gBACnF;oBAAE,IAAI;oBAAK,MAAM;oBAAkB,OAAO;oBAAe,SAAS;gBAAmB;aACtF;QACH;IACF;IAEA,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA,WAClD,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG7D,MAAM,eAAe,CAAC;QACpB,wBAAwB;QACxB,IAAI,SAAS,QAAQ,IAAI,GAAG;YAC1B,MAAM;YACN;QACF;QAEA,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,OACrC,KAAK,OAAO,KAAK,SAAS,OAAO;QAGnC,IAAI,cAAc;YAChB,sCAAsC;YACtC,MAAM,iBAAiB,aAAa,QAAQ,GAAG,aAAa,YAAY,GAAG;YAC3E,IAAI,iBAAiB,SAAS,QAAQ,EAAE;gBACtC,MAAM;gBACN;YACF;YAEA,gBAAgB,CAAA,QACd,MAAM,GAAG,CAAC,CAAA,OACR,KAAK,OAAO,KAAK,SAAS,OAAO,GAC7B;wBACE,GAAG,IAAI;wBACP,UAAU,KAAK,QAAQ,GAAG;wBAC1B,YAAY,CAAC,KAAK,QAAQ,GAAG,CAAC,IAAI,KAAK,SAAS;oBAClD,IACA;QAGV,OAAO;YACL,MAAM,UAAuB;gBAC3B,IAAI,SAAS,EAAE;gBACf,SAAS,SAAS,OAAO;gBACzB,cAAc,SAAS,IAAI;gBAC3B,WAAW,SAAS,SAAS;gBAC7B,UAAU;gBACV,cAAc;gBACd,WAAW,SAAS,KAAK;gBACzB,YAAY,SAAS,KAAK;gBAC1B,YAAY,SAAS,MAAM;gBAC3B,mBAAmB,SAAS,QAAQ;YACtC;YACA,gBAAgB;mBAAI;gBAAc;aAAQ;QAC5C;QACA,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC,SAAiB;QACvC,gDAAgD;QAChD,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,eAAe;QAE5C,gBAAgB,CAAA,QACd,MAAM,GAAG,CAAC,CAAA;gBACR,IAAI,KAAK,OAAO,KAAK,SAAS;oBAC5B,MAAM,iBAAiB,WAAW,KAAK,YAAY;oBACnD,IAAI,iBAAiB,KAAK,iBAAiB,IAAI,WAAW,GAAG;wBAC3D,MAAM;wBACN,OAAO;oBACT;oBACA,OAAO;wBACL,GAAG,IAAI;wBACP,UAAU;wBACV,YAAY,WAAW,KAAK,SAAS;oBACvC;gBACF;gBACA,OAAO;YACT;IAEJ;IAEA,MAAM,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,kBAAkB,GAAG;QAEzB,gBAAgB,CAAA,QACd,MAAM,GAAG,CAAC,CAAA;gBACR,IAAI,KAAK,OAAO,KAAK,SAAS;oBAC5B,MAAM,iBAAiB,KAAK,QAAQ,GAAG;oBACvC,IAAI,iBAAiB,KAAK,iBAAiB,EAAE;wBAC3C,MAAM;wBACN,OAAO;oBACT;oBACA,OAAO;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAgB;gBAClD;gBACA,OAAO;YACT;IAEJ;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB,CAAA,QACd,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;IAE1C;IAEA,MAAM,oBAAoB;QACxB,OAAO,aAAa,MAAM,CAAC,CAAC,OAAO,OACjC,QAAQ,KAAK,UAAU,EAAE;IAE7B;IAEA,MAAM,uBAAuB;QAC3B,OAAO,sBAAsB;IAC/B;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB,aAAa,MAAM;QAClD,QAAQ,GAAG,CAAC,eAAe;QAE3B,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM;YACN;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,gBAAgB;YAEtB,uBAAuB;YACvB,MAAM,cAAc;gBAClB,gBAAgB;gBAChB,aAAa,kBAAkB;gBAC/B,eAAe,kBAAkB,QAAQ;gBACzC,cAAc;gBACd,iBAAiB;gBACjB,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB,kBAAkB,SAAS,SAAS;gBACpD,OAAO;gBACP,eAAe;YACjB;YAEA,6BAA6B;YAC7B,MAAM,UAAU,EAAE;YAClB,KAAK,MAAM,QAAQ,aAAc;gBAC/B,2EAA2E;gBAC3E,MAAM,WAAW,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;gBACxE,MAAM,eAAe,UAAU,QAAQ;gBAEvC,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;gBACxD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,EAAE;gBAClC,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,cAAc;gBAE5C,wBAAwB;gBACxB,IAAI,KAAK,QAAQ,GAAG,GAAG;oBACrB,QAAQ,IAAI,CAAC;wBACX,mBAAmB,KAAK,OAAO;wBAC/B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,SAAS;wBAC1B,aAAa,KAAK,UAAU;wBAC5B,SAAS;wBACT,eAAe;wBACf,cAAc;oBAChB;gBACF;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,YAAY,GAAG,GAAG;oBACzB,QAAQ,IAAI,CAAC;wBACX,mBAAmB,KAAK,OAAO;wBAC/B,UAAU,KAAK,YAAY;wBAC3B,YAAY;wBACZ,aAAa;wBACb,SAAS;wBACT,eAAe;wBACf,cAAc;oBAChB;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa;YAC3D,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,OAAO,OAAO,EAAE;gBAClB,yDAAyD;gBACzD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA;oBAChC,MAAM,WAAW,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,iBAAiB;oBAClF,MAAM,eAAe,UAAU,QAAQ,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;oBAElF,QAAQ,GAAG,CAAC,CAAC,uBAAuB,CAAC;oBACrC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,EAAE;oBAClC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc;oBAEpD,OAAO;wBACL,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI;wBACpD,SAAS,KAAK,iBAAiB,IAAI,KAAK,OAAO;wBAC/C,MAAM;wBACN,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,UAAU,IAAI,KAAK,SAAS;wBAC5C,YAAY,KAAK,WAAW,IAAI,KAAK,UAAU;wBAC/C,QAAQ,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI;wBACvC,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,YAAY,UAAU,aAAa;4BACnC,aAAa,UAAU,UAAU;4BACjC,WAAW;gCACT,MAAM;gCACN,UAAU,UAAU,YAAY;gCAChC,cAAc,UAAU,gBAAgB;gCACxC,UAAU,UAAU,YAAY;gCAChC,MAAM,UAAU,QAAQ;4BAC1B;wBACF;oBACF;gBACF;gBAEA,MAAM,sBAAsB;oBAC1B,GAAG,WAAW;oBACd;oBACA,MAAM;oBACN,cAAc,kBAAkB,QAAQ;oBACxC,eAAe,kBAAkB;oBACjC,iBAAiB,kBAAkB;oBACnC,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,aAAa;oBACb,qBAAqB,cAAc,iDAAiD;gBACtF;gBAEA,kBAAkB;gBAClB,MAAM;gBAEN,qBAAqB;gBACrB,WAAW;oBACT,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE,qBAAqB,SAAS;gBAC7C,GAAG;gBAEH,wCAAwC;gBACxC,MAAM;gBAEN,aAAa;gBACb,gBAAgB,EAAE;gBAClB,oBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;gBAClB,iBAAiB;gBACjB,SAAS;gBACT,gBAAgB;YAClB,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B,OAAO,KAAK;gBACpD,MAAM,eAAe,OAAO,KAAK,EAAE,WAAW;gBAC9C,MAAM,CAAC,6BAA6B,EAAE,aAAa,qDAAqD,CAAC;YAC3G;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,CAAC,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;QACxG,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,kBAAkB,aAAa,MAAM,GAAG,GAAG;YAC9C;QACF;QACA,oBAAoB;IACtB;IAEA,MAAM,oBAAoB;QACxB,IAAI,gBAAgB;YAClB,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,SAAS;QACxC,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM;YACN;QACF;QAEA,MAAM,cAAc,kBAAkB;YACpC,eAAe;YACf,MAAM;YACN,cAAc,kBAAkB,QAAQ;YACxC,UAAU;YACV,UAAU;YACV,aAAa;QACf;QAEA,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;YAAC;SAAY;QACnD,IAAI,SAAS;YACX,MAAM;QACR,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,gBAAgB,SAAS,IAAI;QAC7B,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,qBACE,8OAAC,+HAAA,CAAA,UAAS;kBACR,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAEzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,UAAU,cAAc;;;;;;;;;;;;;;;;;;8BAInC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;wCAIb,4BACC,8OAAC;4CAAI,WAAU;;gDACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;wDAEC,SAAS,IAAM,aAAa;wDAC5B,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAkD,SAAS,IAAI;;;;;;kFAC5E,8OAAC;wEAAE,WAAU;;4EAAwC;4EAC5C,SAAS,SAAS;4EAAC;4EAAW,SAAS,QAAQ;4EAAC;4EAAY,SAAS,MAAM;;;;;;;;;;;;;0EAGtF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAAkD,SAAS,KAAK;4EAAC;;;;;;;kFAC9E,8OAAC;wEAAO,WAAU;kFAA2G;;;;;;;;;;;;;uDAZ1H,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;;;;;gDAkB9C,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC;oDAAE,WAAU;8DAAsD;;;;;;;;;;;;;;;;;;8CAO3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;wCAErE,aAAa,MAAM,KAAK,kBACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;iEAGpD,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;oDAAuB,WAAU;;sEAChC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkD,KAAK,YAAY;;;;;;sFAChF,8OAAC;4EAAE,WAAU;;gFAAwC;gFAC5C,KAAK,SAAS;gFAAC;gFAAY,KAAK,UAAU;gFAAC;gFAAW,KAAK,iBAAiB;;;;;;;;;;;;;8EAGvF,8OAAC;oEACC,SAAS,IAAM,WAAW,KAAK,OAAO;oEACtC,WAAU;8EAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIjB,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAA+C;;;;;;8FAC/D,8OAAC;oFAAK,WAAU;;wFAAoC,KAAK,SAAS;wFAAC;;;;;;;;;;;;;sFAErE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,SAAS,IAAM,eAAe,KAAK,OAAO,EAAE,KAAK,QAAQ,GAAG;oFAC5D,WAAU;8FACX;;;;;;8FAGD,8OAAC;oFAEC,MAAK;oFACL,OAAO,KAAK,QAAQ,IAAI;oFACxB,UAAU,CAAC;wFACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wFAC5B,IAAI,UAAU,IAAI;4FAChB,eAAe,KAAK,OAAO,EAAE;wFAC/B,OAAO;4FACL,MAAM,WAAW,SAAS;4FAC1B,IAAI,CAAC,MAAM,WAAW;gGACpB,eAAe,KAAK,OAAO,EAAE;4FAC/B;wFACF;oFACF;oFACA,QAAQ,CAAC;wFACP,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK,MAAM,GAAG;4FAC3D,eAAe,KAAK,OAAO,EAAE;wFAC/B;oFACF;oFACA,WAAU;oFACV,KAAI;oFACJ,KAAK,KAAK,iBAAiB;oFAC3B,aAAY;mFAtBP,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;;;;;8FAwBjC,8OAAC;oFACC,SAAS,IAAM,eAAe,KAAK,OAAO,EAAE,KAAK,QAAQ,GAAG;oFAC5D,WAAU;8FACX;;;;;;;;;;;;;;;;;;8EAOL,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAgD;;;;;;8FAChE,8OAAC;oFAAK,WAAU;8FAAoC;;;;;;;;;;;;sFAEtD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,SAAS,IAAM,mBAAmB,KAAK,OAAO,EAAE,KAAK,YAAY,GAAG;oFACpE,WAAU;8FACX;;;;;;8FAGD,8OAAC;oFACC,MAAK;oFACL,OAAO,KAAK,YAAY,IAAI;oFAC5B,UAAU,CAAC;wFACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wFAC5B,IAAI,UAAU,IAAI;4FAChB,mBAAmB,KAAK,OAAO,EAAE;wFACnC,OAAO;4FACL,MAAM,WAAW,SAAS;4FAC1B,IAAI,CAAC,MAAM,WAAW;gGACpB,mBAAmB,KAAK,OAAO,EAAE;4FACnC;wFACF;oFACF;oFACA,WAAU;oFACV,KAAI;oFACJ,KAAK,KAAK,iBAAiB;oFAC3B,aAAY;;;;;;8FAEd,8OAAC;oFACC,SAAS,IAAM,mBAAmB,KAAK,OAAO,EAAE,KAAK,YAAY,GAAG;oFACpE,WAAU;8FACX;;;;;;;;;;;;;;;;;;;;;;;;sEAOP,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;oEAAwB;oEACtB,KAAK,QAAQ,GAAG,KAAK,YAAY;oEAAC;oEACxC,KAAK,iBAAiB,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,YAAY;;;;;;;;;;;;;mDA7GjE,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;sCAwHhC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDACZ,iCACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAkD,iBAAiB,IAAI;;;;;;8EACpF,8OAAC;oEAAE,WAAU;8EAAyC,iBAAiB,KAAK;;;;;;8EAC5E,8OAAC;oEAAE,WAAU;8EAAoC,iBAAiB,OAAO;;;;;;;;;;;;sEAE3E,8OAAC;4DACC,SAAS;4DACT,WAAU;sEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;qEAKnB;;kEACE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA0D;;;;;;0EAG3E,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC/C,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,8OAAC;wDACC,SAAS,IAAM,qBAAqB;wDACpC,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;8CAStC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAe,aAAa;gEAAqB;;;;;;;;;;;;;8DAGnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;4DACxD,aAAY;4DACZ,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAGlC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB,aAAa;oEAAwB;;;;;;;;;;;;;;;;;;8DAK1E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAC3D,aAAa,MAAM,GAAG,IAClB,gCACA,iCACJ;8EACC,aAAa,MAAM,GAAG,IAAI,kBAAkB;;;;;;;;;;;;sEAGjD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAA6B,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAW,CAAC,sDAAsD,EAChE,kBAAkB,SACd,gDACA,wDACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAU;;;;;;;;;;;;8DAG3B,8OAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAW,CAAC,sDAAsD,EAChE,kBAAkB,WACd,mDACA,yDACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAU;;;;;;;;;;;;;;;;;;wCAI5B,kBAAkB,0BACjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAQ7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4DACxC,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,SAAS;4CACT,UAAU,aAAa,MAAM,KAAK,KAAK;4CACvC,WAAW,CAAC,0HAA0H,EACpI,aAAa,MAAM,KAAK,KAAK,UACzB,iDACA,sFACJ;;8DAEF,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,wBACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAkE;;mEAInF;;;;;;;wCAKH,aAAa,MAAM,KAAK,mBACvB,8OAAC;4CAAE,WAAU;sDAA8D;;;;;;sDAK7E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU,aAAa,MAAM,KAAK;oDAClC,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAIjC,8OAAC;oDACC,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAKnC,8OAAC;4CACC,SAAS;4CACT,UAAU,aAAa,MAAM,KAAK;4CAClC,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;gBAQvC,mCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wCAEC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC;gDAAE,WAAU;0DAA6B,SAAS,IAAI;;;;;;0DACvD,8OAAC;gDAAE,WAAU;0DAAyB,SAAS,KAAK;;;;;;0DACpD,8OAAC;gDAAE,WAAU;0DAAyB,SAAS,OAAO;;;;;;;uCANjD,SAAS,EAAE;;;;;;;;;;0CAWtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;gBASR,oBAAoB,gCACnB,8OAAC,mIAAA,CAAA,UAAa;oBACZ,OAAM;oBACN,MAAM;oBACN,MAAK;oBACL,UAAU;oBACV,SAAS,IAAM,oBAAoB;8BAEnC,cAAA,8OAAC,mIAAA,CAAA,eAAY;wBACX,SAAS;wBACT,MAAK;wBACL,UAAU;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}]}