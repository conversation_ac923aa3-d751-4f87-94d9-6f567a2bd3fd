'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import { Menu, X, CheckCircle, AlertCircle } from 'lucide-react'

export default function TestSidebarPage() {
  const [testLog, setTestLog] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestLog(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const testSidebarButton = () => {
    addLog('🔍 اختبار زر الشريط الجانبي...')
    addLog('📝 تحقق من وحدة التحكم (Console) للرسائل')
    addLog('👆 اضغط على زر القائمة (☰) في الهيدر')
    addLog('👀 يجب أن يظهر/يختفي الشريط الجانبي')
  }

  const clearLog = () => {
    setTestLog([])
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <Menu className="h-8 w-8" />
            <div>
              <h1 className="text-2xl md:text-3xl font-bold">اختبار الشريط الجانبي</h1>
              <p className="text-blue-100 mt-1">التأكد من عمل زر القائمة بشكل صحيح</p>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-2">تعليمات الاختبار</h3>
              <div className="text-yellow-700 space-y-2 text-sm">
                <p><strong>1. ابحث عن زر القائمة (☰)</strong> في الزاوية اليسرى من الهيدر</p>
                <p><strong>2. اضغط على الزر</strong> ويجب أن يظهر الشريط الجانبي من اليمين</p>
                <p><strong>3. اضغط مرة أخرى</strong> ويجب أن يختفي الشريط الجانبي</p>
                <p><strong>4. افتح وحدة التحكم (F12)</strong> لرؤية رسائل التشخيص</p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">أدوات الاختبار</h2>
          <div className="flex gap-4">
            <button
              onClick={testSidebarButton}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Menu className="h-5 w-5" />
              بدء اختبار الشريط الجانبي
            </button>
            
            <button
              onClick={clearLog}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              مسح السجل
            </button>
          </div>
        </div>

        {/* Test Log */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">سجل الاختبار</h2>
          {testLog.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>لم يتم تشغيل أي اختبارات بعد</p>
              <p className="text-sm mt-1">اضغط على "بدء اختبار الشريط الجانبي" للبدء</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testLog.map((log, index) => (
                <div
                  key={index}
                  className="p-3 bg-gray-50 rounded-lg border border-gray-200 font-mono text-sm"
                >
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Debug Info */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="font-medium text-gray-900 mb-4">معلومات التشخيص</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">موقع زر القائمة:</p>
              <p className="text-gray-600">الهيدر → الجانب الأيسر → أول زر</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">شكل الزر:</p>
              <p className="text-gray-600">أيقونة ثلاث خطوط أفقية (☰)</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">سلوك الشريط الجانبي:</p>
              <p className="text-gray-600">ينزلق من اليمين عند الفتح</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">طريقة الإغلاق:</p>
              <p className="text-gray-600">الضغط على الزر أو خارج الشريط</p>
            </div>
          </div>
        </div>

        {/* Visual Guide */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="font-medium text-gray-900 mb-4">دليل مرئي</h3>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <Menu className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium text-blue-900">زر القائمة (مغلق)</p>
                <p className="text-blue-700 text-sm">اضغط لفتح الشريط الجانبي</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-red-50 rounded-lg">
              <div className="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center">
                <X className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium text-red-900">زر القائمة (مفتوح)</p>
                <p className="text-red-700 text-sm">اضغط لإغلاق الشريط الجانبي</p>
              </div>
            </div>
          </div>
        </div>

        {/* Browser Console Instructions */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-green-800 mb-2">فتح وحدة التحكم</h3>
              <div className="text-green-700 space-y-1 text-sm">
                <p><strong>Windows/Linux:</strong> اضغط F12 أو Ctrl+Shift+I</p>
                <p><strong>Mac:</strong> اضغط Cmd+Option+I</p>
                <p><strong>أو:</strong> انقر بالزر الأيمن → "فحص العنصر" → تبويب "Console"</p>
                <p className="mt-2 font-medium">ستظهر رسائل مثل: "Toggle mobile menu clicked, current state: false"</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
