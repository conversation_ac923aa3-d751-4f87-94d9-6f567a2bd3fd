'use client'

import { useState, useEffect } from 'react'
import { Menu, X, AlertCircle } from 'lucide-react'

export default function DebugFullPage() {
  const [isOpen, setIsOpen] = useState(false)
  const [debugInfo, setDebugInfo] = useState<string[]>([])

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`])
  }

  const toggle = () => {
    addDebugInfo(`🔄 Toggle called - Current: ${isOpen} → New: ${!isOpen}`)
    setIsOpen(!isOpen)
  }

  useEffect(() => {
    addDebugInfo(`📱 Component mounted`)
  }, [])

  useEffect(() => {
    addDebugInfo(`🔄 State changed to: ${isOpen}`)
  }, [isOpen])

  const testSidebarManually = () => {
    addDebugInfo('🔧 Manual test started')
    const sidebar = document.getElementById('test-sidebar')
    if (sidebar) {
      addDebugInfo('✅ Sidebar element found')
      const currentTransform = sidebar.style.transform
      addDebugInfo(`📏 Current transform: ${currentTransform}`)
      
      if (currentTransform === 'translateX(0px)' || currentTransform === '') {
        sidebar.style.transform = 'translateX(100%)'
        addDebugInfo('➡️ Set to hidden (translateX(100%))')
      } else {
        sidebar.style.transform = 'translateX(0px)'
        addDebugInfo('⬅️ Set to visible (translateX(0px))')
      }
    } else {
      addDebugInfo('❌ Sidebar element not found')
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 relative">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-40">
        <div className="flex items-center justify-between h-full px-6">
          <div className="flex items-center gap-4">
            {/* Menu Button */}
            <button
              onClick={toggle}
              className="p-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors border-2 border-blue-500 bg-blue-200"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
            <span className="text-lg font-bold">اختبار شامل للشريط الجانبي</span>
          </div>
          <div className="text-sm bg-blue-100 px-3 py-1 rounded">
            الحالة: {isOpen ? '🟢 مفتوح' : '🔴 مغلق'}
          </div>
        </div>
      </header>

      {/* Sidebar - Method 1: CSS Classes */}
      <div
        id="test-sidebar"
        className={`
          bg-white shadow-2xl h-screen w-64 fixed right-0 top-0 z-50 border-l-4 border-blue-500
          transform transition-transform duration-500 ease-in-out
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
      >
        <div className="p-6 h-full overflow-y-auto">
          <div className="flex items-center justify-between mb-6 p-4 bg-blue-100 rounded-lg">
            <h2 className="text-xl font-bold text-blue-900">الشريط الجانبي</h2>
            <button
              onClick={toggle}
              className="p-2 hover:bg-blue-200 rounded-lg text-blue-700"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-100 rounded-lg border border-green-300">
              <p className="text-green-800 font-medium">✅ الشريط الجانبي مرئي!</p>
              <p className="text-green-600 text-sm mt-1">الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>
              <p className="text-green-600 text-sm">CSS Class: {isOpen ? 'translate-x-0' : 'translate-x-full'}</p>
            </div>
            
            <div className="p-4 bg-blue-100 rounded-lg border border-blue-300">
              <p className="text-blue-800 font-medium">🔧 أدوات التحكم</p>
              <div className="space-y-2 mt-2">
                <button
                  onClick={toggle}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  تبديل الحالة
                </button>
                <button
                  onClick={testSidebarManually}
                  className="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
                >
                  اختبار يدوي
                </button>
              </div>
            </div>

            <div className="p-4 bg-yellow-100 rounded-lg border border-yellow-300">
              <p className="text-yellow-800 font-medium">📊 معلومات التشخيص</p>
              <div className="text-yellow-700 text-sm mt-2 space-y-1">
                <p>• React State: {isOpen ? 'true' : 'false'}</p>
                <p>• CSS Class: {isOpen ? 'translate-x-0' : 'translate-x-full'}</p>
                <p>• Z-Index: 50</p>
                <p>• Position: fixed right-0 top-0</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar - Method 2: Inline Styles */}
      <div
        id="test-sidebar-2"
        className="bg-red-50 shadow-2xl h-screen w-64 fixed right-0 top-0 z-40 border-l-4 border-red-500"
        style={{
          transform: isOpen ? 'translateX(-270px)' : 'translateX(0px)',
          transition: 'transform 0.3s ease-in-out'
        }}
      >
        <div className="p-6">
          <div className="p-4 bg-red-100 rounded-lg">
            <h3 className="text-red-800 font-bold">الشريط الثاني (Inline Style)</h3>
            <p className="text-red-600 text-sm">Transform: {isOpen ? 'translateX(-270px)' : 'translateX(0px)'}</p>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={toggle}
        />
      )}

      {/* Main Content */}
      <main className="pt-20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Control Panel */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h1 className="text-2xl font-bold mb-4 flex items-center gap-2">
                <AlertCircle className="h-6 w-6 text-blue-600" />
                لوحة التحكم
              </h1>
              
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h2 className="font-semibold mb-2">الحالة الحالية:</h2>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${isOpen ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span>React State: {isOpen ? 'مفتوح' : 'مغلق'}</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      CSS Transform: {isOpen ? 'translate-x-0' : 'translate-x-full'}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <button
                    onClick={toggle}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    {isOpen ? 'إغلاق الشريط الجانبي' : 'فتح الشريط الجانبي'}
                  </button>
                  
                  <button
                    onClick={testSidebarManually}
                    className="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                  >
                    اختبار يدوي للشريط
                  </button>
                  
                  <button
                    onClick={() => {
                      setDebugInfo([])
                      addDebugInfo('🧹 Debug log cleared')
                    }}
                    className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                  >
                    مسح سجل التشخيص
                  </button>
                </div>
              </div>
            </div>

            {/* Debug Log */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold mb-4">سجل التشخيص</h2>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {debugInfo.length === 0 ? (
                  <p className="text-gray-500">لا توجد رسائل تشخيص...</p>
                ) : (
                  debugInfo.map((info, index) => (
                    <div key={index} className="mb-1">
                      {info}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-bold text-yellow-800 mb-3">تعليمات الاختبار:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-700 text-sm">
              <div>
                <h4 className="font-medium mb-2">الاختبارات الأساسية:</h4>
                <ul className="space-y-1">
                  <li>• اضغط على الزر الأزرق في الهيدر</li>
                  <li>• راقب تغيير الحالة في الهيدر</li>
                  <li>• تحقق من ظهور الشريط الأبيض من اليمين</li>
                  <li>• جرب الضغط على الخلفية المظلمة</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">اختبارات متقدمة:</h4>
                <ul className="space-y-1">
                  <li>• استخدم "اختبار يدوي" إذا لم يعمل الأساسي</li>
                  <li>• راقب سجل التشخيص للأخطاء</li>
                  <li>• تحقق من وجود شريطين (أبيض وأحمر)</li>
                  <li>• افتح أدوات المطور (F12) للمزيد</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
