@import "tailwindcss";
:root {
    --background: #ffffff;
    --foreground: #171717;
}


/* Custom Animations */

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}


/* Background Grid Pattern */

.bg-grid-pattern {
    background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}


/* Glassmorphism Effect */

.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}


/* Custom Scrollbar */

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}


/* Hover Effects */

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}


/* Enhanced Text Colors */

.text-dark {
    color: #1a1a1a !important;
}

.text-darker {
    color: #0f0f0f !important;
}


/* Form Input Enhancements */

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
textarea,
select {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    color: #0f0f0f !important;
    font-weight: 600 !important;
}


/* Placeholder Text */

input::placeholder,
textarea::placeholder {
    color: #6b7280 !important;
    font-weight: 400 !important;
}


/* Label Text */

label {
    color: #1f2937 !important;
    font-weight: 600 !important;
}


/* Table Text */

table th {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

table td {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}


/* Button Text */

button {
    font-weight: 600 !important;
}


/* Mobile Responsive Enhancements */

@media (max-width: 768px) {
    /* Mobile Typography */
    h1 {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important;
    }
    h2 {
        font-size: 1.5rem !important;
        line-height: 2rem !important;
    }
    h3 {
        font-size: 1.25rem !important;
        line-height: 1.75rem !important;
    }
    /* Mobile Form Inputs */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="number"],
    textarea,
    select {
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        padding: 12px !important;
        min-height: 44px !important;
        /* Touch target size */
    }
    /* Mobile Buttons */
    button {
        min-height: 44px !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
    }
    /* Mobile Tables */
    table {
        font-size: 14px !important;
    }
    /* Mobile Cards */
    .card {
        padding: 16px !important;
        margin: 8px !important;
    }
    /* Mobile Modals */
    .modal {
        margin: 16px !important;
        max-height: calc(100vh - 32px) !important;
    }
}

@media (max-width: 640px) {
    /* Small Mobile Adjustments */
    .container {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
    /* Smaller text for very small screens */
    table {
        font-size: 12px !important;
    }
    /* Stack elements vertically */
    .flex-mobile-stack {
        flex-direction: column !important;
        gap: 8px !important;
    }
}


/* Touch-friendly enhancements */

@media (hover: none) and (pointer: coarse) {
    /* Touch device specific styles */
    button:hover {
        transform: none !important;
    }
    .hover-lift:hover {
        transform: none !important;
    }
    /* Larger touch targets */
    a,
    button,
    input,
    select,
    textarea {
        min-height: 44px !important;
    }
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
     :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: Arial, Helvetica, sans-serif;
    overflow-x: hidden;
    /* منع التمرير الأفقي */
}


/* Mobile-specific improvements */

@media (max-width: 768px) {
    /* Better mobile table handling */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
    /* Mobile-friendly cards */
    .mobile-card {
        margin: 8px !important;
        padding: 16px !important;
        border-radius: 12px !important;
    }
    /* Mobile content spacing */
    .mobile-content {
        padding: 12px !important;
    }
    /* Stack buttons vertically on mobile */
    .button-group-mobile {
        flex-direction: column !important;
        gap: 8px !important;
    }
    /* Mobile modal adjustments */
    .mobile-modal {
        margin: 8px !important;
        max-height: calc(100vh - 16px) !important;
    }
}


/* Very small screens */

@media (max-width: 480px) {
    .mobile-content {
        padding: 8px !important;
    }
    /* Smaller text for very small screens */
    h1 {
        font-size: 1.5rem !important;
    }
    h2 {
        font-size: 1.25rem !important;
    }
}

/* Enhanced Mobile Support */

/* Prevent horizontal scroll on all devices */
html, body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Mobile-first approach for containers */
.container {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    /* Sidebar adjustments for mobile */
    .sidebar-mobile {
        width: 280px !important;
        height: 100vh !important;
        position: fixed !important;
        top: 0 !important;
        right: 0 !important;
        z-index: 50 !important;
        transform: translateX(100%) !important;
        transition: transform 0.3s ease-in-out !important;
    }

    .sidebar-mobile.open {
        transform: translateX(0) !important;
    }

    /* Header mobile adjustments */
    .header-mobile {
        padding: 0.75rem 1rem !important;
        height: 60px !important;
    }

    /* Main content mobile spacing */
    .main-content-mobile {
        margin-top: 60px !important;
        margin-right: 0 !important;
        padding: 1rem !important;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    /* Larger touch targets for all interactive elements */
    button,
    a,
    input,
    select,
    textarea,
    .clickable {
        min-height: 48px !important;
        min-width: 48px !important;
        padding: 12px 16px !important;
    }

    /* Remove hover effects on touch devices */
    .hover-lift:hover,
    button:hover,
    a:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Better focus states for touch */
    button:focus,
    a:focus,
    input:focus,
    select:focus,
    textarea:focus {
        outline: 2px solid #3b82f6 !important;
        outline-offset: 2px !important;
    }
}

/* Mobile table improvements */
@media (max-width: 768px) {
    .table-mobile {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
        -webkit-overflow-scrolling: touch !important;
    }

    .table-mobile table {
        min-width: 600px !important;
        font-size: 14px !important;
    }

    .table-mobile th,
    .table-mobile td {
        padding: 8px 12px !important;
        white-space: nowrap !important;
    }
}

/* Mobile form improvements */
@media (max-width: 768px) {
    .form-mobile input,
    .form-mobile select,
    .form-mobile textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: 14px 16px !important;
        border-radius: 8px !important;
        border: 2px solid #e5e7eb !important;
        width: 100% !important;
    }

    .form-mobile input:focus,
    .form-mobile select:focus,
    .form-mobile textarea:focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    .form-mobile label {
        font-size: 14px !important;
        font-weight: 600 !important;
        margin-bottom: 6px !important;
        display: block !important;
    }
}

/* Mobile modal improvements */
@media (max-width: 768px) {
    .modal-mobile {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        margin: 0 !important;
        border-radius: 0 !important;
        max-height: 100vh !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    .modal-content-mobile {
        padding: 1rem !important;
        max-height: calc(100vh - 2rem) !important;
        overflow-y: auto !important;
    }
}

/* Mobile button groups */
@media (max-width: 768px) {
    .button-group-mobile {
        display: flex !important;
        flex-direction: column !important;
        gap: 12px !important;
        width: 100% !important;
    }

    .button-group-mobile button {
        width: 100% !important;
        justify-content: center !important;
        padding: 14px 20px !important;
        font-size: 16px !important;
    }
}

/* Mobile card layouts */
@media (max-width: 768px) {
    .card-mobile {
        margin: 8px 0 !important;
        padding: 16px !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .card-grid-mobile {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 12px !important;
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .header-mobile {
        height: 50px !important;
        padding: 0.5rem 1rem !important;
    }

    .main-content-mobile {
        margin-top: 50px !important;
    }

    .sidebar-mobile {
        width: 240px !important;
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .container {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .mobile-content {
        padding: 6px !important;
    }

    h1 {
        font-size: 1.25rem !important;
    }

    h2 {
        font-size: 1.125rem !important;
    }

    .button-group-mobile button {
        padding: 12px 16px !important;
        font-size: 14px !important;
    }
}

/* PWA and Mobile App Enhancements */

/* Prevent text selection on touch devices */
@media (hover: none) and (pointer: coarse) {
    .no-select {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Prevent callout on iOS */
    .no-callout {
        -webkit-touch-callout: none !important;
    }

    /* Prevent tap highlight */
    .no-tap-highlight {
        -webkit-tap-highlight-color: transparent !important;
    }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
    .safe-area-inset-top {
        padding-top: max(1rem, env(safe-area-inset-top)) !important;
    }

    .safe-area-inset-bottom {
        padding-bottom: max(1rem, env(safe-area-inset-bottom)) !important;
    }

    .safe-area-inset-left {
        padding-left: max(1rem, env(safe-area-inset-left)) !important;
    }

    .safe-area-inset-right {
        padding-right: max(1rem, env(safe-area-inset-right)) !important;
    }
}

/* iOS specific fixes */
@supports (-webkit-appearance: none) {
    input[type="search"] {
        -webkit-appearance: none !important;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="number"],
    textarea,
    select {
        -webkit-appearance: none !important;
        border-radius: 8px !important;
    }
}

/* Android specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    select {
        background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>") !important;
        background-repeat: no-repeat !important;
        background-position: left 12px center !important;
        background-size: 12px !important;
        padding-left: 40px !important;
    }
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
    html {
        scroll-behavior: smooth !important;
        -webkit-overflow-scrolling: touch !important;
    }

    body {
        -webkit-overflow-scrolling: touch !important;
    }

    /* Better scrolling for containers */
    .scroll-container {
        -webkit-overflow-scrolling: touch !important;
        overflow-scrolling: touch !important;
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
    /* Larger focus indicators */
    button:focus,
    a:focus,
    input:focus,
    select:focus,
    textarea:focus {
        outline: 3px solid #3b82f6 !important;
        outline-offset: 2px !important;
    }

    /* Better contrast for small screens */
    .text-muted {
        color: #4b5563 !important;
    }

    /* Ensure minimum contrast ratios */
    .text-gray-500 {
        color: #6b7280 !important;
    }

    .text-gray-400 {
        color: #9ca3af !important;
    }
}

/* Performance optimizations for mobile */
.gpu-accelerated {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    will-change: transform !important;
}

.optimize-legibility {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* Mobile-specific animations */
@media (max-width: 768px) {
    /* Reduce motion for better performance */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
        }
    }

    /* Optimize animations for mobile */
    .animate-spin {
        animation: spin 1s linear infinite !important;
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
    }
}