'use client'

import { useState } from 'react'
import { Menu, X } from 'lucide-react'

export default function SimpleTestPage() {
  const [isOpen, setIsOpen] = useState(false)

  const toggle = () => {
    console.log('🔄 Button clicked! Current state:', isOpen, '→ New state:', !isOpen)
    setIsOpen(!isOpen)
  }

  console.log('🔍 Component render - isOpen:', isOpen)

  return (
    <div className="min-h-screen bg-gray-100 relative">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-40">
        <div className="flex items-center justify-between h-full px-6">
          <div className="flex items-center gap-4">
            {/* Menu Button */}
            <button
              onClick={toggle}
              className="p-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors border-2 border-red-500 bg-yellow-200"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
            <span className="text-lg font-bold">اختبار بسيط</span>
          </div>
          <div className="text-sm bg-blue-100 px-3 py-1 rounded">
            الحالة: {isOpen ? '🟢 مفتوح' : '🔴 مغلق'}
          </div>
        </div>
      </header>

      {/* Sidebar */}
      <div
        className={`
          bg-white shadow-2xl h-screen w-64 fixed right-0 top-0 z-50 border-l-4 border-blue-500
          transform transition-transform duration-500 ease-in-out
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
        style={{
          backgroundColor: isOpen ? '#f0f9ff' : '#ffffff',
          borderLeftColor: isOpen ? '#3b82f6' : '#e5e7eb'
        }}
      >
        <div className="p-6 h-full">
          <div className="flex items-center justify-between mb-6 p-4 bg-blue-100 rounded-lg">
            <h2 className="text-xl font-bold text-blue-900">الشريط الجانبي</h2>
            <button
              onClick={toggle}
              className="p-2 hover:bg-blue-200 rounded-lg text-blue-700"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-100 rounded-lg border border-green-300">
              <p className="text-green-800 font-medium">✅ الشريط الجانبي يعمل!</p>
              <p className="text-green-600 text-sm mt-1">الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>
            </div>
            
            <div className="p-4 bg-blue-100 rounded-lg border border-blue-300">
              <p className="text-blue-800 font-medium">📱 اختبار التحكم</p>
              <button
                onClick={toggle}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                إغلاق الشريط
              </button>
            </div>

            <div className="p-4 bg-yellow-100 rounded-lg border border-yellow-300">
              <p className="text-yellow-800 font-medium">🔍 معلومات التشخيص</p>
              <div className="text-yellow-700 text-sm mt-2 space-y-1">
                <p>• الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>
                <p>• CSS Class: {isOpen ? 'translate-x-0' : 'translate-x-full'}</p>
                <p>• Z-Index: 50</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggle}
        />
      )}

      {/* Main Content */}
      <main className="pt-20 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h1 className="text-2xl font-bold mb-4">اختبار الشريط الجانبي البسيط</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h2 className="text-lg font-semibold">معلومات الحالة:</h2>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${isOpen ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span>الشريط الجانبي: {isOpen ? 'مفتوح' : 'مغلق'}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    CSS Transform: {isOpen ? 'translate-x-0' : 'translate-x-full'}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-lg font-semibold">أدوات التحكم:</h2>
                <div className="space-y-2">
                  <button
                    onClick={toggle}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    {isOpen ? 'إغلاق الشريط الجانبي' : 'فتح الشريط الجانبي'}
                  </button>
                  
                  <button
                    onClick={() => {
                      console.log('🔍 Current state check:', isOpen)
                      alert(`الحالة الحالية: ${isOpen ? 'مفتوح' : 'مغلق'}`)
                    }}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                  >
                    فحص الحالة
                  </button>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-800 mb-2">تعليمات الاختبار:</h3>
              <ul className="text-yellow-700 space-y-1 text-sm">
                <li>• الزر الأصفر في الهيدر يجب أن يكون مرئياً</li>
                <li>• اضغط على الزر لفتح/إغلاق الشريط الجانبي</li>
                <li>• يجب أن ينزلق الشريط من اليمين</li>
                <li>• افتح وحدة التحكم (F12) لرؤية الرسائل</li>
                <li>• يمكنك أيضاً الضغط على الخلفية المظلمة للإغلاق</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">رسائل وحدة التحكم:</h3>
              <div className="text-blue-700 text-sm space-y-1">
                <p>• 🔄 Button clicked! - عند الضغط على الزر</p>
                <p>• 🔍 Component render - عند إعادة رسم المكون</p>
                <p>• 🔍 Current state check - عند فحص الحالة</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
