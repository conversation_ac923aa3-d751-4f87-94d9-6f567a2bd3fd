{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { usePermissions } from '@/contexts/AuthContext'\nimport {\n  Home,\n  ShoppingCart,\n  Package,\n  Users,\n  UserCheck,\n  RotateCcw,\n  BarChart3,\n  Settings,\n  Pill,\n  FileText,\n  Wallet,\n  Shield,\n  Activity,\n  Bell,\n  Wrench,\n  Printer,\n  Bug\n} from 'lucide-react'\n\ninterface MenuItem {\n  title: string\n  href: string\n  icon: any\n  permission?: string\n  requireAny?: string[]\n}\n\nconst getMenuItems = (permissions: any): MenuItem[] => [\n  {\n    title: 'الرئيسية',\n    href: '/',\n    icon: Home\n  },\n  {\n    title: 'إدارة المخزون',\n    href: '/inventory',\n    icon: Package,\n    permission: 'inventory_view'\n  },\n  {\n    title: 'المبيعات',\n    href: '/sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  },\n\n  {\n    title: 'المشتريات',\n    href: '/purchases',\n    icon: Pill,\n    permission: 'purchases_view'\n  },\n\n  {\n    title: 'العملاء',\n    href: '/customers',\n    icon: Users,\n    permission: 'customers_view'\n  },\n  {\n    title: 'الموردين',\n    href: '/suppliers',\n    icon: UserCheck,\n    permission: 'suppliers_view'\n  },\n  {\n    title: 'المرتجعات',\n    href: '/returns',\n    icon: RotateCcw,\n    permission: 'returns_view'\n  },\n\n  {\n    title: 'الصندوق',\n    href: '/cashbox',\n    icon: Wallet,\n    permission: 'cashbox_view'\n  },\n  {\n    title: 'التقارير',\n    href: '/reports',\n    icon: BarChart3,\n    permission: 'reports_view'\n  },\n  {\n    title: 'إدارة المستخدمين',\n    href: '/users',\n    icon: Shield,\n    permission: 'users_view'\n  },\n  {\n    title: 'سجل النشاطات',\n    href: '/activity-log',\n    icon: Activity,\n    permission: 'users_view'\n  },\n  {\n    title: 'التنبيهات',\n    href: '/notifications',\n    icon: Bell\n  },\n  {\n    title: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    permission: 'settings_view'\n  },\n  {\n    title: 'إصلاح البيانات',\n    href: '/fix-data',\n    icon: Wrench,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار الطباعة',\n    href: '/test-print',\n    icon: Printer,\n    permission: 'sales_view'\n  },\n  {\n    title: 'تشخيص البيانات',\n    href: '/debug-data',\n    icon: Bug,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار المبيعات',\n    href: '/debug-sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  }\n]\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nexport default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {\n  const pathname = usePathname()\n  const { hasPermission, permissions } = usePermissions()\n\n\n\n  const menuItems = getMenuItems(permissions)\n\n  // إغلاق القائمة عند تغيير الصفحة\n  useEffect(() => {\n    if (onClose) {\n      onClose()\n    }\n  }, [pathname, onClose])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.getElementById('mobile-sidebar')\n      const menuButton = document.getElementById('mobile-menu-button')\n\n      if (sidebar && !sidebar.contains(event.target as Node) &&\n          menuButton && !menuButton.contains(event.target as Node)) {\n        if (onClose) {\n          onClose()\n        }\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  // تصفية العناصر بناءً على الصلاحيات\n  const visibleMenuItems = menuItems.filter(item => {\n    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)\n    return hasPermission(item.permission as any)\n  })\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Desktop Sidebar - Always visible on desktop */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:right-0 md:z-30\">\n        <div className=\"flex flex-col flex-grow bg-gradient-to-b from-white to-gray-50 shadow-xl border-l border-gray-200\">\n          <div className=\"flex items-center gap-3 p-6 bg-gradient-to-r from-blue-600 to-indigo-700 text-white\">\n            <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n              <Pill className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-bold\">نظام الصيدلية</h1>\n              <p className=\"text-sm text-blue-100\">مكتب لارين العلمي</p>\n            </div>\n          </div>\n\n          <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n            {visibleMenuItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'\n                      : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800'\n                  }`}\n                >\n                  <Icon className={`h-5 w-5 ${isActive ? 'text-white' : ''}`} />\n                  <span className=\"font-medium\">{item.title}</span>\n                  {isActive && (\n                    <div className=\"mr-auto w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          <div className=\"p-4\">\n            <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200\">\n              <div className=\"flex items-center justify-center gap-2 mb-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <p className=\"text-xs text-gray-600 font-medium\">متصل</p>\n              </div>\n              <p className=\"text-xs text-gray-500\">© 2024 مكتب لارين العلمي</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Sidebar - Only visible on mobile when open */}\n      <div\n        id=\"mobile-sidebar\"\n        className=\"md:hidden\"\n        style={{\n          position: 'fixed',\n          top: 0,\n          right: isOpen ? '0px' : '-300px',\n          height: '100vh',\n          width: '280px',\n          backgroundColor: 'white',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          borderLeft: '1px solid #e5e7eb',\n          transition: 'right 0.3s ease-in-out',\n          zIndex: 9999,\n          overflow: 'auto'\n        }}\n      >\n        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>\n          {/* Header */}\n          <div style={{\n            padding: '16px',\n            background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n            borderRadius: '12px',\n            color: 'white',\n            marginBottom: '24px',\n            textAlign: 'center'\n          }}>\n            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>نظام الصيدلية</h1>\n            <p style={{ fontSize: '14px', margin: '8px 0 0 0', opacity: 0.9 }}>مكتب لارين العلمي</p>\n          </div>\n\n          {/* Navigation Menu */}\n          <div style={{ marginBottom: '24px' }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              color: '#374151',\n              borderBottom: '2px solid #e5e7eb',\n              paddingBottom: '8px'\n            }}>القائمة الرئيسية</h3>\n\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              {visibleMenuItems.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <a\n                    key={item.href}\n                    href={item.href}\n                    onClick={onClose}\n                    style={{\n                      padding: '12px 16px',\n                      backgroundColor: isActive ? '#3b82f6' : '#f8fafc',\n                      color: isActive ? 'white' : '#374151',\n                      textDecoration: 'none',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '12px',\n                      border: isActive ? 'none' : '1px solid #e5e7eb',\n                      transition: 'all 0.2s ease',\n                      fontSize: '14px',\n                      fontWeight: isActive ? 'bold' : 'normal'\n                    }}\n                  >\n                    <Icon style={{ width: '20px', height: '20px' }} />\n                    {item.title}\n                  </a>\n                )\n              })}\n            </div>\n          </div>\n\n          {/* Close Button */}\n          <div style={{ marginTop: 'auto', paddingTop: '16px' }}>\n            <button\n              onClick={onClose}\n              style={{\n                width: '100%',\n                padding: '12px',\n                backgroundColor: '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                fontSize: '16px',\n                fontWeight: 'bold',\n                cursor: 'pointer',\n                transition: 'background-color 0.2s ease'\n              }}\n              onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}\n              onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}\n            >\n              إغلاق القائمة\n            </button>\n          </div>\n\n        </div>\n      </div>\n\n      {/* Overlay */}\n      {isOpen && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 9998\n          }}\n          onClick={onClose}\n        />\n      )}\n    </>\n  )\n}\n\n// تصدير دالة للتحكم في القائمة من مكونات أخرى\nexport const useSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAkCA,MAAM,eAAe,CAAC,cAAiC;QACrD;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;KACD;AAOc,SAAS;QAAQ,EAAE,SAAS,KAAK,EAAE,OAAO,EAAgB,GAAzC,iEAA4C,CAAC;;IAC3E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAIpD,MAAM,YAAY,aAAa;IAE/B,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;4BAAG;QAAC;QAAU;KAAQ;IAEtB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,aAAa,SAAS,cAAc,CAAC;oBAE3C,IAAI,WAAW,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KACzC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5D,IAAI,SAAS;4BACX;wBACF;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;gBACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;4BAAG;QAAC;QAAQ;KAAQ;IAEpB,oCAAoC;IACpC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,KAAK,6CAA6C;;QAC/E,OAAO,cAAc,KAAK,UAAU;IACtC;IAEA,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC;gCACrB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,kFAIX,OAHC,WACI,sEACA;;sDAGN,6LAAC;4CAAK,WAAW,AAAC,WAAuC,OAA7B,WAAW,eAAe;;;;;;sDACtD,6LAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;wCACxC,0BACC,6LAAC;4CAAI,WAAU;;;;;;;mCAXZ,KAAK,IAAI;;;;;4BAepB;;;;;;sCAGF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;kDAEnD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBACC,IAAG;gBACH,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,OAAO,SAAS,QAAQ;oBACxB,QAAQ;oBACR,OAAO;oBACP,iBAAiB;oBACjB,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,UAAU;gBACZ;0BAEA,cAAA,6LAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,QAAQ;wBAAQ,WAAW;oBAAO;;sCAE/D,6LAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,OAAO;gCACP,cAAc;gCACd,WAAW;4BACb;;8CACE,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAQ,YAAY;wCAAQ,QAAQ;oCAAE;8CAAG;;;;;;8CAChE,6LAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAQ,QAAQ;wCAAa,SAAS;oCAAI;8CAAG;;;;;;;;;;;;sCAIrE,6LAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CACjC,6LAAC;oCAAG,OAAO;wCACT,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,cAAc;wCACd,eAAe;oCACjB;8CAAG;;;;;;8CAEH,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;8CAChE,iBAAiB,GAAG,CAAC,CAAC;wCACrB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wCAEvC,qBACE,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS;4CACT,OAAO;gDACL,SAAS;gDACT,iBAAiB,WAAW,YAAY;gDACxC,OAAO,WAAW,UAAU;gDAC5B,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,QAAQ,WAAW,SAAS;gDAC5B,YAAY;gDACZ,UAAU;gDACV,YAAY,WAAW,SAAS;4CAClC;;8DAEA,6LAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAQ,QAAQ;oDAAO;;;;;;gDAC5C,KAAK,KAAK;;2CAnBN,KAAK,IAAI;;;;;oCAsBpB;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAQ,YAAY;4BAAO;sCAClD,cAAA,6LAAC;gCACC,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,UAAU;oCACV,YAAY;oCACZ,QAAQ;oCACR,YAAY;gCACd;gCACA,aAAa,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;gCACrD,YAAY,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;0CACrD;;;;;;;;;;;;;;;;;;;;;;YASN,wBACC,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;;;AAKnB;GAnOwB;;QACL,qIAAA,CAAA,cAAW;QACW,kIAAA,CAAA,iBAAc;;;KAF/B;AAsOjB,MAAM,aAAa;;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO;QAAE;QAAQ;QAAW,eAAe,IAAM,UAAU,CAAC;IAAQ;AACtE;IAHa", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  X,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign\n} from 'lucide-react'\n\nexport default function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll\n  } = useNotifications()\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const filteredNotifications = activeTab === 'unread' \n    ? notifications.filter(n => !n.isRead)\n    : notifications\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-4 w-4 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-4 w-4 text-blue-500\" />\n    \n    // أيقونات حسب الفئة\n    if (category === 'inventory') return <Package className=\"h-4 w-4 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-4 w-4 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-4 w-4 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-4 w-4 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-4 w-4 text-gray-500\" />\n    \n    return <Bell className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'border-r-4 border-red-500 bg-red-50'\n      case 'high': return 'border-r-4 border-orange-500 bg-orange-50'\n      case 'medium': return 'border-r-4 border-yellow-500 bg-yellow-50'\n      case 'low': return 'border-r-4 border-blue-500 bg-blue-50'\n      default: return 'border-r-4 border-gray-500 bg-gray-50'\n    }\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n      setIsOpen(false)\n    }\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Bell className=\"h-5 w-5\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">التنبيهات</h3>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n            \n            {/* Tabs */}\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('all')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'all'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                الكل ({notifications.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('unread')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'unread'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                غير مقروءة ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Actions */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800\"\n                    >\n                      <CheckCheck className=\"h-3 w-3\" />\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n                <button\n                  onClick={clearAll}\n                  className=\"flex items-center gap-1 text-xs text-red-600 hover:text-red-800\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  مسح الكل\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500\">\n                  {activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                      !notification.isRead ? getPriorityColor(notification.priority) : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                              {notification.message}\n                            </p>\n                            \n                            {/* Action Button */}\n                            {notification.actionUrl && (\n                              <button className=\"inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2\">\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض التفاصيل'}\n                              </button>\n                            )}\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-1 mr-2\">\n                            {!notification.isRead && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation()\n                                  markAsRead(notification.id)\n                                }}\n                                className=\"p-1 text-gray-400 hover:text-blue-600\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                removeNotification(notification.id)\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-600\"\n                              title=\"حذف\"\n                            >\n                              <X className=\"h-3 w-3\" />\n                            </button>\n                          </div>\n                        </div>\n                        \n                        {/* Time */}\n                        <div className=\"flex items-center gap-1 mt-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(notification.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  router.push('/notifications')\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع التنبيهات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAwBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;qEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG,EAAE;IAEL,MAAM,wBAAwB,cAAc,WACxC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IACnC;IAEJ,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,oBAAoB;QACpB,IAAI,aAAa,aAAa,qBAAO,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,AAAC,OAAoB,OAAd,eAAc;QAEpD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,AAAC,OAAkB,OAAZ,aAAY;QAEhD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,AAAC,OAAiB,OAAX,YAAW;IAC3B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;YAClC,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,QACV,8BACA;;4CAEP;4CACQ,cAAc,MAAM;4CAAC;;;;;;;kDAE9B,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,WACV,8BACA;;4CAEP;4CACc;4CAAY;;;;;;;;;;;;;;;;;;;oBAM9B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,cAAc,mBACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAQtC,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CACV,cAAc,WAAW,+BAA+B;;;;;;;;;;;iDAI7D,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC;oCAEC,WAAW,AAAC,yDAEX,OADC,CAAC,aAAa,MAAM,GAAG,iBAAiB,aAAa,QAAQ,IAAI;oCAEnE,SAAS,IAAM,wBAAwB;8CAEvC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAW,AAAC,uBAEf,OADC,CAAC,aAAa,MAAM,GAAG,kBAAkB;kFAExC,aAAa,KAAK;;;;;;kFAErB,6LAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;oEAItB,aAAa,SAAS,kBACrB,6LAAC;wEAAO,WAAU;;0FAChB,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,aAAa,WAAW,IAAI;;;;;;;;;;;;;0EAMnC,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,WAAW,aAAa,EAAE;wEAC5B;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAGrB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;;;;;;;mCAhEtC,aAAa,EAAE;;;;;;;;;;;;;;;oBA2E7B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;gCACP,OAAO,IAAI,CAAC;gCACZ,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GA7QwB;;QAIP,qIAAA,CAAA,YAAS;QASpB,0IAAA,CAAA,mBAAgB;;;KAbE", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileMenuButton.tsx"], "sourcesContent": ["'use client'\n\nimport { Menu, X } from 'lucide-react'\n\ninterface MobileMenuButtonProps {\n  isOpen: boolean\n  onClick: () => void\n}\n\nexport default function MobileMenuButton({ isOpen, onClick }: MobileMenuButtonProps) {\n  const handleClick = () => {\n    onClick()\n  }\n\n  return (\n    <button\n      id=\"mobile-menu-button\"\n      onClick={handleClick}\n      className=\"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center\"\n      aria-label={isOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n    >\n      {isOpen ? (\n        <X className=\"h-6 w-6\" />\n      ) : (\n        <Menu className=\"h-6 w-6\" />\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AASe,SAAS,iBAAiB,KAA0C;QAA1C,EAAE,MAAM,EAAE,OAAO,EAAyB,GAA1C;IACvC,MAAM,cAAc;QAClB;IACF;IAEA,qBACE,6LAAC;QACC,IAAG;QACH,SAAS;QACT,WAAU;QACV,cAAY,SAAS,kBAAkB;kBAEtC,uBACC,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;iCAEb,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;KAnBwB", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport NotificationDropdown from './NotificationDropdown'\nimport MobileMenuButton from './MobileMenuButton'\nimport {\n  Search,\n  User,\n  LogOut,\n  Settings,\n  Shield,\n  ChevronDown,\n  Activity\n} from 'lucide-react'\n\ninterface HeaderProps {\n  onMobileMenuToggle?: () => void\n  isMobileMenuOpen?: boolean\n}\n\nexport default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, logout } = useAuth()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    const roleNames: Record<string, string> = {\n      admin: 'مدير النظام',\n      manager: 'مدير',\n      pharmacist: 'صيدلي',\n      cashier: 'كاشير',\n      viewer: 'مشاهد'\n    }\n    return roleNames[role] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors: Record<string, string> = {\n      admin: 'bg-red-600',\n      manager: 'bg-purple-600',\n      pharmacist: 'bg-blue-600',\n      cashier: 'bg-green-600',\n      viewer: 'bg-gray-600'\n    }\n    return colors[role] || 'bg-gray-600'\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 md:right-64 z-40 header-mobile\">\n      <div className=\"flex items-center justify-between h-full px-3 md:px-6\">\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Mobile Menu Button - Only visible on mobile */}\n          <div className=\"md:hidden\">\n            <MobileMenuButton\n              isOpen={isMobileMenuOpen}\n              onClick={onMobileMenuToggle || (() => console.log('⚠️ No onMobileMenuToggle function provided!'))}\n            />\n          </div>\n\n          {/* Mobile Search - Hidden on very small screens */}\n          <div className=\"relative hidden sm:hidden md:block\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث...\"\n              className=\"pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile\"\n            />\n          </div>\n\n          {/* Mobile Search Icon - Visible only on small screens */}\n          <button className=\"md:hidden p-2 hover:bg-gray-100 rounded-lg\">\n            <Search className=\"h-5 w-5 text-gray-600\" />\n          </button>\n        </div>\n\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Notifications */}\n          <NotificationDropdown />\n\n          {/* User Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]\"\n            >\n              {/* Hide user info text on mobile */}\n              <div className=\"text-right hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-800\">{user?.full_name || 'مستخدم'}</p>\n                <p className=\"text-xs text-gray-500\">{user ? getRoleDisplayName(user.role) : ''}</p>\n              </div>\n              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <ChevronDown className=\"h-4 w-4 text-gray-400 hidden md:block\" />\n            </button>\n\n            {/* Dropdown Menu */}\n            {showUserMenu && (\n              <div className=\"absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile\">\n                {/* User Info */}\n                <div className=\"px-4 py-3 border-b border-gray-100\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                      <p className=\"text-xs text-gray-500\">@{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :\n                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :\n                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :\n                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      <Shield className=\"h-3 w-3 ml-1\" />\n                      {user ? getRoleDisplayName(user.role) : ''}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/profile')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    الملف الشخصي\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/activity-log')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Activity className=\"h-4 w-4\" />\n                    سجل النشاطات\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/settings')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    الإعدادات\n                  </button>\n                </div>\n\n                {/* Logout */}\n                <div className=\"border-t border-gray-100 pt-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      handleLogout()\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAsBe,SAAS;QAAO,EAAE,kBAAkB,EAAE,mBAAmB,KAAK,EAAe,GAA7D,iEAAgE,CAAC;;IAC9F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAoC;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;oCACf,QAAQ;oCACR,SAAS,sBAAsB,CAAC,IAAM,QAAQ,GAAG,CAAC,8CAA8C;;;;;;;;;;;0CAKpG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6IAAA,CAAA,UAAoB;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAyB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;0DAE/E,6LAAC;gDAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0DAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0EAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAqC,iBAAA,2BAAA,KAAM,SAAS;;;;;;kFACjE,6LAAC;wEAAE,WAAU;;4EAAwB;4EAAE,iBAAA,2BAAA,KAAM,QAAQ;;;;;;;kFACrD,6LAAC;wEAAE,WAAU;kFAAyB,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,AAAC,uEAMjB,OALC,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU,4BACzB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,kCAC3B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,eAAe,8BAC9B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,gCAC3B;;8EAEA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAI9B,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIlC,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB;oDACF;oDACA,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC;GA9KwB;;QAEG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <AlertTriangle className=\"h-12 w-12 text-red-500\" />\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 mb-2\">حدث خطأ غير متوقع</h1>\n            \n            <p className=\"text-gray-600 mb-4\">\n              عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.\n            </p>\n            \n            {this.state.error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left\">\n                <p className=\"text-sm text-red-800 font-mono\">\n                  {this.state.error.message}\n                </p>\n              </div>\n            )}\n            \n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={this.retry}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                إعادة المحاولة\n              </button>\n              \n              <button\n                onClick={() => window.location.reload()}\n                className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\n              >\n                إعادة تحميل الصفحة\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  تفاصيل الخطأ (للمطورين)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                  {this.state.error?.stack}\n                  {'\\n\\n'}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for functional components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null)\n\n  const resetError = React.useCallback(() => {\n    setError(null)\n  }, [])\n\n  const handleError = React.useCallback((error: Error) => {\n    console.error('Error caught by useErrorHandler:', error)\n    setError(error)\n  }, [])\n\n  React.useEffect(() => {\n    if (error) {\n      throw error\n    }\n  }, [error])\n\n  return { handleError, resetError }\n}\n\n// Safe component wrapper\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;;AAsFa;;;AApFb;AACA;AAAA;;;;AAHA;;;AAgBA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAMzC,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAMA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAkDV,mBAEA;YAnDb,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAIjC,IAAI,CAAC,KAAK,CAAC,KAAK,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAI,CAAC,KAAK;oCACnB,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC7D,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;;yCACZ,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,cAAhB,wCAAA,kBAAkB,KAAK;wCACvB;yCACA,wBAAA,IAAI,CAAC,KAAK,CAAC,SAAS,cAApB,4CAAA,sBAAsB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;QAOnD;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAvFA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QAmBR,+KAAA,SAAQ;YACN,IAAI,CAAC,QAAQ,CAAC;gBAAE,UAAU;gBAAO,OAAO;gBAAW,WAAW;YAAU;QAC1E;QApBE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AAqFF;AAGO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,WAAW;mDAAC;YACnC,SAAS;QACX;kDAAG,EAAE;IAEL,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,WAAW;oDAAC,CAAC;YACrC,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;mDAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,OAAO;gBACT,MAAM;YACR;QACF;oCAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC;GAnBgB;AAsBT,SAAS,kBACd,SAAiC,EACjC,QAAmE;IAEnE,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;YAAc,UAAU;sBACvB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,AAAC,qBAA4D,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAE5F,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ToastNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  X,\n  CheckCircle,\n  AlertTriangle,\n  Info,\n  XCircle,\n  ExternalLink\n} from 'lucide-react'\n\ninterface ToastNotification {\n  id: string\n  type: 'success' | 'warning' | 'error' | 'info'\n  title: string\n  message: string\n  actionUrl?: string\n  actionLabel?: string\n  duration?: number\n}\n\nexport default function ToastNotifications() {\n  const [toasts, setToasts] = useState<ToastNotification[]>([])\n  const { notifications } = useNotifications()\n\n  // مراقبة التنبيهات الجديدة وعرضها كـ Toast\n  useEffect(() => {\n    const latestNotification = notifications[0]\n    if (latestNotification && !latestNotification.isRead) {\n      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)\n      const notificationTime = new Date(latestNotification.createdAt).getTime()\n      const now = new Date().getTime()\n      const diffInMinutes = (now - notificationTime) / (1000 * 60)\n      \n      if (diffInMinutes < 1) {\n        showToast({\n          id: latestNotification.id,\n          type: latestNotification.type,\n          title: latestNotification.title,\n          message: latestNotification.message,\n          actionUrl: latestNotification.actionUrl,\n          actionLabel: latestNotification.actionLabel,\n          duration: getDurationByPriority(latestNotification.priority)\n        })\n      }\n    }\n  }, [notifications])\n\n  const getDurationByPriority = (priority: string): number => {\n    switch (priority) {\n      case 'critical': return 10000 // 10 ثواني\n      case 'high': return 7000     // 7 ثواني\n      case 'medium': return 5000   // 5 ثواني\n      case 'low': return 3000      // 3 ثواني\n      default: return 5000\n    }\n  }\n\n  const showToast = (toast: ToastNotification) => {\n    // تجنب التكرار\n    if (toasts.find(t => t.id === toast.id)) return\n\n    setToasts(prev => [...prev, toast])\n\n    // إزالة التنبيه تلقائياً بعد المدة المحددة\n    setTimeout(() => {\n      removeToast(toast.id)\n    }, toast.duration || 5000)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const getToastIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getToastStyles = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-20 left-4 md:left-72 z-[60] space-y-3 max-w-sm\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className={`w-full shadow-lg rounded-lg border p-3 md:p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}\n        >\n          <div className=\"flex items-start gap-2 md:gap-3\">\n            {/* Icon */}\n            <div className=\"flex-shrink-0 mt-0.5\">\n              {getToastIcon(toast.type)}\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 min-w-0\">\n              <h4 className=\"text-xs md:text-sm font-medium mb-1\">\n                {toast.title}\n              </h4>\n              <p className=\"text-xs md:text-sm opacity-90 line-clamp-2\">\n                {toast.message}\n              </p>\n\n              {/* Action Button */}\n              {toast.actionUrl && (\n                <a\n                  href={toast.actionUrl}\n                  className=\"inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline min-h-[32px]\"\n                >\n                  <ExternalLink className=\"h-3 w-3\" />\n                  {toast.actionLabel || 'عرض التفاصيل'}\n                </a>\n              )}\n            </div>\n\n            {/* Close Button */}\n            <button\n              onClick={() => removeToast(toast.id)}\n              className=\"flex-shrink-0 p-2 hover:bg-black hover:bg-opacity-10 rounded min-h-[44px] min-w-[44px] flex items-center justify-center\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// إضافة الأنيميشن إلى CSS\nconst toastStyles = `\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`\n\n// إضافة الأنيميشن إلى الصفحة\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style')\n  styleElement.textContent = toastStyles\n  document.head.appendChild(styleElement)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAuBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,qBAAqB,aAAa,CAAC,EAAE;YAC3C,IAAI,sBAAsB,CAAC,mBAAmB,MAAM,EAAE;gBACpD,uDAAuD;gBACvD,MAAM,mBAAmB,IAAI,KAAK,mBAAmB,SAAS,EAAE,OAAO;gBACvE,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,MAAM,gBAAgB,CAAC,MAAM,gBAAgB,IAAI,CAAC,OAAO,EAAE;gBAE3D,IAAI,gBAAgB,GAAG;oBACrB,UAAU;wBACR,IAAI,mBAAmB,EAAE;wBACzB,MAAM,mBAAmB,IAAI;wBAC7B,OAAO,mBAAmB,KAAK;wBAC/B,SAAS,mBAAmB,OAAO;wBACnC,WAAW,mBAAmB,SAAS;wBACvC,aAAa,mBAAmB,WAAW;wBAC3C,UAAU,sBAAsB,mBAAmB,QAAQ;oBAC7D;gBACF;YACF;QACF;uCAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAY,OAAO,MAAM,WAAW;;YACzC,KAAK;gBAAQ,OAAO,KAAS,UAAU;;YACvC,KAAK;gBAAU,OAAO,KAAO,UAAU;;YACvC,KAAK;gBAAO,OAAO,KAAU,UAAU;;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,eAAe;QACf,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;QAEzC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,2CAA2C;QAC3C,WAAW;YACT,YAAY,MAAM,EAAE;QACtB,GAAG,MAAM,QAAQ,IAAI;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,WAAW,AAAC,6GAAuI,OAA3B,eAAe,MAAM,IAAI;0BAEjJ,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,IAAI;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,6LAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;gCAIf,MAAM,SAAS,kBACd,6LAAC;oCACC,MAAM,MAAM,SAAS;oCACrB,WAAU;;sDAEV,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,MAAM,WAAW,IAAI;;;;;;;;;;;;;sCAM5B,6LAAC;4BACC,SAAS,IAAM,YAAY,MAAM,EAAE;4BACnC,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;eAnCZ,MAAM,EAAE;;;;;;;;;;AA0CvB;GAnIwB;;QAEI,0IAAA,CAAA,mBAAgB;;;KAFpB;AAqIxB,0BAA0B;AAC1B,MAAM,cAAe;AAwBrB,6BAA6B;AAC7B,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileOptimizer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\nexport default function MobileOptimizer() {\n  useEffect(() => {\n    // Detect mobile device\n    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    if (isMobile || isTouch) {\n      // Add mobile-specific classes to body\n      document.body.classList.add('mobile-device', 'touch-device')\n      \n      // Prevent zoom on input focus (iOS)\n      const viewport = document.querySelector('meta[name=viewport]')\n      if (viewport) {\n        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover')\n      }\n\n      // Add touch-friendly classes to interactive elements\n      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')\n      interactiveElements.forEach(element => {\n        element.classList.add('touch-friendly')\n      })\n\n      // Optimize scrolling\n      document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')\n      document.body.style.setProperty('-webkit-overflow-scrolling', 'touch')\n\n      // Prevent bounce scrolling on iOS\n      document.addEventListener('touchmove', (e) => {\n        if (e.target === document.body) {\n          e.preventDefault()\n        }\n      }, { passive: false })\n\n      // Add safe area support\n      if (CSS.supports('padding: max(0px)')) {\n        document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')\n        document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')\n        document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')\n        document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')\n      }\n\n      // Optimize performance for mobile\n      const optimizeElement = (element: Element) => {\n        if (element instanceof HTMLElement) {\n          element.style.setProperty('transform', 'translateZ(0)')\n          element.style.setProperty('will-change', 'transform')\n        }\n      }\n\n      // Apply optimizations to animated elements\n      const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift')\n      animatedElements.forEach(optimizeElement)\n\n      // Handle orientation change\n      const handleOrientationChange = () => {\n        // Force repaint after orientation change\n        setTimeout(() => {\n          window.scrollTo(0, window.scrollY)\n        }, 100)\n      }\n\n      window.addEventListener('orientationchange', handleOrientationChange)\n      \n      // Cleanup\n      return () => {\n        window.removeEventListener('orientationchange', handleOrientationChange)\n      }\n    }\n  }, [])\n\n  useEffect(() => {\n    // Add PWA install prompt handling\n    let deferredPrompt: any\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      deferredPrompt = e\n      \n      // Show custom install button or banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'block'\n      }\n    }\n\n    const handleAppInstalled = () => {\n      console.log('PWA was installed')\n      deferredPrompt = null\n      \n      // Hide install banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'none'\n      }\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  return null // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,uBAAuB;YACvB,MAAM,WAAW,iEAAiE,IAAI,CAAC,UAAU,SAAS;YAC1G,MAAM,UAAU,kBAAkB,UAAU,UAAU,cAAc,GAAG;YAEvE,IAAI,YAAY,SAAS;gBACvB,sCAAsC;gBACtC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB;gBAE7C,oCAAoC;gBACpC,MAAM,WAAW,SAAS,aAAa,CAAC;gBACxC,IAAI,UAAU;oBACZ,SAAS,YAAY,CAAC,WAAW;gBACnC;gBAEA,qDAAqD;gBACrD,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;gBACtD,oBAAoB,OAAO;iDAAC,CAAA;wBAC1B,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;;gBAEA,qBAAqB;gBACrB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;gBACzE,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;gBAE9D,kCAAkC;gBAClC,SAAS,gBAAgB,CAAC;iDAAa,CAAC;wBACtC,IAAI,EAAE,MAAM,KAAK,SAAS,IAAI,EAAE;4BAC9B,EAAE,cAAc;wBAClB;oBACF;gDAAG;oBAAE,SAAS;gBAAM;gBAEpB,wBAAwB;gBACxB,IAAI,IAAI,QAAQ,CAAC,sBAAsB;oBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB;oBACpE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,4BAA4B;oBACvE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B;oBACrE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,2BAA2B;gBACxE;gBAEA,kCAAkC;gBAClC,MAAM;iEAAkB,CAAC;wBACvB,IAAI,mBAAmB,aAAa;4BAClC,QAAQ,KAAK,CAAC,WAAW,CAAC,aAAa;4BACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,eAAe;wBAC3C;oBACF;;gBAEA,2CAA2C;gBAC3C,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;gBACnD,iBAAiB,OAAO,CAAC;gBAEzB,4BAA4B;gBAC5B,MAAM;yEAA0B;wBAC9B,yCAAyC;wBACzC;iFAAW;gCACT,OAAO,QAAQ,CAAC,GAAG,OAAO,OAAO;4BACnC;gFAAG;oBACL;;gBAEA,OAAO,gBAAgB,CAAC,qBAAqB;gBAE7C,UAAU;gBACV;iDAAO;wBACL,OAAO,mBAAmB,CAAC,qBAAqB;oBAClD;;YACF;QACF;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,kCAAkC;YAClC,IAAI;YAEJ,MAAM;uEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,iBAAiB;oBAEjB,uCAAuC;oBACvC,MAAM,gBAAgB,SAAS,cAAc,CAAC;oBAC9C,IAAI,eAAe;wBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;oBAChC;gBACF;;YAEA,MAAM;gEAAqB;oBACzB,QAAQ,GAAG,CAAC;oBACZ,iBAAiB;oBAEjB,sBAAsB;oBACtB,MAAM,gBAAgB,SAAS,cAAc,CAAC;oBAC9C,IAAI,eAAe;wBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;6CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;oCAAG,EAAE;IAEL,OAAO,KAAK,yCAAyC;;AACvD;GA1GwB;KAAA", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/PWAInstallBanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Smartphone } from 'lucide-react'\n\nexport default function PWAInstallBanner() {\n  const [showBanner, setShowBanner] = useState(false)\n  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)\n\n  useEffect(() => {\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e)\n      setShowBanner(true)\n    }\n\n    const handleAppInstalled = () => {\n      setShowBanner(false)\n      setDeferredPrompt(null)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    // Check if app is already installed\n    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {\n      setShowBanner(false)\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  const handleInstallClick = async () => {\n    if (deferredPrompt) {\n      deferredPrompt.prompt()\n      const { outcome } = await deferredPrompt.userChoice\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt')\n      } else {\n        console.log('User dismissed the install prompt')\n      }\n      \n      setDeferredPrompt(null)\n      setShowBanner(false)\n    }\n  }\n\n  const handleDismiss = () => {\n    setShowBanner(false)\n    // Remember user dismissed the banner\n    localStorage.setItem('pwa-install-dismissed', 'true')\n  }\n\n  // Don't show if user previously dismissed\n  useEffect(() => {\n    const dismissed = localStorage.getItem('pwa-install-dismissed')\n    if (dismissed === 'true') {\n      setShowBanner(false)\n    }\n  }, [])\n\n  if (!showBanner) return null\n\n  return (\n    <div \n      id=\"pwa-install-banner\"\n      className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card\"\n    >\n      <div className=\"flex items-start gap-3\">\n        <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n          <Smartphone className=\"h-5 w-5\" />\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-sm md:text-base mb-1\">\n            تثبيت التطبيق\n          </h3>\n          <p className=\"text-blue-100 text-xs md:text-sm mb-3\">\n            ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت\n          </p>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleInstallClick}\n              className=\"bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1\"\n            >\n              <Download className=\"h-3 w-3 md:h-4 md:w-4\" />\n              تثبيت\n            </button>\n            <button\n              onClick={handleDismiss}\n              className=\"text-blue-100 hover:text-white transition-colors\"\n            >\n              <X className=\"h-4 w-4 md:h-5 md:w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;wEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,cAAc;gBAChB;;YAEA,MAAM;iEAAqB;oBACzB,cAAc;oBACd,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,oCAAoC;YACpC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;gBAChF,cAAc;YAChB;YAEA;8CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;qCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,eAAe,MAAM;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,qCAAqC;QACrC,aAAa,OAAO,CAAC,yBAAyB;IAChD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,cAAc,QAAQ;gBACxB,cAAc;YAChB;QACF;qCAAG,EAAE;IAEL,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;8BAExB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGhD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAjGwB;KAAA", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ScreenSizeIndicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Monitor, Tablet, Smartphone, Laptop } from 'lucide-react'\n\nexport default function ScreenSizeIndicator() {\n  const [screenInfo, setScreenInfo] = useState({\n    width: 0,\n    height: 0,\n    breakpoint: '',\n    device: '',\n    orientation: ''\n  })\n\n  useEffect(() => {\n    const updateScreenInfo = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n      \n      let breakpoint = ''\n      let device = ''\n      \n      if (width < 640) {\n        breakpoint = 'sm (< 640px)'\n        device = 'هاتف صغير'\n      } else if (width < 768) {\n        breakpoint = 'md (640px - 768px)'\n        device = 'هاتف كبير'\n      } else if (width < 1024) {\n        breakpoint = 'lg (768px - 1024px)'\n        device = 'تابلت'\n      } else if (width < 1280) {\n        breakpoint = 'xl (1024px - 1280px)'\n        device = 'لابتوب'\n      } else {\n        breakpoint = '2xl (> 1280px)'\n        device = 'كمبيوتر مكتبي'\n      }\n\n      const orientation = width > height ? 'أفقي' : 'عمودي'\n\n      setScreenInfo({\n        width,\n        height,\n        breakpoint,\n        device,\n        orientation\n      })\n    }\n\n    updateScreenInfo()\n    window.addEventListener('resize', updateScreenInfo)\n    window.addEventListener('orientationchange', updateScreenInfo)\n\n    return () => {\n      window.removeEventListener('resize', updateScreenInfo)\n      window.removeEventListener('orientationchange', updateScreenInfo)\n    }\n  }, [])\n\n  const getIcon = () => {\n    if (screenInfo.width < 768) return Smartphone\n    if (screenInfo.width < 1024) return Tablet\n    if (screenInfo.width < 1280) return Laptop\n    return Monitor\n  }\n\n  const Icon = getIcon()\n\n  return (\n    <div className=\"fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block\">\n      <div className=\"flex items-center gap-2 mb-1\">\n        <Icon className=\"h-4 w-4\" />\n        <span className=\"font-medium\">{screenInfo.device}</span>\n      </div>\n      <div className=\"space-y-1\">\n        <div>{screenInfo.width} × {screenInfo.height}</div>\n        <div>{screenInfo.breakpoint}</div>\n        <div>{screenInfo.orientation}</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;kEAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,MAAM,SAAS,OAAO,WAAW;oBAEjC,IAAI,aAAa;oBACjB,IAAI,SAAS;oBAEb,IAAI,QAAQ,KAAK;wBACf,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,KAAK;wBACtB,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,MAAM;wBACvB,aAAa;wBACb,SAAS;oBACX,OAAO,IAAI,QAAQ,MAAM;wBACvB,aAAa;wBACb,SAAS;oBACX,OAAO;wBACL,aAAa;wBACb,SAAS;oBACX;oBAEA,MAAM,cAAc,QAAQ,SAAS,SAAS;oBAE9C,cAAc;wBACZ;wBACA;wBACA;wBACA;wBACA;oBACF;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C;iDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,qBAAqB;gBAClD;;QACF;wCAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI,WAAW,KAAK,GAAG,KAAK,OAAO,iNAAA,CAAA,aAAU;QAC7C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,yMAAA,CAAA,SAAM;QAC1C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,yMAAA,CAAA,SAAM;QAC1C,OAAO,2MAAA,CAAA,UAAO;IAChB;IAEA,MAAM,OAAO;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAe,WAAW,MAAM;;;;;;;;;;;;0BAElD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAK,WAAW,KAAK;4BAAC;4BAAI,WAAW,MAAM;;;;;;;kCAC5C,6LAAC;kCAAK,WAAW,UAAU;;;;;;kCAC3B,6LAAC;kCAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;AAIpC;GA7EwB;KAAA", "debugId": null}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport Header from './Header'\nimport ErrorBoundary from './ErrorBoundary'\nimport ToastNotifications from './ToastNotifications'\nimport MobileOptimizer from './MobileOptimizer'\nimport PWAInstallBanner from './PWAInstallBanner'\nimport ScreenSizeIndicator from './ScreenSizeIndicator'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <MobileOptimizer />\n      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n      <Header\n        onMobileMenuToggle={toggleMobileMenu}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n      <main className={`\n        mr-0 md:mr-64\n        mt-16\n        p-3 md:p-6\n        main-content-mobile\n        min-h-screen\n        w-full\n        max-w-full\n        overflow-x-hidden\n        safe-area-inset-bottom\n      `}>\n        <ErrorBoundary>\n          <div className=\"max-w-full overflow-x-auto container\">\n            {children}\n          </div>\n        </ErrorBoundary>\n      </main>\n      <ToastNotifications />\n      <PWAInstallBanner />\n      <ScreenSizeIndicator />\n\n      {/* Mobile overlay when sidebar is open */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAee,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IAChC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,gIAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;0BACtE,6LAAC,+HAAA,CAAA,UAAM;gBACL,oBAAoB;gBACpB,kBAAkB;;;;;;0BAEpB,6LAAC;gBAAK,WAAY;0BAWhB,cAAA,6LAAC,sIAAA,CAAA,UAAa;8BACZ,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;0BAIP,6LAAC,2IAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC,yIAAA,CAAA,UAAgB;;;;;0BACjB,6LAAC,4IAAA,CAAA,UAAmB;;;;;YAGnB,kCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAK7C;GA7CwB;KAAA", "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth, usePermissions } from '@/contexts/AuthContext'\nimport { UserPermissions } from '@/lib/auth-database'\nimport { Loader2, Shield, AlertTriangle } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredPermissions?: (keyof UserPermissions)[]\n  requiredRole?: string\n  fallback?: React.ReactNode\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requiredPermissions = [], \n  requiredRole,\n  fallback \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, user } = useAuth()\n  const { hasPermission, hasAnyPermission } = usePermissions()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  // عرض شاشة التحميل\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">جاري التحقق من الصلاحيات...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // إعادة توجيه إذا لم يكن مسجل دخول\n  if (!isAuthenticated) {\n    return null\n  }\n\n  // التحقق من الدور المطلوب\n  if (requiredRole && user?.role !== requiredRole) {\n    return fallback || (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n          <AlertTriangle className=\"h-16 w-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            غير مصرح لك\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            هذه الصفحة مخصصة للمستخدمين من نوع: {requiredRole}\n          </p>\n          <button\n            onClick={() => router.back()}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            العودة\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  // التحقق من الصلاحيات المطلوبة\n  if (requiredPermissions.length > 0) {\n    const hasRequiredPermissions = hasAnyPermission(requiredPermissions)\n    \n    if (!hasRequiredPermissions) {\n      return fallback || (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n            <Shield className=\"h-16 w-16 text-red-500 mx-auto mb-4\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              صلاحيات غير كافية\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة\n            </p>\n            <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n              <p className=\"text-sm text-gray-700 font-medium mb-2\">الصلاحيات المطلوبة:</p>\n              <ul className=\"text-sm text-gray-600 space-y-1\">\n                {requiredPermissions.map((permission) => (\n                  <li key={permission} className=\"flex items-center gap-2\">\n                    <span className=\"w-2 h-2 bg-red-400 rounded-full\"></span>\n                    {translatePermission(permission)}\n                  </li>\n                ))}\n              </ul>\n            </div>\n            <button\n              onClick={() => router.back()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              العودة\n            </button>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من صلاحية واحدة فقط\ninterface PermissionGuardProps {\n  permission: keyof UserPermissions\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function PermissionGuard({ \n  permission, \n  children, \n  fallback,\n  showMessage = false \n}: PermissionGuardProps) {\n  const { hasPermission } = usePermissions()\n\n  if (!hasPermission(permission)) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <p className=\"text-yellow-800 text-sm\">\n              ليس لديك صلاحية: {translatePermission(permission)}\n            </p>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من عدة صلاحيات\ninterface MultiPermissionGuardProps {\n  permissions: (keyof UserPermissions)[]\n  requireAll?: boolean // true = يحتاج جميع الصلاحيات، false = يحتاج واحدة على الأقل\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function MultiPermissionGuard({ \n  permissions, \n  requireAll = false,\n  children, \n  fallback,\n  showMessage = false \n}: MultiPermissionGuardProps) {\n  const { hasPermission, hasAnyPermission } = usePermissions()\n\n  const hasAccess = requireAll \n    ? permissions.every(permission => hasPermission(permission))\n    : hasAnyPermission(permissions)\n\n  if (!hasAccess) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <div>\n              <p className=\"text-yellow-800 text-sm font-medium\">\n                ليس لديك الصلاحيات المطلوبة\n              </p>\n              <p className=\"text-yellow-700 text-xs mt-1\">\n                {requireAll ? 'تحتاج جميع الصلاحيات التالية:' : 'تحتاج واحدة على الأقل من الصلاحيات التالية:'}\n              </p>\n              <ul className=\"text-xs text-yellow-600 mt-2 space-y-1\">\n                {permissions.map((permission) => (\n                  <li key={permission}>• {translatePermission(permission)}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من الدور\ninterface RoleGuardProps {\n  role: string\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function RoleGuard({ \n  role, \n  children, \n  fallback,\n  showMessage = false \n}: RoleGuardProps) {\n  const { user } = useAuth()\n\n  if (user?.role !== role) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <p className=\"text-yellow-800 text-sm\">\n              هذه الميزة مخصصة للمستخدمين من نوع: {translateRole(role)}\n            </p>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// دوال الترجمة\nconst translatePermission = (permission: keyof UserPermissions): string => {\n  const translations: Record<keyof UserPermissions, string> = {\n    sales_view: 'عرض المبيعات',\n    sales_create: 'إنشاء مبيعات',\n    sales_edit: 'تعديل المبيعات',\n    sales_delete: 'حذف المبيعات',\n    sales_print: 'طباعة المبيعات',\n    sales_view_prices: 'عرض الأسعار',\n    \n    purchases_view: 'عرض المشتريات',\n    purchases_create: 'إنشاء مشتريات',\n    purchases_edit: 'تعديل المشتريات',\n    purchases_delete: 'حذف المشتريات',\n    purchases_print: 'طباعة المشتريات',\n    \n    inventory_view: 'عرض المخزون',\n    inventory_create: 'إضافة للمخزون',\n    inventory_edit: 'تعديل المخزون',\n    inventory_delete: 'حذف من المخزون',\n    inventory_print: 'طباعة المخزون',\n    \n    customers_view: 'عرض العملاء',\n    customers_create: 'إضافة عملاء',\n    customers_edit: 'تعديل العملاء',\n    customers_delete: 'حذف العملاء',\n    \n    suppliers_view: 'عرض الموردين',\n    suppliers_create: 'إضافة موردين',\n    suppliers_edit: 'تعديل الموردين',\n    suppliers_delete: 'حذف الموردين',\n    \n    reports_view: 'عرض التقارير',\n    reports_financial: 'التقارير المالية',\n    reports_detailed: 'التقارير المفصلة',\n    reports_export: 'تصدير التقارير',\n    \n    users_view: 'عرض المستخدمين',\n    users_create: 'إضافة مستخدمين',\n    users_edit: 'تعديل المستخدمين',\n    users_delete: 'حذف المستخدمين',\n    \n    settings_view: 'عرض الإعدادات',\n    settings_edit: 'تعديل الإعدادات',\n    \n    cashbox_view: 'عرض الصندوق',\n    cashbox_manage: 'إدارة الصندوق',\n    \n    returns_view: 'عرض المرتجعات',\n    returns_create: 'إنشاء مرتجعات',\n    returns_edit: 'تعديل المرتجعات',\n    returns_delete: 'حذف المرتجعات',\n  }\n  \n  return translations[permission] || permission\n}\n\nconst translateRole = (role: string): string => {\n  const translations: Record<string, string> = {\n    admin: 'مدير النظام',\n    manager: 'مدير',\n    pharmacist: 'صيدلي',\n    cashier: 'كاشير',\n    viewer: 'مشاهد'\n  }\n  \n  return translations[role] || role\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;;;AANA;;;;;AAee,SAAS,eAAe,KAKjB;QALiB,EACrC,QAAQ,EACR,sBAAsB,EAAE,EACxB,YAAY,EACZ,QAAQ,EACY,GALiB;;IAMrC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,mBAAmB;IACnB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,cAAc;QAC/C,OAAO,0BACL,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;;4BAAqB;4BACK;;;;;;;kCAEvC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,+BAA+B;IAC/B,IAAI,oBAAoB,MAAM,GAAG,GAAG;QAClC,MAAM,yBAAyB,iBAAiB;QAEhD,IAAI,CAAC,wBAAwB;YAC3B,OAAO,0BACL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;8CACX,oBAAoB,GAAG,CAAC,CAAC,2BACxB,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAK,WAAU;;;;;;gDACf,oBAAoB;;2CAFd;;;;;;;;;;;;;;;;sCAOf,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;IACF;IAEA,qBAAO;kBAAG;;AACZ;GA/FwB;;QAMuB,kIAAA,CAAA,UAAO;QACR,kIAAA,CAAA,iBAAc;QAC3C,qIAAA,CAAA,YAAS;;;KARF;AAyGjB,SAAS,gBAAgB,KAKT;QALS,EAC9B,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACE,GALS;;IAM9B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEvC,IAAI,CAAC,cAAc,aAAa;QAC9B,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAE,WAAU;;gCAA0B;gCACnB,oBAAoB;;;;;;;;;;;;;;;;;;QAKhD;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;IAzBgB;;QAMY,kIAAA,CAAA,iBAAc;;;MAN1B;AAoCT,SAAS,qBAAqB,KAMT;QANS,EACnC,WAAW,EACX,aAAa,KAAK,EAClB,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACO,GANS;;IAOnC,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEzD,MAAM,YAAY,aACd,YAAY,KAAK,CAAC,CAAA,aAAc,cAAc,eAC9C,iBAAiB;IAErB,IAAI,CAAC,WAAW;QACd,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAsC;;;;;;8CAGnD,6LAAC;oCAAE,WAAU;8CACV,aAAa,kCAAkC;;;;;;8CAElD,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;;gDAAoB;gDAAG,oBAAoB;;2CAAnC;;;;;;;;;;;;;;;;;;;;;;;;;;;QAOvB;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;IAxCgB;;QAO8B,kIAAA,CAAA,iBAAc;;;MAP5C;AAkDT,SAAS,UAAU,KAKT;QALS,EACxB,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACJ,GALS;;IAMxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,MAAM;QACvB,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAE,WAAU;;gCAA0B;gCACA,cAAc;;;;;;;;;;;;;;;;;;QAK7D;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;IAzBgB;;QAMG,kIAAA,CAAA,UAAO;;;MANV;AA2BhB,eAAe;AACf,MAAM,sBAAsB,CAAC;IAC3B,MAAM,eAAsD;QAC1D,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,mBAAmB;QAEnB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QAEjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QAEjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,cAAc;QACd,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAEhB,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,cAAc;QAEd,eAAe;QACf,eAAe;QAEf,cAAc;QACd,gBAAgB;QAEhB,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,gBAAgB;IAClB;IAEA,OAAO,YAAY,CAAC,WAAW,IAAI;AACrC;AAEA,MAAM,gBAAgB,CAAC;IACrB,MAAM,eAAuC;QAC3C,OAAO;QACP,SAAS;QACT,YAAY;QACZ,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,YAAY,CAAC,KAAK,IAAI;AAC/B", "debugId": null}}, {"offset": {"line": 3233, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/app/notifications/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport AppLayout from '@/components/AppLayout'\nimport ProtectedRoute from '@/components/ProtectedRoute'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  Filter,\n  Search,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign,\n  RefreshCw,\n  Archive\n} from 'lucide-react'\n\nexport default function NotificationsPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [categoryFilter, setCategoryFilter] = useState('all')\n  const [priorityFilter, setPriorityFilter] = useState('all')\n  const [statusFilter, setStatusFilter] = useState('all')\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll,\n    refreshNotifications\n  } = useNotifications()\n\n  // تصفية التنبيهات\n  const filteredNotifications = notifications.filter(notification => {\n    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         notification.message.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesCategory = categoryFilter === 'all' || notification.category === categoryFilter\n    const matchesPriority = priorityFilter === 'all' || notification.priority === priorityFilter\n    const matchesStatus = statusFilter === 'all' || \n                         (statusFilter === 'read' && notification.isRead) ||\n                         (statusFilter === 'unread' && !notification.isRead)\n    \n    return matchesSearch && matchesCategory && matchesPriority && matchesStatus\n  })\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-5 w-5 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-5 w-5 text-blue-500\" />\n    \n    if (category === 'inventory') return <Package className=\"h-5 w-5 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-5 w-5 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-5 w-5 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-5 w-5 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-5 w-5 text-gray-500\" />\n    \n    return <Bell className=\"h-5 w-5 text-gray-500\" />\n  }\n\n  const getPriorityBadge = (priority: string) => {\n    const colors = {\n      critical: 'bg-red-100 text-red-800 border-red-200',\n      high: 'bg-orange-100 text-orange-800 border-orange-200',\n      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n      low: 'bg-blue-100 text-blue-800 border-blue-200'\n    }\n    \n    const labels = {\n      critical: 'حرج',\n      high: 'عالي',\n      medium: 'متوسط',\n      low: 'منخفض'\n    }\n    \n    return (\n      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${colors[priority as keyof typeof colors]}`}>\n        {labels[priority as keyof typeof labels]}\n      </span>\n    )\n  }\n\n  const getCategoryBadge = (category: string) => {\n    const colors = {\n      inventory: 'bg-purple-100 text-purple-800',\n      sales: 'bg-green-100 text-green-800',\n      financial: 'bg-yellow-100 text-yellow-800',\n      user: 'bg-blue-100 text-blue-800',\n      system: 'bg-gray-100 text-gray-800'\n    }\n    \n    const labels = {\n      inventory: 'مخزون',\n      sales: 'مبيعات',\n      financial: 'مالي',\n      user: 'مستخدمين',\n      system: 'نظام'\n    }\n    \n    return (\n      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[category as keyof typeof colors]}`}>\n        {labels[category as keyof typeof labels]}\n      </span>\n    )\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <AppLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">التنبيهات</h1>\n              <p className=\"text-gray-600 mt-1\">\n                إدارة ومتابعة جميع تنبيهات النظام\n              </p>\n            </div>\n            \n            <div className=\"flex items-center gap-3\">\n              <button\n                onClick={refreshNotifications}\n                className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                تحديث\n              </button>\n              \n              {unreadCount > 0 && (\n                <button\n                  onClick={markAllAsRead}\n                  className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <CheckCheck className=\"h-4 w-4\" />\n                  تحديد الكل كمقروء\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Statistics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Bell className=\"h-8 w-8 text-blue-600\" />\n                </div>\n                <div className=\"mr-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      إجمالي التنبيهات\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {notifications.length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n                </div>\n                <div className=\"mr-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      غير مقروءة\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {unreadCount}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <XCircle className=\"h-8 w-8 text-red-600\" />\n                </div>\n                <div className=\"mr-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      حرجة\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {notifications.filter(n => n.priority === 'critical').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <Package className=\"h-8 w-8 text-purple-600\" />\n                </div>\n                <div className=\"mr-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      تنبيهات المخزون\n                    </dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">\n                      {notifications.filter(n => n.category === 'inventory').length}\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n              {/* Search */}\n              <div className=\"relative\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"البحث في التنبيهات...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n\n              {/* Category Filter */}\n              <div>\n                <select\n                  value={categoryFilter}\n                  onChange={(e) => setCategoryFilter(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">جميع الفئات</option>\n                  <option value=\"inventory\">مخزون</option>\n                  <option value=\"sales\">مبيعات</option>\n                  <option value=\"financial\">مالي</option>\n                  <option value=\"user\">مستخدمين</option>\n                  <option value=\"system\">نظام</option>\n                </select>\n              </div>\n\n              {/* Priority Filter */}\n              <div>\n                <select\n                  value={priorityFilter}\n                  onChange={(e) => setPriorityFilter(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">جميع الأولويات</option>\n                  <option value=\"critical\">حرج</option>\n                  <option value=\"high\">عالي</option>\n                  <option value=\"medium\">متوسط</option>\n                  <option value=\"low\">منخفض</option>\n                </select>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">جميع الحالات</option>\n                  <option value=\"unread\">غير مقروءة</option>\n                  <option value=\"read\">مقروءة</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Notifications List */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-12 text-center\">\n                <Bell className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا توجد تنبيهات</h3>\n                <p className=\"text-gray-500\">لا توجد تنبيهات تطابق المعايير المحددة</p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-200\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-6 hover:bg-gray-50 transition-colors ${\n                      !notification.isRead ? 'bg-blue-50 border-r-4 border-blue-500' : ''\n                    }`}\n                  >\n                    <div className=\"flex items-start gap-4\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <div className=\"flex items-center gap-2 mb-2\">\n                              <h3 className={`text-lg font-medium ${\n                                !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                              }`}>\n                                {notification.title}\n                              </h3>\n                              {!notification.isRead && (\n                                <span className=\"w-2 h-2 bg-blue-500 rounded-full\"></span>\n                              )}\n                            </div>\n                            \n                            <p className=\"text-gray-600 mb-3\">\n                              {notification.message}\n                            </p>\n                            \n                            <div className=\"flex items-center gap-3 mb-3\">\n                              {getCategoryBadge(notification.category)}\n                              {getPriorityBadge(notification.priority)}\n                            </div>\n                            \n                            <div className=\"flex items-center gap-1 text-sm text-gray-500\">\n                              <Clock className=\"h-4 w-4\" />\n                              {formatTimeAgo(notification.createdAt)}\n                            </div>\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-2 mr-4\">\n                            {notification.actionUrl && (\n                              <button\n                                onClick={() => handleNotificationClick(notification)}\n                                className=\"flex items-center gap-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded-lg hover:bg-blue-50\"\n                              >\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض'}\n                              </button>\n                            )}\n                            \n                            {!notification.isRead && (\n                              <button\n                                onClick={() => markAsRead(notification.id)}\n                                className=\"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-4 w-4\" />\n                              </button>\n                            )}\n                            \n                            <button\n                              onClick={() => removeNotification(notification.id)}\n                              className=\"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg\"\n                              title=\"حذف\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Bulk Actions */}\n          {notifications.length > 0 && (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm text-gray-600\">\n                  عرض {filteredNotifications.length} من {notifications.length} تنبيه\n                </div>\n                \n                <div className=\"flex items-center gap-3\">\n                  <button\n                    onClick={clearAll}\n                    className=\"flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                    مسح جميع التنبيهات\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </AppLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA6Be,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACR,oBAAoB,EACrB,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,kBAAkB;IAClB,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,MAAM,gBAAgB,aAAa,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,aAAa,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvF,MAAM,kBAAkB,mBAAmB,SAAS,aAAa,QAAQ,KAAK;QAC9E,MAAM,kBAAkB,mBAAmB,SAAS,aAAa,QAAQ,KAAK;QAC9E,MAAM,gBAAgB,iBAAiB,SACjB,iBAAiB,UAAU,aAAa,MAAM,IAC9C,iBAAiB,YAAY,CAAC,aAAa,MAAM;QAEvE,OAAO,iBAAiB,mBAAmB,mBAAmB;IAChE;IAEA,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,IAAI,aAAa,aAAa,qBAAO,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,MAAM,SAAS;YACb,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,qBACE,6LAAC;YAAK,WAAW,AAAC,8EAAqH,OAAxC,MAAM,CAAC,SAAgC;sBACnI,MAAM,CAAC,SAAgC;;;;;;IAG9C;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,WAAW;YACX,OAAO;YACP,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QAEA,MAAM,SAAS;YACb,WAAW;YACX,OAAO;YACP,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QAEA,qBACE,6LAAC;YAAK,WAAW,AAAC,uEAA8G,OAAxC,MAAM,CAAC,SAAgC;sBAC5H,MAAM,CAAC,SAAgC;;;;;;IAG9C;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,AAAC,OAAoB,OAAd,eAAc;QAEpD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,AAAC,OAAkB,OAAZ,aAAY;QAEhD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,AAAC,OAAiB,OAAX,YAAW;IAC3B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;IACF;IAEA,qBACE,6LAAC,uIAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;oCAIlC,cAAc,mBACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAQ1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACX,cAAc,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACX,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACX,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;8CAK3B,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;8CACC,cAAA,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7B,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAG/B,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC;oCAEC,WAAW,AAAC,0CAEX,OADC,CAAC,aAAa,MAAM,GAAG,0CAA0C;8CAGnE,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAW,AAAC,uBAEf,OADC,CAAC,aAAa,MAAM,GAAG,kBAAkB;sFAExC,aAAa,KAAK;;;;;;wEAEpB,CAAC,aAAa,MAAM,kBACnB,6LAAC;4EAAK,WAAU;;;;;;;;;;;;8EAIpB,6LAAC;oEAAE,WAAU;8EACV,aAAa,OAAO;;;;;;8EAGvB,6LAAC;oEAAI,WAAU;;wEACZ,iBAAiB,aAAa,QAAQ;wEACtC,iBAAiB,aAAa,QAAQ;;;;;;;8EAGzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;sEAKzC,6LAAC;4DAAI,WAAU;;gEACZ,aAAa,SAAS,kBACrB,6LAAC;oEACC,SAAS,IAAM,wBAAwB;oEACvC,WAAU;;sFAEV,6LAAC,yNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEACvB,aAAa,WAAW,IAAI;;;;;;;gEAIhC,CAAC,aAAa,MAAM,kBACnB,6LAAC;oEACC,SAAS,IAAM,WAAW,aAAa,EAAE;oEACzC,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAIrB,6LAAC;oEACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;oEACjD,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCApEvB,aAAa,EAAE;;;;;;;;;;;;;;;oBAiF7B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAChC,sBAAsB,MAAM;wCAAC;wCAAK,cAAc,MAAM;wCAAC;;;;;;;8CAG9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlD;GAnZwB;;QAKP,qIAAA,CAAA,YAAS;QAUpB,0IAAA,CAAA,mBAAgB;;;KAfE", "debugId": null}}]}