'use client'

import { useState, useEffect } from 'react'
import { Monitor, Tablet, Smartphone, Laptop } from 'lucide-react'

export default function ScreenSizeIndicator() {
  const [screenInfo, setScreenInfo] = useState({
    width: 0,
    height: 0,
    breakpoint: '',
    device: '',
    orientation: ''
  })

  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      let breakpoint = ''
      let device = ''
      
      if (width < 640) {
        breakpoint = 'sm (< 640px)'
        device = 'هاتف صغير'
      } else if (width < 768) {
        breakpoint = 'md (640px - 768px)'
        device = 'هاتف كبير'
      } else if (width < 1024) {
        breakpoint = 'lg (768px - 1024px)'
        device = 'تابلت'
      } else if (width < 1280) {
        breakpoint = 'xl (1024px - 1280px)'
        device = 'لابتوب'
      } else {
        breakpoint = '2xl (> 1280px)'
        device = 'كمبيوتر مكتبي'
      }

      const orientation = width > height ? 'أفقي' : 'عمودي'

      setScreenInfo({
        width,
        height,
        breakpoint,
        device,
        orientation
      })
    }

    updateScreenInfo()
    window.addEventListener('resize', updateScreenInfo)
    window.addEventListener('orientationchange', updateScreenInfo)

    return () => {
      window.removeEventListener('resize', updateScreenInfo)
      window.removeEventListener('orientationchange', updateScreenInfo)
    }
  }, [])

  const getIcon = () => {
    if (screenInfo.width < 768) return Smartphone
    if (screenInfo.width < 1024) return Tablet
    if (screenInfo.width < 1280) return Laptop
    return Monitor
  }

  const Icon = getIcon()

  return (
    <div className="fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block">
      <div className="flex items-center gap-2 mb-1">
        <Icon className="h-4 w-4" />
        <span className="font-medium">{screenInfo.device}</span>
      </div>
      <div className="space-y-1">
        <div>{screenInfo.width} × {screenInfo.height}</div>
        <div>{screenInfo.breakpoint}</div>
        <div>{screenInfo.orientation}</div>
      </div>
    </div>
  )
}
