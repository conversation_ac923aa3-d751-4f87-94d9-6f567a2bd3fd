'use client'

import { useState } from 'react'
import { LogIn, User, Lock, CheckCircle, AlertCircle, Eye, EyeOff } from 'lucide-react'

export default function TestLoginPage() {
  const [showInstructions, setShowInstructions] = useState(true)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Instructions Card */}
        {showInstructions && (
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <h2 className="text-lg font-semibold text-gray-900">تعليمات تسجيل الدخول</h2>
              </div>
              <button
                onClick={() => setShowInstructions(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">بيانات تسجيل الدخول التجريبية:</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    <span className="text-blue-800">اسم المستخدم: <strong>admin</strong></span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-blue-600" />
                    <span className="text-blue-800">كلمة المرور: <strong>admin123</strong></span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">خطوات الاختبار:</h3>
                <ol className="text-sm text-green-800 space-y-1">
                  <li>1. اذهب إلى صفحة تسجيل الدخول</li>
                  <li>2. أدخل البيانات المذكورة أعلاه</li>
                  <li>3. اضغط "تسجيل الدخول"</li>
                  <li>4. ابحث عن زر القائمة (☰) في الهيدر</li>
                  <li>5. اضغط على الزر لفتح الشريط الجانبي</li>
                </ol>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-yellow-900 mb-1">ملاحظة مهمة:</h3>
                    <p className="text-sm text-yellow-800">
                      إذا لم يظهر زر القائمة، تأكد من أنك مسجل دخول بنجاح ووصلت إلى الصفحة الرئيسية.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 flex gap-3">
              <a
                href="/login"
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center flex items-center justify-center gap-2"
              >
                <LogIn className="h-4 w-4" />
                اذهب لتسجيل الدخول
              </a>
              <a
                href="/debug-sidebar"
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center"
              >
                اختبار مبسط
              </a>
            </div>
          </div>
        )}

        {/* Quick Login Form */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
          <div className="text-center mb-6">
            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <LogIn className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">تسجيل دخول سريع</h1>
            <p className="text-gray-600 mt-2">استخدم البيانات التجريبية للاختبار</p>
          </div>

          <form action="/login" method="get" className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم
              </label>
              <div className="relative">
                <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value="admin"
                  readOnly
                  className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 cursor-not-allowed"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور
              </label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value="admin123"
                  readOnly
                  className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 cursor-not-allowed"
                />
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
            >
              <LogIn className="h-5 w-5" />
              انتقل إلى صفحة تسجيل الدخول
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              سيتم نقلك إلى صفحة تسجيل الدخول حيث يمكنك إدخال البيانات يدوياً
            </p>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-900">الإصلاحات</p>
            <p className="text-xs text-gray-600">مكتملة</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
            <AlertCircle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-900">جاهز للاختبار</p>
            <p className="text-xs text-gray-600">سجل دخولك الآن</p>
          </div>
        </div>

        {/* Links */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="font-medium text-gray-900 mb-3">روابط مفيدة:</h3>
          <div className="space-y-2 text-sm">
            <a href="/debug-sidebar" className="block text-blue-600 hover:text-blue-800">
              • اختبار الشريط الجانبي المبسط
            </a>
            <a href="/test-fixes" className="block text-blue-600 hover:text-blue-800">
              • صفحة اختبار الإصلاحات
            </a>
            <a href="/test-responsive" className="block text-blue-600 hover:text-blue-800">
              • اختبار التجاوب مع الهواتف
            </a>
            <a href="/" className="block text-blue-600 hover:text-blue-800">
              • الصفحة الرئيسية (يتطلب تسجيل دخول)
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
