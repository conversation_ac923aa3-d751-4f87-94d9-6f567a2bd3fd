{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/styles/classic-print.css"], "sourcesContent": ["/* Classic Invoice Print Styles - Traditional Arabic Invoice Format */\n\n@media print {\n    /* Reset and base styles */\n    * {\n        -webkit-print-color-adjust: exact !important;\n        color-adjust: exact !important;\n    }\n    body {\n        margin: 0;\n        padding: 0;\n        font-family: 'Arial', 'Tahoma', sans-serif;\n        font-size: 12px;\n        line-height: 1.3;\n        color: #000;\n        background: white;\n        direction: rtl;\n    }\n    .print-content {\n        max-width: none;\n        margin: 0;\n        padding: 8mm;\n        box-shadow: none;\n        border: none;\n        background: white;\n        page-break-inside: avoid;\n    }\n    /* Hide non-print elements */\n    .no-print,\n    button,\n    .btn,\n    .print-button,\n    .fixed,\n    .modal,\n    .dropdown {\n        display: none !important;\n    }\n    /* Page breaks */\n    .page-break {\n        page-break-before: always;\n        break-before: page;\n    }\n    .avoid-break {\n        page-break-inside: avoid;\n        break-inside: avoid;\n    }\n    /* Classic header styling */\n    .invoice-header {\n        border: 3px solid #000;\n        margin-bottom: 8px;\n        page-break-inside: avoid;\n    }\n    .header-row {\n        display: table;\n        width: 100%;\n        table-layout: fixed;\n        border-collapse: collapse;\n    }\n    .header-cell {\n        display: table-cell;\n        vertical-align: middle;\n        padding: 8px;\n        border-right: 2px solid #000;\n        text-align: center;\n        height: 80px;\n    }\n    .header-cell:last-child {\n        border-right: none;\n    }\n    .header-cell:first-child {\n        text-align: center;\n        vertical-align: middle;\n    }\n    /* Company logo area */\n    .logo-container {\n        width: 80px;\n        height: 80px;\n        border: 2px solid #000;\n        margin: 0 auto;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f8f8f8;\n    }\n    /* Classic table styling */\n    .invoice-table {\n        width: 100%;\n        border-collapse: collapse;\n        border: 2px solid #000;\n        margin: 8px 0;\n        page-break-inside: avoid;\n    }\n    .invoice-table th,\n    .invoice-table td {\n        border: 1px solid #000;\n        padding: 6px 4px;\n        text-align: center;\n        vertical-align: middle;\n        font-size: 11px;\n        line-height: 1.2;\n    }\n    .invoice-table th {\n        background-color: #f0f0f0;\n        font-weight: bold;\n        height: 30px;\n    }\n    .invoice-table td {\n        height: 28px;\n    }\n    /* Column widths */\n    .col-serial {\n        width: 6%;\n        min-width: 30px;\n    }\n    .col-medicine {\n        width: 40%;\n        min-width: 120px;\n    }\n    .col-quantity {\n        width: 10%;\n        min-width: 50px;\n    }\n    .col-price {\n        width: 15%;\n        min-width: 70px;\n    }\n    .col-total {\n        width: 15%;\n        min-width: 70px;\n    }\n    .col-batch {\n        width: 8%;\n        min-width: 50px;\n    }\n    .col-expiry {\n        width: 6%;\n        min-width: 40px;\n    }\n    /* Medicine name cell specific styling */\n    .medicine-cell {\n        text-align: right !important;\n        padding-right: 8px !important;\n        vertical-align: top !important;\n    }\n    /* Number cells */\n    .number-cell {\n        text-align: center !important;\n        font-family: 'Courier New', monospace !important;\n        font-weight: bold !important;\n    }\n    /* Text alignment for specific columns */\n    .text-right {\n        text-align: right !important;\n    }\n    .text-center {\n        text-align: center !important;\n    }\n    .text-left {\n        text-align: left !important;\n    }\n    /* Font weights and sizes */\n    .font-bold {\n        font-weight: bold !important;\n    }\n    .font-normal {\n        font-weight: normal !important;\n    }\n    .text-xl {\n        font-size: 18px !important;\n    }\n    .text-lg {\n        font-size: 16px !important;\n    }\n    .text-md {\n        font-size: 14px !important;\n    }\n    .text-sm {\n        font-size: 12px !important;\n    }\n    .text-xs {\n        font-size: 10px !important;\n    }\n    /* Spacing utilities */\n    .p-1 {\n        padding: 2px !important;\n    }\n    .p-2 {\n        padding: 4px !important;\n    }\n    .p-3 {\n        padding: 6px !important;\n    }\n    .p-4 {\n        padding: 8px !important;\n    }\n    .m-0 {\n        margin: 0 !important;\n    }\n    .mb-1 {\n        margin-bottom: 2px !important;\n    }\n    .mb-2 {\n        margin-bottom: 4px !important;\n    }\n    .mb-4 {\n        margin-bottom: 8px !important;\n    }\n    .mt-1 {\n        margin-top: 2px !important;\n    }\n    .mt-2 {\n        margin-top: 4px !important;\n    }\n    /* Border utilities */\n    .border {\n        border: 1px solid #000 !important;\n    }\n    .border-2 {\n        border: 2px solid #000 !important;\n    }\n    .border-3 {\n        border: 3px solid #000 !important;\n    }\n    .border-black {\n        border-color: #000 !important;\n    }\n    .border-t {\n        border-top: 1px solid #000 !important;\n    }\n    .border-b {\n        border-bottom: 1px solid #000 !important;\n    }\n    .border-l {\n        border-left: 1px solid #000 !important;\n    }\n    .border-r {\n        border-right: 1px solid #000 !important;\n    }\n    .border-t-2 {\n        border-top: 2px solid #000 !important;\n    }\n    .border-b-2 {\n        border-bottom: 2px solid #000 !important;\n    }\n    .border-l-2 {\n        border-left: 2px solid #000 !important;\n    }\n    .border-r-2 {\n        border-right: 2px solid #000 !important;\n    }\n    /* Grid system for print */\n    .grid {\n        display: table;\n        width: 100%;\n        table-layout: fixed;\n    }\n    .grid-cols-2>div {\n        display: table-cell;\n        width: 50%;\n        vertical-align: top;\n    }\n    .grid-cols-3>div {\n        display: table-cell;\n        width: 33.333%;\n        vertical-align: top;\n    }\n    .grid-cols-4>div {\n        display: table-cell;\n        width: 25%;\n        vertical-align: top;\n    }\n    /* Notes section */\n    .notes-section {\n        border: 2px solid #000;\n        padding: 6px;\n        margin: 8px 0;\n        min-height: 40px;\n    }\n    /* Signature area */\n    .signature-area {\n        margin-top: 20px;\n        page-break-inside: avoid;\n    }\n    .signature-circle {\n        width: 120px;\n        height: 80px;\n        border: 2px solid #000;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0 auto;\n        font-size: 11px;\n        font-weight: bold;\n        text-align: center;\n    }\n    /* Footer */\n    .print-footer {\n        border-top: 2px solid #000;\n        padding-top: 6px;\n        margin-top: 15px;\n        font-size: 10px;\n        text-align: center;\n    }\n    /* Color adjustments for print */\n    .text-blue-600,\n    .text-green-600,\n    .text-red-600,\n    .text-yellow-600,\n    .text-purple-600 {\n        color: #000 !important;\n    }\n    .bg-blue-100,\n    .bg-green-100,\n    .bg-red-100,\n    .bg-yellow-100,\n    .bg-purple-100,\n    .bg-gray-100 {\n        background-color: #f5f5f5 !important;\n    }\n    .bg-gray-50 {\n        background-color: #fafafa !important;\n    }\n    /* Remove modern styling for print */\n    .rounded,\n    .rounded-lg,\n    .rounded-md,\n    .rounded-sm {\n        border-radius: 0 !important;\n    }\n    .shadow,\n    .shadow-sm,\n    .shadow-md,\n    .shadow-lg {\n        box-shadow: none !important;\n    }\n    /* Ensure proper contrast */\n    .text-gray-600,\n    .text-gray-700,\n    .text-gray-800 {\n        color: #333 !important;\n    }\n    .text-gray-500 {\n        color: #666 !important;\n    }\n    /* Number formatting */\n    .font-mono {\n        font-family: 'Courier New', 'Lucida Console', monospace !important;\n    }\n    /* Arabic text improvements */\n    .arabic-text {\n        font-family: 'Tahoma', 'Arial', sans-serif !important;\n        direction: rtl;\n        text-align: right;\n    }\n    .english-text {\n        font-family: 'Arial', 'Times New Roman', serif !important;\n        direction: ltr;\n        text-align: left;\n    }\n    /* Ensure table borders are visible */\n    table {\n        border-collapse: collapse !important;\n    }\n    /* Page margins */\n    @page {\n        margin: 15mm;\n        size: A4;\n    }\n}\n\n\n/* Screen styles for preview */\n\n@media screen {\n    .print-content {\n        max-width: 210mm;\n        margin: 0 auto;\n        background: white;\n        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n        padding: 20px;\n    }\n}"], "names": [], "mappings": "AAEA;EAEI;;;;;EAIA;;;;;;;;;;;EAUA;;;;;;;;;;EAUA;;;;EAUA;;;;;EAIA;;;;;EAKA;;;;;;EAKA;;;;;;;EAMA;;;;;;;;;EAQA;;;;EAGA;;;;;EAKA;;;;;;;;;;;EAWA;;;;;;;;EAOA;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAQA;;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAMA;;;;;;;EAOA;;;;;EAIA;;;;;;;;;;;;;;EAcA;;;;;;;;EAQA;;;;EAOA;;;;EAQA;;;;EAIA;;;;EAMA;;;;EAOA;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;;AASJ;EACI", "debugId": null}}]}