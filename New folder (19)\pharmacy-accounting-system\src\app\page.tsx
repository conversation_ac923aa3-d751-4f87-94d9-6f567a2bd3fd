'use client'

import AppLayout from '@/components/AppLayout'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import {
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Calendar,
  Pill,
  Shield
} from 'lucide-react'
import { formatCurrency, formatNumber } from '@/utils/formatters'
import { useState } from 'react'
import { Menu, X } from 'lucide-react'

const stats = [
  {
    title: 'إجمالي المبيعات اليوم',
    value: formatNumber(2450000),
    unit: 'د.ع',
    change: '+12%',
    changeType: 'positive' as const,
    icon: DollarSign
  },
  {
    title: 'عدد الفواتير',
    value: formatNumber(156),
    unit: 'فاتورة',
    change: '+8%',
    changeType: 'positive' as const,
    icon: ShoppingCart
  },
  {
    title: 'الأدوية المتوفرة',
    value: formatNumber(1247),
    unit: 'صنف',
    change: '-2%',
    changeType: 'negative' as const,
    icon: Package
  },
  {
    title: 'العملاء النشطين',
    value: formatNumber(89),
    unit: 'عميل',
    change: '+5%',
    changeType: 'positive' as const,
    icon: Users
  }
]

const recentSales = [
  { id: '001', customer: 'أحمد محمد', amount: 125000, time: '10:30 ص' },
  { id: '002', customer: 'فاطمة علي', amount: 89000, time: '10:15 ص' },
  { id: '003', customer: 'محمد حسن', amount: 156000, time: '09:45 ص' },
  { id: '004', customer: 'سارة أحمد', amount: 67000, time: '09:30 ص' },
  { id: '005', customer: 'علي محمود', amount: 234000, time: '09:15 ص' }
]

const expiringMedicines = [
  { name: 'باراسيتامول 500mg', batch: 'B001', expiry: '2024-08-15', quantity: 50 },
  { name: 'أموكسيسيلين 250mg', batch: 'B002', expiry: '2024-08-20', quantity: 30 },
  { name: 'إيبوبروفين 400mg', batch: 'B003', expiry: '2024-08-25', quantity: 25 }
]



export default function Home() {
  const { user } = useAuth()

  return (
    <ProtectedRoute>

      <AppLayout>
      <div className="space-y-6 md:space-y-8">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-4 md:p-8 text-white shadow-xl mobile-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className="text-2xl md:text-4xl font-bold mb-2">مرحباً بك، {user?.full_name || 'المستخدم'}</h1>
              <p className="text-blue-100 text-sm md:text-lg mb-4">نظام إدارة الصيدلية - مكتب لارين العلمي</p>
              <div className="flex flex-col md:flex-row items-start md:items-center gap-3 md:gap-6">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 md:h-5 md:w-5 text-blue-200" />
                  <span className="text-blue-100 text-sm md:text-base">{user?.role === 'admin' ? 'مدير النظام' : user?.role}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 md:h-5 md:w-5 text-blue-200" />
                  <span className="text-blue-100 text-sm md:text-base">{new Date().toLocaleDateString('ar-EG', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</span>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="bg-white bg-opacity-20 rounded-full p-6">
                <Pill className="h-16 w-16 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 card-grid-mobile">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            const gradients = [
              'from-blue-500 to-blue-600',
              'from-green-500 to-green-600',
              'from-purple-500 to-purple-600',
              'from-orange-500 to-orange-600'
            ]
            return (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-100 p-4 md:p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 mobile-card">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-xs md:text-sm font-medium text-gray-600 mb-2">{stat.title}</p>
                    <div className="flex items-baseline gap-2 mb-3">
                      <p className="text-2xl md:text-3xl font-bold text-gray-900">{stat.value}</p>
                      <span className="text-xs md:text-sm text-gray-500">{stat.unit}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className={`h-3 w-3 md:h-4 md:w-4 ${
                        stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'
                      }`} />
                      <span className={`text-xs md:text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-xs text-gray-500">من الأمس</span>
                    </div>
                  </div>
                  <div className={`bg-gradient-to-br ${gradients[index]} p-3 md:p-4 rounded-xl shadow-lg`}>
                    <Icon className="h-5 w-5 md:h-6 md:w-6 text-white" />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 card-grid-mobile">
          {/* Recent Sales */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mobile-card">
            <div className="p-4 md:p-6 border-b border-gray-200">
              <h2 className="text-base md:text-lg font-semibold text-gray-900">آخر المبيعات</h2>
            </div>
            <div className="p-4 md:p-6 mobile-content">
              <div className="space-y-3 md:space-y-4">
                {recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2 md:gap-3">
                      <div className="bg-green-100 p-2 rounded-lg">
                        <ShoppingCart className="h-3 w-3 md:h-4 md:w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-sm md:text-base">فاتورة #{sale.id}</p>
                        <p className="text-xs md:text-sm text-gray-500">{sale.customer}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900 text-sm md:text-base">{formatCurrency(sale.amount)}</p>
                      <p className="text-xs md:text-sm text-gray-500">{sale.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Expiring Medicines */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mobile-card">
            <div className="p-4 md:p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 text-orange-500" />
                <h2 className="text-base md:text-lg font-semibold text-gray-900">أدوية قاربت على الانتهاء</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {expiringMedicines.map((medicine, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="bg-orange-100 p-2 rounded-lg">
                        <Pill className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{medicine.name}</p>
                        <p className="text-sm text-gray-500">وجبة: {medicine.batch}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-orange-600">{medicine.expiry}</p>
                      <p className="text-sm text-gray-500">{medicine.quantity} قطعة</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
    </ProtectedRoute>
  )
}
