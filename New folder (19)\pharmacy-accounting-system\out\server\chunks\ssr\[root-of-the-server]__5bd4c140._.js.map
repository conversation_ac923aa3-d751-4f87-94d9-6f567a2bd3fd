{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { usePermissions } from '@/contexts/AuthContext'\nimport {\n  Home,\n  ShoppingCart,\n  Package,\n  Users,\n  UserCheck,\n  RotateCcw,\n  BarChart3,\n  Settings,\n  Pill,\n  FileText,\n  Wallet,\n  Shield,\n  Activity,\n  Bell,\n  Wrench,\n  Printer,\n  Bug\n} from 'lucide-react'\n\ninterface MenuItem {\n  title: string\n  href: string\n  icon: any\n  permission?: string\n  requireAny?: string[]\n}\n\nconst getMenuItems = (permissions: any): MenuItem[] => [\n  {\n    title: 'الرئيسية',\n    href: '/',\n    icon: Home\n  },\n  {\n    title: 'إدارة المخزون',\n    href: '/inventory',\n    icon: Package,\n    permission: 'inventory_view'\n  },\n  {\n    title: 'المبيعات',\n    href: '/sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  },\n\n  {\n    title: 'المشتريات',\n    href: '/purchases',\n    icon: Pill,\n    permission: 'purchases_view'\n  },\n\n  {\n    title: 'العملاء',\n    href: '/customers',\n    icon: Users,\n    permission: 'customers_view'\n  },\n  {\n    title: 'الموردين',\n    href: '/suppliers',\n    icon: UserCheck,\n    permission: 'suppliers_view'\n  },\n  {\n    title: 'المرتجعات',\n    href: '/returns',\n    icon: RotateCcw,\n    permission: 'returns_view'\n  },\n\n  {\n    title: 'الصندوق',\n    href: '/cashbox',\n    icon: Wallet,\n    permission: 'cashbox_view'\n  },\n  {\n    title: 'التقارير',\n    href: '/reports',\n    icon: BarChart3,\n    permission: 'reports_view'\n  },\n  {\n    title: 'إدارة المستخدمين',\n    href: '/users',\n    icon: Shield,\n    permission: 'users_view'\n  },\n  {\n    title: 'سجل النشاطات',\n    href: '/activity-log',\n    icon: Activity,\n    permission: 'users_view'\n  },\n  {\n    title: 'التنبيهات',\n    href: '/notifications',\n    icon: Bell\n  },\n  {\n    title: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    permission: 'settings_view'\n  },\n  {\n    title: 'إصلاح البيانات',\n    href: '/fix-data',\n    icon: Wrench,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار الطباعة',\n    href: '/test-print',\n    icon: Printer,\n    permission: 'sales_view'\n  },\n  {\n    title: 'تشخيص البيانات',\n    href: '/debug-data',\n    icon: Bug,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار المبيعات',\n    href: '/debug-sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  }\n]\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nexport default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {\n  const pathname = usePathname()\n  const { hasPermission, permissions } = usePermissions()\n\n  console.log('🔧 Sidebar render - isOpen:', isOpen, 'right position:', isOpen ? '0px' : '-300px')\n\n  const menuItems = getMenuItems(permissions)\n\n  // إغلاق القائمة عند تغيير الصفحة\n  useEffect(() => {\n    if (onClose) {\n      onClose()\n    }\n  }, [pathname, onClose])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.getElementById('mobile-sidebar')\n      const menuButton = document.getElementById('mobile-menu-button')\n\n      if (sidebar && !sidebar.contains(event.target as Node) &&\n          menuButton && !menuButton.contains(event.target as Node)) {\n        if (onClose) {\n          onClose()\n        }\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  // تصفية العناصر بناءً على الصلاحيات\n  const visibleMenuItems = menuItems.filter(item => {\n    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)\n    return hasPermission(item.permission as any)\n  })\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar - نسخة جديدة تعمل بالتأكيد */}\n      <div\n        id=\"mobile-sidebar\"\n        style={{\n          position: 'fixed',\n          top: 0,\n          right: isOpen ? '0px' : '-300px',\n          height: '100vh',\n          width: '280px',\n          backgroundColor: 'white',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          borderLeft: '1px solid #e5e7eb',\n          transition: 'right 0.3s ease-in-out',\n          zIndex: 9999,\n          overflow: 'auto'\n        }}\n      >\n        <div style={{ padding: '24px', height: '100%', overflowY: 'auto' }}>\n          <div style={{\n            padding: '16px',\n            backgroundColor: '#3b82f6',\n            borderRadius: '12px',\n            color: 'white',\n            marginBottom: '24px',\n            textAlign: 'center'\n          }}>\n            <h1 style={{ fontSize: '18px', fontWeight: 'bold', margin: 0 }}>✅ الشريط الجانبي يعمل!</h1>\n            <p style={{ fontSize: '14px', margin: '8px 0 0 0' }}>نظام الصيدلية - مكتب لارين العلمي</p>\n          </div>\n\n          <div style={{\n            padding: '16px',\n            backgroundColor: '#10b981',\n            borderRadius: '8px',\n            color: 'white',\n            marginBottom: '16px',\n            textAlign: 'center'\n          }}>\n            <p style={{ margin: 0, fontWeight: 'bold' }}>🎉 تم إصلاح المشكلة!</p>\n            <p style={{ margin: '8px 0 0 0', fontSize: '12px' }}>الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>\n          </div>\n\n          <div style={{ marginBottom: '16px' }}>\n            <button\n              onClick={onClose}\n              style={{\n                width: '100%',\n                padding: '12px',\n                backgroundColor: '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                fontSize: '16px',\n                fontWeight: 'bold',\n                cursor: 'pointer'\n              }}\n            >\n              إغلاق الشريط الجانبي\n            </button>\n          </div>\n\n          <div style={{\n            padding: '16px',\n            backgroundColor: '#f3f4f6',\n            borderRadius: '8px',\n            marginBottom: '16px'\n          }}>\n            <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: 'bold' }}>القائمة الرئيسية:</h3>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              <a href=\"/sales\" style={{\n                padding: '8px 12px',\n                backgroundColor: '#3b82f6',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '6px',\n                display: 'block',\n                textAlign: 'center'\n              }}>المبيعات</a>\n              <a href=\"/purchases\" style={{\n                padding: '8px 12px',\n                backgroundColor: '#3b82f6',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '6px',\n                display: 'block',\n                textAlign: 'center'\n              }}>المشتريات</a>\n              <a href=\"/inventory\" style={{\n                padding: '8px 12px',\n                backgroundColor: '#3b82f6',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '6px',\n                display: 'block',\n                textAlign: 'center'\n              }}>المخزون</a>\n            </div>\n          </div>\n\n        </div>\n      </div>\n\n      {/* Overlay */}\n      {isOpen && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 9998\n          }}\n          onClick={onClose}\n        />\n      )}\n    </>\n  )\n}\n\n// تصدير دالة للتحكم في القائمة من مكونات أخرى\nexport const useSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAkCA,MAAM,eAAe,CAAC,cAAiC;QACrD;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sNAAA,CAAA,eAAY;YAClB,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,gMAAA,CAAA,MAAG;YACT,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,sNAAA,CAAA,eAAY;YAClB,YAAY;QACd;KACD;AAOc,SAAS,QAAQ,EAAE,SAAS,KAAK,EAAE,OAAO,EAAgB,GAAG,CAAC,CAAC;IAC5E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEpD,QAAQ,GAAG,CAAC,+BAA+B,QAAQ,mBAAmB,SAAS,QAAQ;IAEvF,MAAM,YAAY,aAAa;IAE/B,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,MAAM,aAAa,SAAS,cAAc,CAAC;YAE3C,IAAI,WAAW,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KACzC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5D,IAAI,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,oCAAoC;IACpC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,KAAK,6CAA6C;;QAC/E,OAAO,cAAc,KAAK,UAAU;IACtC;IAEA,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBACC,IAAG;gBACH,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,OAAO,SAAS,QAAQ;oBACxB,QAAQ;oBACR,OAAO;oBACP,iBAAiB;oBACjB,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,UAAU;gBACZ;0BAEA,cAAA,8OAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,QAAQ;wBAAQ,WAAW;oBAAO;;sCAC/D,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,iBAAiB;gCACjB,cAAc;gCACd,OAAO;gCACP,cAAc;gCACd,WAAW;4BACb;;8CACE,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAQ,YAAY;wCAAQ,QAAQ;oCAAE;8CAAG;;;;;;8CAChE,8OAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAQ,QAAQ;oCAAY;8CAAG;;;;;;;;;;;;sCAGvD,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,iBAAiB;gCACjB,cAAc;gCACd,OAAO;gCACP,cAAc;gCACd,WAAW;4BACb;;8CACE,8OAAC;oCAAE,OAAO;wCAAE,QAAQ;wCAAG,YAAY;oCAAO;8CAAG;;;;;;8CAC7C,8OAAC;oCAAE,OAAO;wCAAE,QAAQ;wCAAa,UAAU;oCAAO;;wCAAG;wCAAS,SAAS,UAAU;;;;;;;;;;;;;sCAGnF,8OAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;sCACjC,cAAA,8OAAC;gCACC,SAAS;gCACT,OAAO;oCACL,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,UAAU;oCACV,YAAY;oCACZ,QAAQ;gCACV;0CACD;;;;;;;;;;;sCAKH,8OAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,iBAAiB;gCACjB,cAAc;gCACd,cAAc;4BAChB;;8CACE,8OAAC;oCAAG,OAAO;wCAAE,QAAQ;wCAAc,UAAU;wCAAQ,YAAY;oCAAO;8CAAG;;;;;;8CAC3E,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,KAAK;oCAAM;;sDACjE,8OAAC;4CAAE,MAAK;4CAAS,OAAO;gDACtB,SAAS;gDACT,iBAAiB;gDACjB,OAAO;gDACP,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,WAAW;4CACb;sDAAG;;;;;;sDACH,8OAAC;4CAAE,MAAK;4CAAa,OAAO;gDAC1B,SAAS;gDACT,iBAAiB;gDACjB,OAAO;gDACP,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,WAAW;4CACb;sDAAG;;;;;;sDACH,8OAAC;4CAAE,MAAK;4CAAa,OAAO;gDAC1B,SAAS;gDACT,iBAAiB;gDACjB,OAAO;gDACP,gBAAgB;gDAChB,cAAc;gDACd,SAAS;gDACT,WAAW;4CACb;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQV,wBACC,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;gBACA,SAAS;;;;;;;;AAKnB;AAGO,MAAM,aAAa;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO;QAAE;QAAQ;QAAW,eAAe,IAAM,UAAU,CAAC;IAAQ;AACtE", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  X,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign\n} from 'lucide-react'\n\nexport default function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll\n  } = useNotifications()\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const filteredNotifications = activeTab === 'unread' \n    ? notifications.filter(n => !n.isRead)\n    : notifications\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-4 w-4 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-4 w-4 text-blue-500\" />\n    \n    // أيقونات حسب الفئة\n    if (category === 'inventory') return <Package className=\"h-4 w-4 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-4 w-4 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-4 w-4 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-4 w-4 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-4 w-4 text-gray-500\" />\n    \n    return <Bell className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'border-r-4 border-red-500 bg-red-50'\n      case 'high': return 'border-r-4 border-orange-500 bg-orange-50'\n      case 'medium': return 'border-r-4 border-yellow-500 bg-yellow-50'\n      case 'low': return 'border-r-4 border-blue-500 bg-blue-50'\n      default: return 'border-r-4 border-gray-500 bg-gray-50'\n    }\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n      setIsOpen(false)\n    }\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Bell className=\"h-5 w-5\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">التنبيهات</h3>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n            \n            {/* Tabs */}\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('all')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'all'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                الكل ({notifications.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('unread')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'unread'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                غير مقروءة ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Actions */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800\"\n                    >\n                      <CheckCheck className=\"h-3 w-3\" />\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n                <button\n                  onClick={clearAll}\n                  className=\"flex items-center gap-1 text-xs text-red-600 hover:text-red-800\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  مسح الكل\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500\">\n                  {activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                      !notification.isRead ? getPriorityColor(notification.priority) : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                              {notification.message}\n                            </p>\n                            \n                            {/* Action Button */}\n                            {notification.actionUrl && (\n                              <button className=\"inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2\">\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض التفاصيل'}\n                              </button>\n                            )}\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-1 mr-2\">\n                            {!notification.isRead && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation()\n                                  markAsRead(notification.id)\n                                }}\n                                className=\"p-1 text-gray-400 hover:text-blue-600\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                removeNotification(notification.id)\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-600\"\n                              title=\"حذف\"\n                            >\n                              <X className=\"h-3 w-3\" />\n                            </button>\n                          </div>\n                        </div>\n                        \n                        {/* Time */}\n                        <div className=\"flex items-center gap-1 mt-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(notification.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  router.push('/notifications')\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع التنبيهات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,wBAAwB,cAAc,WACxC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IACnC;IAEJ,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,oBAAoB;QACpB,IAAI,aAAa,aAAa,qBAAO,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC;QAE3D,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC;QAEtD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC;IAChC;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;YAClC,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,mBACb,8OAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,6DAA6D,EACvE,cAAc,QACV,8BACA,qCACJ;;4CACH;4CACQ,cAAc,MAAM;4CAAC;;;;;;;kDAE9B,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,6DAA6D,EACvE,cAAc,WACV,8BACA,qCACJ;;4CACH;4CACc;4CAAY;;;;;;;;;;;;;;;;;;;oBAM9B,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,cAAc,mBACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAQtC,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CACV,cAAc,WAAW,+BAA+B;;;;;;;;;;;iDAI7D,8OAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC;oCAEC,WAAW,CAAC,sDAAsD,EAChE,CAAC,aAAa,MAAM,GAAG,iBAAiB,aAAa,QAAQ,IAAI,IACjE;oCACF,SAAS,IAAM,wBAAwB;8CAEvC,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,MAAM,GAAG,kBAAkB,iBACzC;kFACC,aAAa,KAAK;;;;;;kFAErB,8OAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;oEAItB,aAAa,SAAS,kBACrB,8OAAC;wEAAO,WAAU;;0FAChB,8OAAC,sNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,aAAa,WAAW,IAAI;;;;;;;;;;;;;0EAMnC,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,WAAW,aAAa,EAAE;wEAC5B;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAGrB,8OAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;;;;;;;mCAhEtC,aAAa,EAAE;;;;;;;;;;;;;;;oBA2E7B,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,OAAO,IAAI,CAAC;gCACZ,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileMenuButton.tsx"], "sourcesContent": ["'use client'\n\nimport { Menu, X } from 'lucide-react'\n\ninterface MobileMenuButtonProps {\n  isOpen: boolean\n  onClick: () => void\n}\n\nexport default function MobileMenuButton({ isOpen, onClick }: MobileMenuButtonProps) {\n  const handleClick = () => {\n    console.log('🔘 MobileMenuButton clicked! isOpen:', isOpen)\n    onClick()\n  }\n\n  return (\n    <button\n      id=\"mobile-menu-button\"\n      onClick={handleClick}\n      className=\"p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center border-2 border-green-500 bg-green-200\"\n      aria-label={isOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n    >\n      {isOpen ? (\n        <X className=\"h-6 w-6\" />\n      ) : (\n        <Menu className=\"h-6 w-6\" />\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AASe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAyB;IACjF,MAAM,cAAc;QAClB,QAAQ,GAAG,CAAC,wCAAwC;QACpD;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,SAAS;QACT,WAAU;QACV,cAAY,SAAS,kBAAkB;kBAEtC,uBACC,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;iCAEb,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport NotificationDropdown from './NotificationDropdown'\nimport MobileMenuButton from './MobileMenuButton'\nimport {\n  Search,\n  User,\n  LogOut,\n  Settings,\n  Shield,\n  ChevronDown,\n  Activity\n} from 'lucide-react'\n\ninterface HeaderProps {\n  onMobileMenuToggle?: () => void\n  isMobileMenuOpen?: boolean\n}\n\nexport default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, logout } = useAuth()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    const roleNames: Record<string, string> = {\n      admin: 'مدير النظام',\n      manager: 'مدير',\n      pharmacist: 'صيدلي',\n      cashier: 'كاشير',\n      viewer: 'مشاهد'\n    }\n    return roleNames[role] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors: Record<string, string> = {\n      admin: 'bg-red-600',\n      manager: 'bg-purple-600',\n      pharmacist: 'bg-blue-600',\n      cashier: 'bg-green-600',\n      viewer: 'bg-gray-600'\n    }\n    return colors[role] || 'bg-gray-600'\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-40 header-mobile\">\n      <div className=\"flex items-center justify-between h-full px-3 md:px-6\">\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Mobile Menu Button */}\n          <MobileMenuButton\n            isOpen={isMobileMenuOpen}\n            onClick={onMobileMenuToggle || (() => console.log('⚠️ No onMobileMenuToggle function provided!'))}\n          />\n\n          {/* Mobile Search - Hidden on very small screens */}\n          <div className=\"relative hidden sm:hidden md:block\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث...\"\n              className=\"pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80 form-mobile\"\n            />\n          </div>\n\n          {/* Mobile Search Icon - Visible only on small screens */}\n          <button className=\"md:hidden p-2 hover:bg-gray-100 rounded-lg\">\n            <Search className=\"h-5 w-5 text-gray-600\" />\n          </button>\n        </div>\n\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Notifications */}\n          <NotificationDropdown />\n\n          {/* User Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center gap-2 md:gap-3 border-r border-gray-200 pr-2 md:pr-4 hover:bg-gray-50 rounded-lg p-1 md:p-2 transition-colors min-h-[44px]\"\n            >\n              {/* Hide user info text on mobile */}\n              <div className=\"text-right hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-800\">{user?.full_name || 'مستخدم'}</p>\n                <p className=\"text-xs text-gray-500\">{user ? getRoleDisplayName(user.role) : ''}</p>\n              </div>\n              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <ChevronDown className=\"h-4 w-4 text-gray-400 hidden md:block\" />\n            </button>\n\n            {/* Dropdown Menu */}\n            {showUserMenu && (\n              <div className=\"absolute left-0 md:left-0 mt-2 w-64 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 modal-mobile md:relative md:modal-content-mobile\">\n                {/* User Info */}\n                <div className=\"px-4 py-3 border-b border-gray-100\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                      <p className=\"text-xs text-gray-500\">@{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :\n                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :\n                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :\n                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      <Shield className=\"h-3 w-3 ml-1\" />\n                      {user ? getRoleDisplayName(user.role) : ''}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/profile')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    الملف الشخصي\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/activity-log')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Activity className=\"h-4 w-4\" />\n                    سجل النشاطات\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/settings')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    الإعدادات\n                  </button>\n                </div>\n\n                {/* Logout */}\n                <div className=\"border-t border-gray-100 pt-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      handleLogout()\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAsBe,SAAS,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,KAAK,EAAe,GAAG,CAAC,CAAC;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAoC;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,sIAAA,CAAA,UAAgB;gCACf,QAAQ;gCACR,SAAS,sBAAsB,CAAC,IAAM,QAAQ,GAAG,CAAC,8CAA8C;;;;;;0CAIlG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0IAAA,CAAA,UAAoB;;;;;0CAGrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,MAAM,aAAa;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;0DAE/E,8OAAC;gDAAI,WAAW,GAAG,OAAO,aAAa,KAAK,IAAI,IAAI,cAAc,iBAAiB,CAAC;0DAClF,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,GAAG,OAAO,aAAa,KAAK,IAAI,IAAI,cAAc,iBAAiB,CAAC;0EAClF,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAqC,MAAM;;;;;;kFACxD,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAE,MAAM;;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;kFAAyB,MAAM;;;;;;;;;;;;;;;;;;kEAGhD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAW,CAAC,oEAAoE,EACpF,MAAM,SAAS,UAAU,4BACzB,MAAM,SAAS,YAAY,kCAC3B,MAAM,SAAS,eAAe,8BAC9B,MAAM,SAAS,YAAY,gCAC3B,6BACA;;8EACA,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;0DAM9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAI9B,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIlC,8OAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB;oDACF;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,8BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <AlertTriangle className=\"h-12 w-12 text-red-500\" />\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 mb-2\">حدث خطأ غير متوقع</h1>\n            \n            <p className=\"text-gray-600 mb-4\">\n              عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.\n            </p>\n            \n            {this.state.error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left\">\n                <p className=\"text-sm text-red-800 font-mono\">\n                  {this.state.error.message}\n                </p>\n              </div>\n            )}\n            \n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={this.retry}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                إعادة المحاولة\n              </button>\n              \n              <button\n                onClick={() => window.location.reload()}\n                className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\n              >\n                إعادة تحميل الصفحة\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  تفاصيل الخطأ (للمطورين)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                  {this.state.error?.stack}\n                  {'\\n\\n'}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for functional components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null)\n\n  const resetError = React.useCallback(() => {\n    setError(null)\n  }, [])\n\n  const handleError = React.useCallback((error: Error) => {\n    console.error('Error caught by useErrorHandler:', error)\n    setError(error)\n  }, [])\n\n  React.useEffect(() => {\n    if (error) {\n      throw error\n    }\n  }, [error])\n\n  return { handleError, resetError }\n}\n\n// Safe component wrapper\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAHA;;;;AAgBA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAEA,QAAQ;QACN,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAIjC,IAAI,CAAC,KAAK,CAAC,KAAK,kBACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAI,CAAC,KAAK;oCACnB,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,8OAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC7D,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;;wCACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;wCAClB;wCACA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QACnC,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACrC,QAAQ,KAAK,CAAC,oCAAoC;QAClD,SAAS;IACX,GAAG,EAAE;IAEL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,OAAO;YACT,MAAM;QACR;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC;AAGO,SAAS,kBACd,SAAiC,EACjC,QAAmE;IAEnE,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAc,UAAU;sBACvB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ToastNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  X,\n  CheckCircle,\n  AlertTriangle,\n  Info,\n  XCircle,\n  ExternalLink\n} from 'lucide-react'\n\ninterface ToastNotification {\n  id: string\n  type: 'success' | 'warning' | 'error' | 'info'\n  title: string\n  message: string\n  actionUrl?: string\n  actionLabel?: string\n  duration?: number\n}\n\nexport default function ToastNotifications() {\n  const [toasts, setToasts] = useState<ToastNotification[]>([])\n  const { notifications } = useNotifications()\n\n  // مراقبة التنبيهات الجديدة وعرضها كـ Toast\n  useEffect(() => {\n    const latestNotification = notifications[0]\n    if (latestNotification && !latestNotification.isRead) {\n      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)\n      const notificationTime = new Date(latestNotification.createdAt).getTime()\n      const now = new Date().getTime()\n      const diffInMinutes = (now - notificationTime) / (1000 * 60)\n      \n      if (diffInMinutes < 1) {\n        showToast({\n          id: latestNotification.id,\n          type: latestNotification.type,\n          title: latestNotification.title,\n          message: latestNotification.message,\n          actionUrl: latestNotification.actionUrl,\n          actionLabel: latestNotification.actionLabel,\n          duration: getDurationByPriority(latestNotification.priority)\n        })\n      }\n    }\n  }, [notifications])\n\n  const getDurationByPriority = (priority: string): number => {\n    switch (priority) {\n      case 'critical': return 10000 // 10 ثواني\n      case 'high': return 7000     // 7 ثواني\n      case 'medium': return 5000   // 5 ثواني\n      case 'low': return 3000      // 3 ثواني\n      default: return 5000\n    }\n  }\n\n  const showToast = (toast: ToastNotification) => {\n    // تجنب التكرار\n    if (toasts.find(t => t.id === toast.id)) return\n\n    setToasts(prev => [...prev, toast])\n\n    // إزالة التنبيه تلقائياً بعد المدة المحددة\n    setTimeout(() => {\n      removeToast(toast.id)\n    }, toast.duration || 5000)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const getToastIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getToastStyles = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-20 left-4 md:left-72 z-[60] space-y-3 max-w-sm\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className={`w-full shadow-lg rounded-lg border p-3 md:p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}\n        >\n          <div className=\"flex items-start gap-2 md:gap-3\">\n            {/* Icon */}\n            <div className=\"flex-shrink-0 mt-0.5\">\n              {getToastIcon(toast.type)}\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 min-w-0\">\n              <h4 className=\"text-xs md:text-sm font-medium mb-1\">\n                {toast.title}\n              </h4>\n              <p className=\"text-xs md:text-sm opacity-90 line-clamp-2\">\n                {toast.message}\n              </p>\n\n              {/* Action Button */}\n              {toast.actionUrl && (\n                <a\n                  href={toast.actionUrl}\n                  className=\"inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline min-h-[32px]\"\n                >\n                  <ExternalLink className=\"h-3 w-3\" />\n                  {toast.actionLabel || 'عرض التفاصيل'}\n                </a>\n              )}\n            </div>\n\n            {/* Close Button */}\n            <button\n              onClick={() => removeToast(toast.id)}\n              className=\"flex-shrink-0 p-2 hover:bg-black hover:bg-opacity-10 rounded min-h-[44px] min-w-[44px] flex items-center justify-center\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// إضافة الأنيميشن إلى CSS\nconst toastStyles = `\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`\n\n// إضافة الأنيميشن إلى الصفحة\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style')\n  styleElement.textContent = toastStyles\n  document.head.appendChild(styleElement)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAuBe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,aAAa,CAAC,EAAE;QAC3C,IAAI,sBAAsB,CAAC,mBAAmB,MAAM,EAAE;YACpD,uDAAuD;YACvD,MAAM,mBAAmB,IAAI,KAAK,mBAAmB,SAAS,EAAE,OAAO;YACvE,MAAM,MAAM,IAAI,OAAO,OAAO;YAC9B,MAAM,gBAAgB,CAAC,MAAM,gBAAgB,IAAI,CAAC,OAAO,EAAE;YAE3D,IAAI,gBAAgB,GAAG;gBACrB,UAAU;oBACR,IAAI,mBAAmB,EAAE;oBACzB,MAAM,mBAAmB,IAAI;oBAC7B,OAAO,mBAAmB,KAAK;oBAC/B,SAAS,mBAAmB,OAAO;oBACnC,WAAW,mBAAmB,SAAS;oBACvC,aAAa,mBAAmB,WAAW;oBAC3C,UAAU,sBAAsB,mBAAmB,QAAQ;gBAC7D;YACF;QACF;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAY,OAAO,MAAM,WAAW;;YACzC,KAAK;gBAAQ,OAAO,KAAS,UAAU;;YACvC,KAAK;gBAAU,OAAO,KAAO,UAAU;;YACvC,KAAK;gBAAO,OAAO,KAAU,UAAU;;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,eAAe;QACf,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;QAEzC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,2CAA2C;QAC3C,WAAW;YACT,YAAY,MAAM,EAAE;QACtB,GAAG,MAAM,QAAQ,IAAI;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAEC,WAAW,CAAC,0GAA0G,EAAE,eAAe,MAAM,IAAI,GAAG;0BAEpJ,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,IAAI;;;;;;sCAI1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;gCAIf,MAAM,SAAS,kBACd,8OAAC;oCACC,MAAM,MAAM,SAAS;oCACrB,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,MAAM,WAAW,IAAI;;;;;;;;;;;;;sCAM5B,8OAAC;4BACC,SAAS,IAAM,YAAY,MAAM,EAAE;4BACnC,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;eAnCZ,MAAM,EAAE;;;;;;;;;;AA0CvB;AAEA,0BAA0B;AAC1B,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;AAsBrB,CAAC;AAED,6BAA6B;AAC7B,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 1966, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileOptimizer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\nexport default function MobileOptimizer() {\n  useEffect(() => {\n    // Detect mobile device\n    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    if (isMobile || isTouch) {\n      // Add mobile-specific classes to body\n      document.body.classList.add('mobile-device', 'touch-device')\n      \n      // Prevent zoom on input focus (iOS)\n      const viewport = document.querySelector('meta[name=viewport]')\n      if (viewport) {\n        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover')\n      }\n\n      // Add touch-friendly classes to interactive elements\n      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')\n      interactiveElements.forEach(element => {\n        element.classList.add('touch-friendly')\n      })\n\n      // Optimize scrolling\n      document.documentElement.style.setProperty('-webkit-overflow-scrolling', 'touch')\n      document.body.style.setProperty('-webkit-overflow-scrolling', 'touch')\n\n      // Prevent bounce scrolling on iOS\n      document.addEventListener('touchmove', (e) => {\n        if (e.target === document.body) {\n          e.preventDefault()\n        }\n      }, { passive: false })\n\n      // Add safe area support\n      if (CSS.supports('padding: max(0px)')) {\n        document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')\n        document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')\n        document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')\n        document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')\n      }\n\n      // Optimize performance for mobile\n      const optimizeElement = (element: Element) => {\n        if (element instanceof HTMLElement) {\n          element.style.setProperty('transform', 'translateZ(0)')\n          element.style.setProperty('will-change', 'transform')\n        }\n      }\n\n      // Apply optimizations to animated elements\n      const animatedElements = document.querySelectorAll('.animate-spin, .animate-pulse, .hover-lift')\n      animatedElements.forEach(optimizeElement)\n\n      // Handle orientation change\n      const handleOrientationChange = () => {\n        // Force repaint after orientation change\n        setTimeout(() => {\n          window.scrollTo(0, window.scrollY)\n        }, 100)\n      }\n\n      window.addEventListener('orientationchange', handleOrientationChange)\n      \n      // Cleanup\n      return () => {\n        window.removeEventListener('orientationchange', handleOrientationChange)\n      }\n    }\n  }, [])\n\n  useEffect(() => {\n    // Add PWA install prompt handling\n    let deferredPrompt: any\n\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      deferredPrompt = e\n      \n      // Show custom install button or banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'block'\n      }\n    }\n\n    const handleAppInstalled = () => {\n      console.log('PWA was installed')\n      deferredPrompt = null\n      \n      // Hide install banner\n      const installBanner = document.getElementById('pwa-install-banner')\n      if (installBanner) {\n        installBanner.style.display = 'none'\n      }\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  return null // This component doesn't render anything\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uBAAuB;QACvB,MAAM,WAAW,iEAAiE,IAAI,CAAC,UAAU,SAAS;QAC1G,MAAM,UAAU,kBAAkB,UAAU,UAAU,cAAc,GAAG;QAEvE,IAAI,YAAY,SAAS;YACvB,sCAAsC;YACtC,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB;YAE7C,oCAAoC;YACpC,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,IAAI,UAAU;gBACZ,SAAS,YAAY,CAAC,WAAW;YACnC;YAEA,qDAAqD;YACrD,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;YACtD,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,QAAQ,SAAS,CAAC,GAAG,CAAC;YACxB;YAEA,qBAAqB;YACrB,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;YACzE,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,8BAA8B;YAE9D,kCAAkC;YAClC,SAAS,gBAAgB,CAAC,aAAa,CAAC;gBACtC,IAAI,EAAE,MAAM,KAAK,SAAS,IAAI,EAAE;oBAC9B,EAAE,cAAc;gBAClB;YACF,GAAG;gBAAE,SAAS;YAAM;YAEpB,wBAAwB;YACxB,IAAI,IAAI,QAAQ,CAAC,sBAAsB;gBACrC,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB;gBACpE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,4BAA4B;gBACvE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B;gBACrE,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,2BAA2B;YACxE;YAEA,kCAAkC;YAClC,MAAM,kBAAkB,CAAC;gBACvB,IAAI,mBAAmB,aAAa;oBAClC,QAAQ,KAAK,CAAC,WAAW,CAAC,aAAa;oBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,eAAe;gBAC3C;YACF;YAEA,2CAA2C;YAC3C,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC;YAEzB,4BAA4B;YAC5B,MAAM,0BAA0B;gBAC9B,yCAAyC;gBACzC,WAAW;oBACT,OAAO,QAAQ,CAAC,GAAG,OAAO,OAAO;gBACnC,GAAG;YACL;YAEA,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C,UAAU;YACV,OAAO;gBACL,OAAO,mBAAmB,CAAC,qBAAqB;YAClD;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI;QAEJ,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,iBAAiB;YAEjB,uCAAuC;YACvC,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,IAAI,eAAe;gBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;YAChC;QACF;QAEA,MAAM,qBAAqB;YACzB,QAAQ,GAAG,CAAC;YACZ,iBAAiB;YAEjB,sBAAsB;YACtB,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,IAAI,eAAe;gBACjB,cAAc,KAAK,CAAC,OAAO,GAAG;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,OAAO,KAAK,yCAAyC;;AACvD", "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/PWAInstallBanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Download, Smartphone } from 'lucide-react'\n\nexport default function PWAInstallBanner() {\n  const [showBanner, setShowBanner] = useState(false)\n  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)\n\n  useEffect(() => {\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault()\n      setDeferredPrompt(e)\n      setShowBanner(true)\n    }\n\n    const handleAppInstalled = () => {\n      setShowBanner(false)\n      setDeferredPrompt(null)\n    }\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n    window.addEventListener('appinstalled', handleAppInstalled)\n\n    // Check if app is already installed\n    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {\n      setShowBanner(false)\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)\n      window.removeEventListener('appinstalled', handleAppInstalled)\n    }\n  }, [])\n\n  const handleInstallClick = async () => {\n    if (deferredPrompt) {\n      deferredPrompt.prompt()\n      const { outcome } = await deferredPrompt.userChoice\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt')\n      } else {\n        console.log('User dismissed the install prompt')\n      }\n      \n      setDeferredPrompt(null)\n      setShowBanner(false)\n    }\n  }\n\n  const handleDismiss = () => {\n    setShowBanner(false)\n    // Remember user dismissed the banner\n    localStorage.setItem('pwa-install-dismissed', 'true')\n  }\n\n  // Don't show if user previously dismissed\n  useEffect(() => {\n    const dismissed = localStorage.getItem('pwa-install-dismissed')\n    if (dismissed === 'true') {\n      setShowBanner(false)\n    }\n  }, [])\n\n  if (!showBanner) return null\n\n  return (\n    <div \n      id=\"pwa-install-banner\"\n      className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg shadow-lg p-4 z-50 mobile-card\"\n    >\n      <div className=\"flex items-start gap-3\">\n        <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n          <Smartphone className=\"h-5 w-5\" />\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-sm md:text-base mb-1\">\n            تثبيت التطبيق\n          </h3>\n          <p className=\"text-blue-100 text-xs md:text-sm mb-3\">\n            ثبت نظام الصيدلية على هاتفك للوصول السريع والعمل بدون إنترنت\n          </p>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleInstallClick}\n              className=\"bg-white text-blue-600 px-3 py-1.5 rounded-md text-xs md:text-sm font-medium hover:bg-blue-50 transition-colors flex items-center gap-1\"\n            >\n              <Download className=\"h-3 w-3 md:h-4 md:w-4\" />\n              تثبيت\n            </button>\n            <button\n              onClick={handleDismiss}\n              className=\"text-blue-100 hover:text-white transition-colors\"\n            >\n              <X className=\"h-4 w-4 md:h-5 md:w-5\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,kBAAkB;YAClB,cAAc;QAChB;QAEA,MAAM,qBAAqB;YACzB,cAAc;YACd,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,oCAAoC;QACpC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;YAChF,cAAc;QAChB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,eAAe,MAAM;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,qCAAqC;QACrC,aAAa,OAAO,CAAC,yBAAyB;IAChD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,cAAc,QAAQ;YACxB,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY,OAAO;IAExB,qBACE,8OAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;8BAExB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAGhD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ScreenSizeIndicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Monitor, Tablet, Smartphone, Laptop } from 'lucide-react'\n\nexport default function ScreenSizeIndicator() {\n  const [screenInfo, setScreenInfo] = useState({\n    width: 0,\n    height: 0,\n    breakpoint: '',\n    device: '',\n    orientation: ''\n  })\n\n  useEffect(() => {\n    const updateScreenInfo = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n      \n      let breakpoint = ''\n      let device = ''\n      \n      if (width < 640) {\n        breakpoint = 'sm (< 640px)'\n        device = 'هاتف صغير'\n      } else if (width < 768) {\n        breakpoint = 'md (640px - 768px)'\n        device = 'هاتف كبير'\n      } else if (width < 1024) {\n        breakpoint = 'lg (768px - 1024px)'\n        device = 'تابلت'\n      } else if (width < 1280) {\n        breakpoint = 'xl (1024px - 1280px)'\n        device = 'لابتوب'\n      } else {\n        breakpoint = '2xl (> 1280px)'\n        device = 'كمبيوتر مكتبي'\n      }\n\n      const orientation = width > height ? 'أفقي' : 'عمودي'\n\n      setScreenInfo({\n        width,\n        height,\n        breakpoint,\n        device,\n        orientation\n      })\n    }\n\n    updateScreenInfo()\n    window.addEventListener('resize', updateScreenInfo)\n    window.addEventListener('orientationchange', updateScreenInfo)\n\n    return () => {\n      window.removeEventListener('resize', updateScreenInfo)\n      window.removeEventListener('orientationchange', updateScreenInfo)\n    }\n  }, [])\n\n  const getIcon = () => {\n    if (screenInfo.width < 768) return Smartphone\n    if (screenInfo.width < 1024) return Tablet\n    if (screenInfo.width < 1280) return Laptop\n    return Monitor\n  }\n\n  const Icon = getIcon()\n\n  return (\n    <div className=\"fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs z-50 hidden md:block\">\n      <div className=\"flex items-center gap-2 mb-1\">\n        <Icon className=\"h-4 w-4\" />\n        <span className=\"font-medium\">{screenInfo.device}</span>\n      </div>\n      <div className=\"space-y-1\">\n        <div>{screenInfo.width} × {screenInfo.height}</div>\n        <div>{screenInfo.breakpoint}</div>\n        <div>{screenInfo.orientation}</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,QAAQ;QACR,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,IAAI,aAAa;YACjB,IAAI,SAAS;YAEb,IAAI,QAAQ,KAAK;gBACf,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,KAAK;gBACtB,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,MAAM;gBACvB,aAAa;gBACb,SAAS;YACX,OAAO,IAAI,QAAQ,MAAM;gBACvB,aAAa;gBACb,SAAS;YACX,OAAO;gBACL,aAAa;gBACb,SAAS;YACX;YAEA,MAAM,cAAc,QAAQ,SAAS,SAAS;YAE9C,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,qBAAqB;QAE7C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,IAAI,WAAW,KAAK,GAAG,KAAK,OAAO,8MAAA,CAAA,aAAU;QAC7C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,sMAAA,CAAA,SAAM;QAC1C,IAAI,WAAW,KAAK,GAAG,MAAM,OAAO,sMAAA,CAAA,SAAM;QAC1C,OAAO,wMAAA,CAAA,UAAO;IAChB;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAe,WAAW,MAAM;;;;;;;;;;;;0BAElD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAK,WAAW,KAAK;4BAAC;4BAAI,WAAW,MAAM;;;;;;;kCAC5C,8OAAC;kCAAK,WAAW,UAAU;;;;;;kCAC3B,8OAAC;kCAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport Header from './Header'\nimport ErrorBoundary from './ErrorBoundary'\nimport ToastNotifications from './ToastNotifications'\nimport MobileOptimizer from './MobileOptimizer'\nimport PWAInstallBanner from './PWAInstallBanner'\nimport ScreenSizeIndicator from './ScreenSizeIndicator'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const toggleMobileMenu = () => {\n    console.log('🔄 AppLayout toggle - Current:', isMobileMenuOpen, '→ New:', !isMobileMenuOpen)\n    setIsMobileMenuOpen(!isMobileMenuOpen)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <MobileOptimizer />\n      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n      <Header\n        onMobileMenuToggle={toggleMobileMenu}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n      <main className={`\n        mr-0\n        mt-16\n        p-3 md:p-6\n        main-content-mobile\n        min-h-screen\n        w-full\n        max-w-full\n        overflow-x-hidden\n        safe-area-inset-bottom\n      `}>\n        <ErrorBoundary>\n          <div className=\"max-w-full overflow-x-auto container\">\n            {children}\n          </div>\n        </ErrorBoundary>\n      </main>\n      <ToastNotifications />\n      <PWAInstallBanner />\n      <ScreenSizeIndicator />\n\n      {/* Mobile overlay when sidebar is open */}\n      {isMobileMenuOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 no-tap-highlight\"\n          onClick={() => setIsMobileMenuOpen(false)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAee,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC,kCAAkC,kBAAkB,UAAU,CAAC;QAC3E,oBAAoB,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,6HAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;0BACtE,8OAAC,4HAAA,CAAA,UAAM;gBACL,oBAAoB;gBACpB,kBAAkB;;;;;;0BAEpB,8OAAC;gBAAK,WAAW,CAAC;;;;;;;;;;MAUlB,CAAC;0BACC,cAAA,8OAAC,mIAAA,CAAA,UAAa;8BACZ,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;0BAIP,8OAAC,wIAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,sIAAA,CAAA,UAAgB;;;;;0BACjB,8OAAC,yIAAA,CAAA,UAAmB;;;;;YAGnB,kCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 2488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth, usePermissions } from '@/contexts/AuthContext'\nimport { UserPermissions } from '@/lib/auth-database'\nimport { Loader2, Shield, AlertTriangle } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredPermissions?: (keyof UserPermissions)[]\n  requiredRole?: string\n  fallback?: React.ReactNode\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requiredPermissions = [], \n  requiredRole,\n  fallback \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, user } = useAuth()\n  const { hasPermission, hasAnyPermission } = usePermissions()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  // عرض شاشة التحميل\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">جاري التحقق من الصلاحيات...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // إعادة توجيه إذا لم يكن مسجل دخول\n  if (!isAuthenticated) {\n    return null\n  }\n\n  // التحقق من الدور المطلوب\n  if (requiredRole && user?.role !== requiredRole) {\n    return fallback || (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n          <AlertTriangle className=\"h-16 w-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            غير مصرح لك\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            هذه الصفحة مخصصة للمستخدمين من نوع: {requiredRole}\n          </p>\n          <button\n            onClick={() => router.back()}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            العودة\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  // التحقق من الصلاحيات المطلوبة\n  if (requiredPermissions.length > 0) {\n    const hasRequiredPermissions = hasAnyPermission(requiredPermissions)\n    \n    if (!hasRequiredPermissions) {\n      return fallback || (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n            <Shield className=\"h-16 w-16 text-red-500 mx-auto mb-4\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              صلاحيات غير كافية\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة\n            </p>\n            <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n              <p className=\"text-sm text-gray-700 font-medium mb-2\">الصلاحيات المطلوبة:</p>\n              <ul className=\"text-sm text-gray-600 space-y-1\">\n                {requiredPermissions.map((permission) => (\n                  <li key={permission} className=\"flex items-center gap-2\">\n                    <span className=\"w-2 h-2 bg-red-400 rounded-full\"></span>\n                    {translatePermission(permission)}\n                  </li>\n                ))}\n              </ul>\n            </div>\n            <button\n              onClick={() => router.back()}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              العودة\n            </button>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من صلاحية واحدة فقط\ninterface PermissionGuardProps {\n  permission: keyof UserPermissions\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function PermissionGuard({ \n  permission, \n  children, \n  fallback,\n  showMessage = false \n}: PermissionGuardProps) {\n  const { hasPermission } = usePermissions()\n\n  if (!hasPermission(permission)) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <p className=\"text-yellow-800 text-sm\">\n              ليس لديك صلاحية: {translatePermission(permission)}\n            </p>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من عدة صلاحيات\ninterface MultiPermissionGuardProps {\n  permissions: (keyof UserPermissions)[]\n  requireAll?: boolean // true = يحتاج جميع الصلاحيات، false = يحتاج واحدة على الأقل\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function MultiPermissionGuard({ \n  permissions, \n  requireAll = false,\n  children, \n  fallback,\n  showMessage = false \n}: MultiPermissionGuardProps) {\n  const { hasPermission, hasAnyPermission } = usePermissions()\n\n  const hasAccess = requireAll \n    ? permissions.every(permission => hasPermission(permission))\n    : hasAnyPermission(permissions)\n\n  if (!hasAccess) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <div>\n              <p className=\"text-yellow-800 text-sm font-medium\">\n                ليس لديك الصلاحيات المطلوبة\n              </p>\n              <p className=\"text-yellow-700 text-xs mt-1\">\n                {requireAll ? 'تحتاج جميع الصلاحيات التالية:' : 'تحتاج واحدة على الأقل من الصلاحيات التالية:'}\n              </p>\n              <ul className=\"text-xs text-yellow-600 mt-2 space-y-1\">\n                {permissions.map((permission) => (\n                  <li key={permission}>• {translatePermission(permission)}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// مكون للتحقق من الدور\ninterface RoleGuardProps {\n  role: string\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showMessage?: boolean\n}\n\nexport function RoleGuard({ \n  role, \n  children, \n  fallback,\n  showMessage = false \n}: RoleGuardProps) {\n  const { user } = useAuth()\n\n  if (user?.role !== role) {\n    if (showMessage) {\n      return (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n            <p className=\"text-yellow-800 text-sm\">\n              هذه الميزة مخصصة للمستخدمين من نوع: {translateRole(role)}\n            </p>\n          </div>\n        </div>\n      )\n    }\n    return fallback || null\n  }\n\n  return <>{children}</>\n}\n\n// دوال الترجمة\nconst translatePermission = (permission: keyof UserPermissions): string => {\n  const translations: Record<keyof UserPermissions, string> = {\n    sales_view: 'عرض المبيعات',\n    sales_create: 'إنشاء مبيعات',\n    sales_edit: 'تعديل المبيعات',\n    sales_delete: 'حذف المبيعات',\n    sales_print: 'طباعة المبيعات',\n    sales_view_prices: 'عرض الأسعار',\n    \n    purchases_view: 'عرض المشتريات',\n    purchases_create: 'إنشاء مشتريات',\n    purchases_edit: 'تعديل المشتريات',\n    purchases_delete: 'حذف المشتريات',\n    purchases_print: 'طباعة المشتريات',\n    \n    inventory_view: 'عرض المخزون',\n    inventory_create: 'إضافة للمخزون',\n    inventory_edit: 'تعديل المخزون',\n    inventory_delete: 'حذف من المخزون',\n    inventory_print: 'طباعة المخزون',\n    \n    customers_view: 'عرض العملاء',\n    customers_create: 'إضافة عملاء',\n    customers_edit: 'تعديل العملاء',\n    customers_delete: 'حذف العملاء',\n    \n    suppliers_view: 'عرض الموردين',\n    suppliers_create: 'إضافة موردين',\n    suppliers_edit: 'تعديل الموردين',\n    suppliers_delete: 'حذف الموردين',\n    \n    reports_view: 'عرض التقارير',\n    reports_financial: 'التقارير المالية',\n    reports_detailed: 'التقارير المفصلة',\n    reports_export: 'تصدير التقارير',\n    \n    users_view: 'عرض المستخدمين',\n    users_create: 'إضافة مستخدمين',\n    users_edit: 'تعديل المستخدمين',\n    users_delete: 'حذف المستخدمين',\n    \n    settings_view: 'عرض الإعدادات',\n    settings_edit: 'تعديل الإعدادات',\n    \n    cashbox_view: 'عرض الصندوق',\n    cashbox_manage: 'إدارة الصندوق',\n    \n    returns_view: 'عرض المرتجعات',\n    returns_create: 'إنشاء مرتجعات',\n    returns_edit: 'تعديل المرتجعات',\n    returns_delete: 'حذف المرتجعات',\n  }\n  \n  return translations[permission] || permission\n}\n\nconst translateRole = (role: string): string => {\n  const translations: Record<string, string> = {\n    admin: 'مدير النظام',\n    manager: 'مدير',\n    pharmacist: 'صيدلي',\n    cashier: 'كاشير',\n    viewer: 'مشاهد'\n  }\n  \n  return translations[role] || role\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AANA;;;;;;AAee,SAAS,eAAe,EACrC,QAAQ,EACR,sBAAsB,EAAE,EACxB,YAAY,EACZ,QAAQ,EACY;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,mBAAmB;IACnB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB,MAAM,SAAS,cAAc;QAC/C,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;;4BAAqB;4BACK;;;;;;;kCAEvC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,+BAA+B;IAC/B,IAAI,oBAAoB,MAAM,GAAG,GAAG;QAClC,MAAM,yBAAyB,iBAAiB;QAEhD,IAAI,CAAC,wBAAwB;YAC3B,OAAO,0BACL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;8CACX,oBAAoB,GAAG,CAAC,CAAC,2BACxB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAK,WAAU;;;;;;gDACf,oBAAoB;;2CAFd;;;;;;;;;;;;;;;;sCAOf,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;IACF;IAEA,qBAAO;kBAAG;;AACZ;AAUO,SAAS,gBAAgB,EAC9B,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACE;IACrB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEvC,IAAI,CAAC,cAAc,aAAa;QAC9B,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAE,WAAU;;gCAA0B;gCACnB,oBAAoB;;;;;;;;;;;;;;;;;;QAKhD;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;AAWO,SAAS,qBAAqB,EACnC,WAAW,EACX,aAAa,KAAK,EAClB,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACO;IAC1B,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEzD,MAAM,YAAY,aACd,YAAY,KAAK,CAAC,CAAA,aAAc,cAAc,eAC9C,iBAAiB;IAErB,IAAI,CAAC,WAAW;QACd,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAsC;;;;;;8CAGnD,8OAAC;oCAAE,WAAU;8CACV,aAAa,kCAAkC;;;;;;8CAElD,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;;gDAAoB;gDAAG,oBAAoB;;2CAAnC;;;;;;;;;;;;;;;;;;;;;;;;;;;QAOvB;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;AAUO,SAAS,UAAU,EACxB,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACJ;IACf,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,MAAM,SAAS,MAAM;QACvB,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAE,WAAU;;gCAA0B;gCACA,cAAc;;;;;;;;;;;;;;;;;;QAK7D;QACA,OAAO,YAAY;IACrB;IAEA,qBAAO;kBAAG;;AACZ;AAEA,eAAe;AACf,MAAM,sBAAsB,CAAC;IAC3B,MAAM,eAAsD;QAC1D,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,mBAAmB;QAEnB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QAEjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QAEjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,cAAc;QACd,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAEhB,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,cAAc;QAEd,eAAe;QACf,eAAe;QAEf,cAAc;QACd,gBAAgB;QAEhB,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,gBAAgB;IAClB;IAEA,OAAO,YAAY,CAAC,WAAW,IAAI;AACrC;AAEA,MAAM,gBAAgB,CAAC;IACrB,MAAM,eAAuC;QAC3C,OAAO;QACP,SAAS;QACT,YAAY;QACZ,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,YAAY,CAAC,KAAK,IAAI;AAC/B", "debugId": null}}, {"offset": {"line": 2937, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/utils/formatters.ts"], "sourcesContent": ["// Utility functions for consistent formatting across server and client\n\n/**\n * Format number with Arabic locale consistently\n * This prevents hydration errors by ensuring same output on server and client\n */\nexport function formatNumber(num: number): string {\n  if (typeof num !== 'number' || isNaN(num)) {\n    return '0'\n  }\n  \n  // Use a consistent format that works on both server and client\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\n/**\n * Format currency with Iraqi Dinar\n */\nexport function formatCurrency(amount: number): string {\n  return `${formatNumber(amount)} د.ع`\n}\n\n/**\n * Format date consistently\n */\nexport function formatDate(date: string | Date): string {\n  if (!date) return ''\n  \n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  if (isNaN(dateObj.getTime())) {\n    return ''\n  }\n  \n  // Use consistent date format\n  return dateObj.toLocaleDateString('en-GB') // DD/MM/YYYY format\n}\n\n/**\n * Format percentage\n */\nexport function formatPercentage(value: number): string {\n  if (typeof value !== 'number' || isNaN(value)) {\n    return '0%'\n  }\n  \n  return `${formatNumber(value)}%`\n}\n\n/**\n * Format phone number\n */\nexport function formatPhone(phone: string): string {\n  if (!phone) return ''\n  \n  // Remove any non-digit characters\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  // Format Iraqi phone numbers\n  if (cleaned.startsWith('964')) {\n    // International format\n    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`\n  } else if (cleaned.startsWith('07')) {\n    // Local format\n    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`\n  }\n  \n  return phone\n}\n\n/**\n * Truncate text with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (!text || text.length <= maxLength) {\n    return text\n  }\n  \n  return text.slice(0, maxLength) + '...'\n}\n\n/**\n * Format file size\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return `${formatNumber(parseFloat((bytes / Math.pow(k, i)).toFixed(2)))} ${sizes[i]}`\n}\n\n/**\n * Format time duration\n */\nexport function formatDuration(seconds: number): string {\n  if (seconds < 60) {\n    return `${Math.round(seconds)} ثانية`\n  } else if (seconds < 3600) {\n    return `${Math.round(seconds / 60)} دقيقة`\n  } else {\n    return `${Math.round(seconds / 3600)} ساعة`\n  }\n}\n\n/**\n * Format status text with proper Arabic\n */\nexport function formatStatus(status: string): string {\n  const statusMap: Record<string, string> = {\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'pending': 'معلق',\n    'approved': 'مقبول',\n    'rejected': 'مرفوض',\n    'paid': 'مدفوع',\n    'unpaid': 'غير مدفوع',\n    'partial': 'جزئي',\n    'completed': 'مكتمل',\n    'cancelled': 'ملغي',\n    'draft': 'مسودة',\n    'published': 'منشور'\n  }\n  \n  return statusMap[status] || status\n}\n\n/**\n * Format payment method\n */\nexport function formatPaymentMethod(method: string): string {\n  const methodMap: Record<string, string> = {\n    'cash': 'نقداً',\n    'credit': 'آجل',\n    'card': 'بطاقة',\n    'bank': 'تحويل بنكي',\n    'check': 'شيك'\n  }\n  \n  return methodMap[method] || method\n}\n\n/**\n * Safe number parsing\n */\nexport function parseNumber(value: string | number): number {\n  if (typeof value === 'number') {\n    return isNaN(value) ? 0 : value\n  }\n  \n  if (typeof value === 'string') {\n    const parsed = parseFloat(value.replace(/[^\\d.-]/g, ''))\n    return isNaN(parsed) ? 0 : parsed\n  }\n  \n  return 0\n}\n\n/**\n * Format quantity with unit\n */\nexport function formatQuantity(quantity: number, unit?: string): string {\n  const formattedQty = formatNumber(quantity)\n  return unit ? `${formattedQty} ${unit}` : formattedQty\n}\n\n/**\n * Format medicine batch code\n */\nexport function formatBatchCode(code: string): string {\n  if (!code) return 'غير محدد'\n  return code.toUpperCase()\n}\n\n/**\n * Format invoice number\n */\nexport function formatInvoiceNumber(number: string | number): string {\n  if (!number) return 'غير محدد'\n  return `#${number}`\n}\n\n/**\n * Calculate and format discount percentage\n */\nexport function formatDiscountPercentage(originalAmount: number, discountAmount: number): string {\n  if (originalAmount <= 0) return '0%'\n  \n  const percentage = (discountAmount / originalAmount) * 100\n  return formatPercentage(Math.round(percentage * 100) / 100)\n}\n\n/**\n * Format expiry status\n */\nexport function formatExpiryStatus(expiryDate: string): { status: string, color: string, text: string } {\n  if (!expiryDate) {\n    return { status: 'unknown', color: 'gray', text: 'غير محدد' }\n  }\n  \n  const today = new Date()\n  const expiry = new Date(expiryDate)\n  const diffTime = expiry.getTime() - today.getTime()\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  \n  if (diffDays < 0) {\n    return { status: 'expired', color: 'red', text: 'منتهي الصلاحية' }\n  } else if (diffDays <= 30) {\n    return { status: 'expiring', color: 'orange', text: 'ينتهي قريباً' }\n  } else if (diffDays <= 90) {\n    return { status: 'warning', color: 'yellow', text: 'تحذير' }\n  } else {\n    return { status: 'good', color: 'green', text: 'طبيعي' }\n  }\n}\n\n/**\n * Format stock status\n */\nexport function formatStockStatus(quantity: number, minStock: number = 10): { status: string, color: string, text: string } {\n  if (quantity <= 0) {\n    return { status: 'out', color: 'red', text: 'نفد المخزون' }\n  } else if (quantity <= minStock) {\n    return { status: 'low', color: 'orange', text: 'كمية قليلة' }\n  } else if (quantity <= minStock * 2) {\n    return { status: 'medium', color: 'yellow', text: 'كمية متوسطة' }\n  } else {\n    return { status: 'good', color: 'green', text: 'كمية جيدة' }\n  }\n}\n"], "names": [], "mappings": "AAAA,uEAAuE;AAEvE;;;CAGC;;;;;;;;;;;;;;;;;;;AACM,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,QAAQ,YAAY,MAAM,MAAM;QACzC,OAAO;IACT;IAEA,+DAA+D;IAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAKO,SAAS,eAAe,MAAc;IAC3C,OAAO,GAAG,aAAa,QAAQ,IAAI,CAAC;AACtC;AAKO,SAAS,WAAW,IAAmB;IAC5C,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,IAAI,MAAM,QAAQ,OAAO,KAAK;QAC5B,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,QAAQ,kBAAkB,CAAC,SAAS,oBAAoB;;AACjE;AAKO,SAAS,iBAAiB,KAAa;IAC5C,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ;QAC7C,OAAO;IACT;IAEA,OAAO,GAAG,aAAa,OAAO,CAAC,CAAC;AAClC;AAKO,SAAS,YAAY,KAAa;IACvC,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,6BAA6B;IAC7B,IAAI,QAAQ,UAAU,CAAC,QAAQ;QAC7B,uBAAuB;QACvB,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IACpG,OAAO,IAAI,QAAQ,UAAU,CAAC,OAAO;QACnC,eAAe;QACf,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW;QACrC,OAAO;IACT;IAEA,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,GAAG,aAAa,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;AACvF;AAKO,SAAS,eAAe,OAAe;IAC5C,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC;IACvC,OAAO,IAAI,UAAU,MAAM;QACzB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC;IAC5C,OAAO;QACL,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,MAAM,KAAK,CAAC;IAC7C;AACF;AAKO,SAAS,aAAa,MAAc;IACzC,MAAM,YAAoC;QACxC,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,SAAS;QACT,aAAa;IACf;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,oBAAoB,MAAc;IAChD,MAAM,YAAoC;QACxC,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,YAAY,KAAsB;IAChD,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,SAAS,IAAI;IAC5B;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,SAAS,WAAW,MAAM,OAAO,CAAC,YAAY;QACpD,OAAO,MAAM,UAAU,IAAI;IAC7B;IAEA,OAAO;AACT;AAKO,SAAS,eAAe,QAAgB,EAAE,IAAa;IAC5D,MAAM,eAAe,aAAa;IAClC,OAAO,OAAO,GAAG,aAAa,CAAC,EAAE,MAAM,GAAG;AAC5C;AAKO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KAAK,WAAW;AACzB;AAKO,SAAS,oBAAoB,MAAuB;IACzD,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,CAAC,CAAC,EAAE,QAAQ;AACrB;AAKO,SAAS,yBAAyB,cAAsB,EAAE,cAAsB;IACrF,IAAI,kBAAkB,GAAG,OAAO;IAEhC,MAAM,aAAa,AAAC,iBAAiB,iBAAkB;IACvD,OAAO,iBAAiB,KAAK,KAAK,CAAC,aAAa,OAAO;AACzD;AAKO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,QAAQ;YAAW,OAAO;YAAQ,MAAM;QAAW;IAC9D;IAEA,MAAM,QAAQ,IAAI;IAClB,MAAM,SAAS,IAAI,KAAK;IACxB,MAAM,WAAW,OAAO,OAAO,KAAK,MAAM,OAAO;IACjD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE1D,IAAI,WAAW,GAAG;QAChB,OAAO;YAAE,QAAQ;YAAW,OAAO;YAAO,MAAM;QAAiB;IACnE,OAAO,IAAI,YAAY,IAAI;QACzB,OAAO;YAAE,QAAQ;YAAY,OAAO;YAAU,MAAM;QAAe;IACrE,OAAO,IAAI,YAAY,IAAI;QACzB,OAAO;YAAE,QAAQ;YAAW,OAAO;YAAU,MAAM;QAAQ;IAC7D,OAAO;QACL,OAAO;YAAE,QAAQ;YAAQ,OAAO;YAAS,MAAM;QAAQ;IACzD;AACF;AAKO,SAAS,kBAAkB,QAAgB,EAAE,WAAmB,EAAE;IACvE,IAAI,YAAY,GAAG;QACjB,OAAO;YAAE,QAAQ;YAAO,OAAO;YAAO,MAAM;QAAc;IAC5D,OAAO,IAAI,YAAY,UAAU;QAC/B,OAAO;YAAE,QAAQ;YAAO,OAAO;YAAU,MAAM;QAAa;IAC9D,OAAO,IAAI,YAAY,WAAW,GAAG;QACnC,OAAO;YAAE,QAAQ;YAAU,OAAO;YAAU,MAAM;QAAc;IAClE,OAAO;QACL,OAAO;YAAE,QAAQ;YAAQ,OAAO;YAAS,MAAM;QAAY;IAC7D;AACF", "debugId": null}}, {"offset": {"line": 3150, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%286%29/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%285%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/app/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport AppLayout from '@/components/AppLayout'\r\nimport ProtectedRoute from '@/components/ProtectedRoute'\r\nimport { useAuth } from '@/contexts/AuthContext'\r\nimport {\r\n  ShoppingCart,\r\n  Package,\r\n  Users,\r\n  DollarSign,\r\n  TrendingUp,\r\n  AlertTriangle,\r\n  Calendar,\r\n  Pill,\r\n  Shield\r\n} from 'lucide-react'\r\nimport { formatCurrency, formatNumber } from '@/utils/formatters'\r\nimport { useState } from 'react'\r\nimport { Menu, X } from 'lucide-react'\r\n\r\nconst stats = [\r\n  {\r\n    title: 'إجمالي المبيعات اليوم',\r\n    value: formatNumber(2450000),\r\n    unit: 'د.ع',\r\n    change: '+12%',\r\n    changeType: 'positive' as const,\r\n    icon: DollarSign\r\n  },\r\n  {\r\n    title: 'عدد الفواتير',\r\n    value: formatNumber(156),\r\n    unit: 'فاتورة',\r\n    change: '+8%',\r\n    changeType: 'positive' as const,\r\n    icon: ShoppingCart\r\n  },\r\n  {\r\n    title: 'الأدوية المتوفرة',\r\n    value: formatNumber(1247),\r\n    unit: 'صنف',\r\n    change: '-2%',\r\n    changeType: 'negative' as const,\r\n    icon: Package\r\n  },\r\n  {\r\n    title: 'العملاء النشطين',\r\n    value: formatNumber(89),\r\n    unit: 'عميل',\r\n    change: '+5%',\r\n    changeType: 'positive' as const,\r\n    icon: Users\r\n  }\r\n]\r\n\r\nconst recentSales = [\r\n  { id: '001', customer: 'أحمد محمد', amount: 125000, time: '10:30 ص' },\r\n  { id: '002', customer: 'فاطمة علي', amount: 89000, time: '10:15 ص' },\r\n  { id: '003', customer: 'محمد حسن', amount: 156000, time: '09:45 ص' },\r\n  { id: '004', customer: 'سارة أحمد', amount: 67000, time: '09:30 ص' },\r\n  { id: '005', customer: 'علي محمود', amount: 234000, time: '09:15 ص' }\r\n]\r\n\r\nconst expiringMedicines = [\r\n  { name: 'باراسيتامول 500mg', batch: 'B001', expiry: '2024-08-15', quantity: 50 },\r\n  { name: 'أموكسيسيلين 250mg', batch: 'B002', expiry: '2024-08-20', quantity: 30 },\r\n  { name: 'إيبوبروفين 400mg', batch: 'B003', expiry: '2024-08-25', quantity: 25 }\r\n]\r\n\r\n// مكون شريط جانبي مبسط للاختبار\r\nfunction TestSidebarComponent() {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n\r\n  return (\r\n    <>\r\n      {/* زر فتح الشريط الجانبي - مثبت في الزاوية */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"fixed top-4 left-4 z-[100] bg-red-500 text-white p-3 rounded-full shadow-lg hover:bg-red-600 transition-colors\"\r\n        style={{ zIndex: 9999 }}\r\n      >\r\n        {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n      </button>\r\n\r\n      {/* زر اختبار الشريط الأصلي */}\r\n      <button\r\n        onClick={() => {\r\n          console.log('🔍 Testing original sidebar...')\r\n          const sidebar = document.getElementById('mobile-sidebar')\r\n          console.log('🔍 Original sidebar element:', sidebar)\r\n          if (sidebar) {\r\n            console.log('🔍 Current styles:', {\r\n              transform: sidebar.style.transform,\r\n              display: sidebar.style.display,\r\n              visibility: sidebar.style.visibility,\r\n              zIndex: sidebar.style.zIndex\r\n            })\r\n            console.log('🔍 Current classes:', sidebar.className)\r\n            console.log('🔍 Computed styles:', window.getComputedStyle(sidebar).transform)\r\n\r\n            // محاولة إظهار الشريط يدوياً\r\n            sidebar.style.transform = 'translateX(0)'\r\n            sidebar.style.zIndex = '9999'\r\n            sidebar.style.visibility = 'visible'\r\n            sidebar.style.display = 'block'\r\n            console.log('🔧 Manually set sidebar to visible')\r\n          } else {\r\n            console.log('❌ Original sidebar not found!')\r\n          }\r\n        }}\r\n        className=\"fixed top-20 left-4 z-[100] bg-purple-500 text-white p-3 rounded shadow-lg hover:bg-purple-600 transition-colors\"\r\n        style={{ zIndex: 9999 }}\r\n      >\r\n        اختبار الشريط الأصلي\r\n      </button>\r\n\r\n      {/* الشريط الجانبي */}\r\n      <div\r\n        className=\"fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[90] border-l-4 border-blue-500\"\r\n        style={{\r\n          transform: isOpen ? 'translateX(0)' : 'translateX(100%)',\r\n          transition: 'transform 0.3s ease-in-out',\r\n          zIndex: 9998\r\n        }}\r\n      >\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center justify-between mb-6 p-4 bg-blue-100 rounded-lg\">\r\n            <h2 className=\"text-xl font-bold text-blue-900\">الشريط الجانبي المبسط</h2>\r\n            <button\r\n              onClick={() => setIsOpen(false)}\r\n              className=\"p-2 hover:bg-blue-200 rounded-lg text-blue-700\"\r\n            >\r\n              <X className=\"h-5 w-5\" />\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"p-4 bg-green-100 rounded-lg border border-green-300\">\r\n              <p className=\"text-green-800 font-medium\">✅ الشريط الجانبي يعمل!</p>\r\n              <p className=\"text-green-600 text-sm mt-1\">الحالة: {isOpen ? 'مفتوح' : 'مغلق'}</p>\r\n            </div>\r\n\r\n            <div className=\"p-4 bg-blue-100 rounded-lg border border-blue-300\">\r\n              <p className=\"text-blue-800 font-medium\">📱 هذا شريط جانبي مبسط</p>\r\n              <p className=\"text-blue-600 text-sm mt-1\">يستخدم inline styles فقط</p>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => setIsOpen(false)}\r\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n              >\r\n                إغلاق الشريط\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => alert('الشريط الجانبي يعمل بشكل صحيح!')}\r\n                className=\"w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\r\n              >\r\n                اختبار التفاعل\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* خلفية مظلمة */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-[80]\"\r\n          onClick={() => setIsOpen(false)}\r\n          style={{ zIndex: 9997 }}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default function Home() {\r\n  const { user } = useAuth()\r\n\r\n  return (\r\n    <ProtectedRoute>\r\n      {/* شريط جانبي مبسط مباشر */}\r\n      <TestSidebarComponent />\r\n      <AppLayout>\r\n      <div className=\"space-y-6 md:space-y-8\">\r\n        {/* Welcome Header */}\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-4 md:p-8 text-white shadow-xl mobile-card\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h1 className=\"text-2xl md:text-4xl font-bold mb-2\">مرحباً بك، {user?.full_name || 'المستخدم'}</h1>\r\n              <p className=\"text-blue-100 text-sm md:text-lg mb-4\">نظام إدارة الصيدلية - مكتب لارين العلمي</p>\r\n              <div className=\"flex flex-col md:flex-row items-start md:items-center gap-3 md:gap-6\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Shield className=\"h-4 w-4 md:h-5 md:w-5 text-blue-200\" />\r\n                  <span className=\"text-blue-100 text-sm md:text-base\">{user?.role === 'admin' ? 'مدير النظام' : user?.role}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Calendar className=\"h-4 w-4 md:h-5 md:w-5 text-blue-200\" />\r\n                  <span className=\"text-blue-100 text-sm md:text-base\">{new Date().toLocaleDateString('ar-EG', {\r\n                    weekday: 'long',\r\n                    year: 'numeric',\r\n                    month: 'long',\r\n                    day: 'numeric'\r\n                  })}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"hidden md:block\">\r\n              <div className=\"bg-white bg-opacity-20 rounded-full p-6\">\r\n                <Pill className=\"h-16 w-16 text-white\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 card-grid-mobile\">\r\n          {stats.map((stat, index) => {\r\n            const Icon = stat.icon\r\n            const gradients = [\r\n              'from-blue-500 to-blue-600',\r\n              'from-green-500 to-green-600',\r\n              'from-purple-500 to-purple-600',\r\n              'from-orange-500 to-orange-600'\r\n            ]\r\n            return (\r\n              <div key={index} className=\"bg-white rounded-xl shadow-lg border border-gray-100 p-4 md:p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 mobile-card\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-xs md:text-sm font-medium text-gray-600 mb-2\">{stat.title}</p>\r\n                    <div className=\"flex items-baseline gap-2 mb-3\">\r\n                      <p className=\"text-2xl md:text-3xl font-bold text-gray-900\">{stat.value}</p>\r\n                      <span className=\"text-xs md:text-sm text-gray-500\">{stat.unit}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-1\">\r\n                      <TrendingUp className={`h-3 w-3 md:h-4 md:w-4 ${\r\n                        stat.changeType === 'positive' ? 'text-green-500' : 'text-red-500'\r\n                      }`} />\r\n                      <span className={`text-xs md:text-sm font-medium ${\r\n                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'\r\n                      }`}>\r\n                        {stat.change}\r\n                      </span>\r\n                      <span className=\"text-xs text-gray-500\">من الأمس</span>\r\n                    </div>\r\n                  </div>\r\n                  <div className={`bg-gradient-to-br ${gradients[index]} p-3 md:p-4 rounded-xl shadow-lg`}>\r\n                    <Icon className=\"h-5 w-5 md:h-6 md:w-6 text-white\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 card-grid-mobile\">\r\n          {/* Recent Sales */}\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mobile-card\">\r\n            <div className=\"p-4 md:p-6 border-b border-gray-200\">\r\n              <h2 className=\"text-base md:text-lg font-semibold text-gray-900\">آخر المبيعات</h2>\r\n            </div>\r\n            <div className=\"p-4 md:p-6 mobile-content\">\r\n              <div className=\"space-y-3 md:space-y-4\">\r\n                {recentSales.map((sale) => (\r\n                  <div key={sale.id} className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-2 md:gap-3\">\r\n                      <div className=\"bg-green-100 p-2 rounded-lg\">\r\n                        <ShoppingCart className=\"h-3 w-3 md:h-4 md:w-4 text-green-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"font-medium text-gray-900 text-sm md:text-base\">فاتورة #{sale.id}</p>\r\n                        <p className=\"text-xs md:text-sm text-gray-500\">{sale.customer}</p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-left\">\r\n                      <p className=\"font-medium text-gray-900 text-sm md:text-base\">{formatCurrency(sale.amount)}</p>\r\n                      <p className=\"text-xs md:text-sm text-gray-500\">{sale.time}</p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Expiring Medicines */}\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mobile-card\">\r\n            <div className=\"p-4 md:p-6 border-b border-gray-200\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <AlertTriangle className=\"h-4 w-4 md:h-5 md:w-5 text-orange-500\" />\r\n                <h2 className=\"text-base md:text-lg font-semibold text-gray-900\">أدوية قاربت على الانتهاء</h2>\r\n              </div>\r\n            </div>\r\n            <div className=\"p-6\">\r\n              <div className=\"space-y-4\">\r\n                {expiringMedicines.map((medicine, index) => (\r\n                  <div key={index} className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"bg-orange-100 p-2 rounded-lg\">\r\n                        <Pill className=\"h-4 w-4 text-orange-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"font-medium text-gray-900\">{medicine.name}</p>\r\n                        <p className=\"text-sm text-gray-500\">وجبة: {medicine.batch}</p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-left\">\r\n                      <p className=\"font-medium text-orange-600\">{medicine.expiry}</p>\r\n                      <p className=\"text-sm text-gray-500\">{medicine.quantity} قطعة</p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </AppLayout>\r\n    </ProtectedRoute>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAAA;AAlBA;;;;;;;;;AAoBA,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACpB,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,MAAM,kNAAA,CAAA,aAAU;IAClB;IACA;QACE,OAAO;QACP,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACpB,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,MAAM,sNAAA,CAAA,eAAY;IACpB;IACA;QACE,OAAO;QACP,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACpB,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,OAAO;QACP,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACpB,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,MAAM,oMAAA,CAAA,QAAK;IACb;CACD;AAED,MAAM,cAAc;IAClB;QAAE,IAAI;QAAO,UAAU;QAAa,QAAQ;QAAQ,MAAM;IAAU;IACpE;QAAE,IAAI;QAAO,UAAU;QAAa,QAAQ;QAAO,MAAM;IAAU;IACnE;QAAE,IAAI;QAAO,UAAU;QAAY,QAAQ;QAAQ,MAAM;IAAU;IACnE;QAAE,IAAI;QAAO,UAAU;QAAa,QAAQ;QAAO,MAAM;IAAU;IACnE;QAAE,IAAI;QAAO,UAAU;QAAa,QAAQ;QAAQ,MAAM;IAAU;CACrE;AAED,MAAM,oBAAoB;IACxB;QAAE,MAAM;QAAqB,OAAO;QAAQ,QAAQ;QAAc,UAAU;IAAG;IAC/E;QAAE,MAAM;QAAqB,OAAO;QAAQ,QAAQ;QAAc,UAAU;IAAG;IAC/E;QAAE,MAAM;QAAoB,OAAO;QAAQ,QAAQ;QAAc,UAAU;IAAG;CAC/E;AAED,gCAAgC;AAChC,SAAS;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAK;0BAErB,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;yCAAe,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAIxD,8OAAC;gBACC,SAAS;oBACP,QAAQ,GAAG,CAAC;oBACZ,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,QAAQ,GAAG,CAAC,gCAAgC;oBAC5C,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC,sBAAsB;4BAChC,WAAW,QAAQ,KAAK,CAAC,SAAS;4BAClC,SAAS,QAAQ,KAAK,CAAC,OAAO;4BAC9B,YAAY,QAAQ,KAAK,CAAC,UAAU;4BACpC,QAAQ,QAAQ,KAAK,CAAC,MAAM;wBAC9B;wBACA,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,SAAS;wBACpD,QAAQ,GAAG,CAAC,uBAAuB,OAAO,gBAAgB,CAAC,SAAS,SAAS;wBAE7E,6BAA6B;wBAC7B,QAAQ,KAAK,CAAC,SAAS,GAAG;wBAC1B,QAAQ,KAAK,CAAC,MAAM,GAAG;wBACvB,QAAQ,KAAK,CAAC,UAAU,GAAG;wBAC3B,QAAQ,KAAK,CAAC,OAAO,GAAG;wBACxB,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF;gBACA,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAK;0BACvB;;;;;;0BAKD,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WAAW,SAAS,kBAAkB;oBACtC,YAAY;oBACZ,QAAQ;gBACV;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;;gDAA8B;gDAAS,SAAS,UAAU;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAU;sDACX;;;;;;sDAID,8OAAC;4CACC,SAAS,IAAM,MAAM;4CACrB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;gBACzB,OAAO;oBAAE,QAAQ;gBAAK;;;;;;;;AAKhC;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC,oIAAA,CAAA,UAAc;;0BAEb,8OAAC;;;;;0BACD,8OAAC,+HAAA,CAAA,UAAS;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDAAsC;oDAAY,MAAM,aAAa;;;;;;;0DACnF,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAK,WAAU;0EAAsC,MAAM,SAAS,UAAU,gBAAgB,MAAM;;;;;;;;;;;;kEAEvG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EAAsC,IAAI,OAAO,kBAAkB,CAAC,SAAS;oEAC3F,SAAS;oEACT,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP;;;;;;;;;;;;;;;;;;;;;;;;kDAIN,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,YAAY;oCAChB;oCACA;oCACA;oCACA;iCACD;gCACD,qBACE,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,KAAK;;;;;;kEAC5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAgD,KAAK,KAAK;;;;;;0EACvE,8OAAC;gEAAK,WAAU;0EAAoC,KAAK,IAAI;;;;;;;;;;;;kEAE/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAW,CAAC,sBAAsB,EAC5C,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;;;;;;0EACF,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;0EACC,KAAK,MAAM;;;;;;0EAEd,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAG5C,8OAAC;gDAAI,WAAW,CAAC,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC,gCAAgC,CAAC;0DACrF,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;mCArBZ;;;;;4BA0Bd;;;;;;sCAGF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;sDAEnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;kFAE1B,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;;oFAAiD;oFAAS,KAAK,EAAE;;;;;;;0FAC9E,8OAAC;gFAAE,WAAU;0FAAoC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0EAGlE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAkD,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM;;;;;;kFACzF,8OAAC;wEAAE,WAAU;kFAAoC,KAAK,IAAI;;;;;;;;;;;;;uDAZpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8CAqBzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAG,WAAU;kEAAmD;;;;;;;;;;;;;;;;;sDAGrE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA6B,SAAS,IAAI;;;;;;0FACvD,8OAAC;gFAAE,WAAU;;oFAAwB;oFAAO,SAAS,KAAK;;;;;;;;;;;;;;;;;;;0EAG9D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAA+B,SAAS,MAAM;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;;4EAAyB,SAAS,QAAQ;4EAAC;;;;;;;;;;;;;;uDAZlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB5B", "debugId": null}}]}